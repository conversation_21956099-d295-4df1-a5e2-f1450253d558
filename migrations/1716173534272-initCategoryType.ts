import { CategoryGroup } from 'src/admin/category/category.group';
import { genInsertQuery } from 'src/core/database/gen-insert-query.parse';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitCategoryType1716173534272 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const categories = await queryRunner.query(
      `SELECT * FROM category WHERE "parentId" IS NULL`,
    );

    const existsCategories = [];
    for (const category of categories) {
      existsCategories.push(category.name);
    }
    const data = [];
    for (const item of Object.values(CategoryGroup)) {
      if (!existsCategories.includes(item.name.toUpperCase())) {
        data.push(CategoryGroup[item.name.toUpperCase()]);
      }
    }

    if (data.length > 0) {
      const query = genInsertQuery('category', data);
      await queryRunner.query(query);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
