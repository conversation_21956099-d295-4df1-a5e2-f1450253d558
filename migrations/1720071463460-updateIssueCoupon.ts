import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateIssueCoupon1720071463460 implements MigrationInterface {
  name = 'UpdateIssueCoupon1720071463460';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "issue_coupon" ADD "minLength" integer`,
    );
    await queryRunner.query(
      `ALTER TABLE "issue_coupon" ADD "startNo" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "issue_coupon" ADD "prefix" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "issue_coupon" ADD "suffix" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "issue_coupon" DROP COLUMN "suffix"`);
    await queryRunner.query(`ALTER TABLE "issue_coupon" DROP COLUMN "prefix"`);
    await queryRunner.query(`ALTER TABLE "issue_coupon" DROP COLUMN "startNo"`);
    await queryRunner.query(
      `ALTER TABLE "issue_coupon" DROP COLUMN "minLength"`,
    );
  }
}
