import { CategoryGroup } from 'src/admin/category/category.group';
import { genInsertQuery } from 'src/core/database/gen-insert-query.parse';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitCategory1699672648871 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const query = genInsertQuery('category', Object.values(CategoryGroup));
    await queryRunner.query(query);
  }

  public async down(): Promise<void> {
    // Do nothing
  }
}
