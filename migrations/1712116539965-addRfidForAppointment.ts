import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRfidForAppointment1712116539965 implements MigrationInterface {
  name = 'AddRfidForAppointment1712116539965';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "appointment" ADD "rfidsId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "appointment" ADD CONSTRAINT "FK_3c1add6ac5df073de2789e2b1be" FOREIGN KEY ("rfidsId") REFERENCES "rfid"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "appointment" DROP CONSTRAINT "FK_3c1add6ac5df073de2789e2b1be"`,
    );
    await queryRunner.query(`ALTER TABLE "appointment" DROP COLUMN "rfidsId"`);
  }
}
