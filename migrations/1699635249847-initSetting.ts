import { genUUID } from 'src/core/crypto/crypto.provider';
import { genInsertQuery } from 'src/core/database/gen-insert-query.parse';
import { MigrationInterface, QueryRunner } from 'typeorm';
import { settingType } from 'src/admin/setting/init-setting';

export class InitSetting1699635249847 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const settings = [];
    for (const settingKey of Object.keys(settingType)) {
      for (const index in settingType[settingKey]) {
        const value = settingType[settingKey][index];
        const id = genUUID(`${settingKey}-${value}`, 'Setting');
        const exist = await queryRunner.query(
          `SELECT * FROM setting WHERE id = '${id}'`,
        );

        if (exist.length === 0) {
          settings.push({
            id: genUUID(`${settingKey}-${value}`, 'Setting'),
            type: settingKey,
            name: value,
            order: index,
          });
        }
      }
    }

    const query = genInsertQuery('setting', settings);
    await queryRunner.query(query);
  }

  public async down(): Promise<void> {
    //none
  }
}
