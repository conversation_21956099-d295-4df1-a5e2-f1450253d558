import { MigrationInterface, QueryRunner } from "typeorm";

export class  AddStatusforOrderDetail1732869902500 implements MigrationInterface {
    name = 'AddStatusforOrderDetail1732869902500'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."order_detail_status_enum" AS ENUM('BOOKING', 'ARRIVED', 'REQUEST', 'FACIAL_IPL', 'REQUEST_ARRIVED', 'CANCEL')`);
        await queryRunner.query(`ALTER TABLE "order_detail" ADD "status" "public"."order_detail_status_enum" NOT NULL DEFAULT 'BOOKING'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "order_detail" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."order_detail_status_enum"`);
    }

}
