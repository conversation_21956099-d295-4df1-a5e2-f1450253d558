import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCreditHistory1715237569701 implements MigrationInterface {
  name = 'UpdateCreditHistory1715237569701';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "credit_history" ADD "refundDate" TIMESTAMP WITH TIME ZONE`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "credit_history" DROP COLUMN "refundDate"`);
  }
}
