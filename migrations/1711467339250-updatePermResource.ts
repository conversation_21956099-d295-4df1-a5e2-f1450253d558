import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePermResource1711467339250 implements MigrationInterface {
  name = 'UpdatePermResource1711467339250';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."resource_type_enum" AS ENUM('DEFAULT', 'CUSTOM')`,
    );
    await queryRunner.query(
      `ALTER TABLE "resource" ADD "type" "public"."resource_type_enum" NOT NULL DEFAULT 'DEFAULT'`,
    );
    await queryRunner.query(`ALTER TABLE "resource" ADD "create" boolean`);
    await queryRunner.query(`ALTER TABLE "resource" ADD "read" boolean`);
    await queryRunner.query(`ALTER TABLE "resource" ADD "list" boolean`);
    await queryRunner.query(`ALTER TABLE "resource" ADD "update" boolean`);
    await queryRunner.query(`ALTER TABLE "resource" ADD "delete" boolean`);
    await queryRunner.query(`ALTER TABLE "resource" ADD "print" boolean`);
    await queryRunner.query(`ALTER TABLE "resource" ADD "download" boolean`);
    await queryRunner.query(`ALTER TABLE "permission" ADD "print" boolean`);
    await queryRunner.query(`ALTER TABLE "permission" ADD "download" boolean`);
    await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "parentId"`);
    await queryRunner.query(`ALTER TABLE "resource" ADD "parentId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "permission" DROP CONSTRAINT IF EXISTS "FK_287fe7669c4035bba465728974c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ALTER COLUMN "create" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ALTER COLUMN "read" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ALTER COLUMN "list" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ALTER COLUMN "update" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ALTER COLUMN "delete" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ALTER COLUMN "resourceId" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "role" ALTER COLUMN "code" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "role" ALTER COLUMN "level" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "role" ALTER COLUMN "level" SET DEFAULT '1'`,
    );
    await queryRunner.query(
      `ALTER TABLE "resource" ADD CONSTRAINT "FK_31cb2785f5ff7d71cbc2e4aaa7c" FOREIGN KEY ("parentId") REFERENCES "resource"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ADD CONSTRAINT "FK_287fe7669c4035bba465728974c" FOREIGN KEY ("resourceId") REFERENCES "resource"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "permission" DROP CONSTRAINT "FK_287fe7669c4035bba465728974c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "resource" DROP CONSTRAINT "FK_31cb2785f5ff7d71cbc2e4aaa7c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "role" ALTER COLUMN "level" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "role" ALTER COLUMN "level" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "role" ALTER COLUMN "code" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ALTER COLUMN "resourceId" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ALTER COLUMN "delete" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ALTER COLUMN "update" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ALTER COLUMN "list" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ALTER COLUMN "read" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ALTER COLUMN "create" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ADD CONSTRAINT "FK_287fe7669c4035bba465728974c" FOREIGN KEY ("resourceId") REFERENCES "resource"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "parentId"`);
    await queryRunner.query(
      `ALTER TABLE "resource" ADD "parentId" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "download"`);
    await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "print"`);
    await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "download"`);
    await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "print"`);
    await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "delete"`);
    await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "update"`);
    await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "list"`);
    await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "read"`);
    await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "create"`);
    await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "type"`);
    await queryRunner.query(`DROP TYPE "public"."resource_type_enum"`);
  }
}
