import { MigrationInterface, QueryRunner } from "typeorm";

export class  AddIsPassportHistory1730883850811 implements MigrationInterface {
    name = 'AddIsPassportHistory1730883850811'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "credit_history" ADD "isPassportHistory" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "credit_history" DROP COLUMN "isPassportHistory"`);
    }

}
