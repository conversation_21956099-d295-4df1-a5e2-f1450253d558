import { MigrationInterface, QueryRunner } from "typeorm";

export class  AddQuantityForMembership1730869336261 implements MigrationInterface {
    name = 'AddQuantityForMembership1730869336261'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "customer" ADD "passportUsageLimit" real DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "product" ADD "quantity" real DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product" DROP COLUMN "quantity"`);
        await queryRunner.query(`ALTER TABLE "customer" DROP COLUMN "passportUsageLimit"`);
    }

}
