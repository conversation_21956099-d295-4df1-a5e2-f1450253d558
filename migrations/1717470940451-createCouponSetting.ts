import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateCouponSetting1717470940451 implements MigrationInterface {
  name = 'CreateCouponSetting1717470940451';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "coupon_setting" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "percent" real NOT NULL DEFAULT '0', "statusId" uuid, CONSTRAINT "PK_780b2ff9f870659a3e97aa1ec1b" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a4551403c24d0e43660fcc1232" ON "coupon_setting" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c3347c24fe9adec69c8dc62f34" ON "coupon_setting" ("updated") `,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon_setting" ADD CONSTRAINT "FK_dca2f2d2890f890489675356f38" FOREIGN KEY ("statusId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "coupon_setting" DROP CONSTRAINT "FK_dca2f2d2890f890489675356f38"`,
    );
    await queryRunner.query(`DROP TABLE "coupon_setting"`);
  }
}
