import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTypeForOrder1712138286577 implements MigrationInterface {
  name = 'AddTypeForOrder1712138286577';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."order_ordertype_enum" AS ENUM('others', 'membership', 'food-beverage')`,
    );
    await queryRunner.query(
      `ALTER TABLE "order" ADD "orderType" "public"."order_ordertype_enum" NOT NULL DEFAULT 'others'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "order" DROP COLUMN "orderType"`);
    await queryRunner.query(`DROP TYPE "public"."order_ordertype_enum"`);
  }
}
