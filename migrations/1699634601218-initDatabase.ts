import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitDatabase1699634601218 implements MigrationInterface {
  name = 'InitDatabase1699634601218';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "setting" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "type" character varying NOT NULL, "name" character varying NOT NULL, "order" integer, "value" integer, CONSTRAINT "PK_fcb21187dc6094e24a48f677bed" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5a82e1557167dffd637b2694ec" ON "setting" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2c9fe777c7cb794bb60678e353" ON "setting" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "resource" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "name" character varying NOT NULL, "code" character varying NOT NULL, "order" integer NOT NULL, "parentId" character varying, "statusId" uuid, CONSTRAINT "PK_e2894a5867e06ae2e8889f1173f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_13aa7974487bf577275c826779" ON "resource" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3d808ef101c28011f615dfec7d" ON "resource" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."permission_status_enum" AS ENUM('Active', 'Inactive')`,
    );
    await queryRunner.query(
      `CREATE TABLE "permission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "name" character varying NOT NULL, "code" character varying NOT NULL, "create" boolean NOT NULL, "read" boolean NOT NULL, "list" boolean NOT NULL, "update" boolean NOT NULL, "delete" boolean NOT NULL, "status" "public"."permission_status_enum" NOT NULL, "roleId" uuid, "resourceId" uuid, CONSTRAINT "PK_3b8b97af9d9d8807e41e6f48362" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2139f3b5ad8f7e095679fb50cf" ON "permission" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_167a9d2dbb3667b896642404cc" ON "permission" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "role" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "name" character varying NOT NULL, "code" character varying NOT NULL, "level" integer NOT NULL, "description" text, "statusId" uuid, CONSTRAINT "PK_b36bcfe02fc8de3c57a8b2391c2" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_39861f6656e48cd488a023e227" ON "role" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0fcd8225d86d3bda963629683a" ON "role" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "auth" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "password" character varying, "type" character varying NOT NULL DEFAULT 'SYSTEM', "confirmationToken" character varying, "userId" uuid NOT NULL, CONSTRAINT "PK_7e416cf6172bc5aec04244f6459" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_52bb200ec9c4db636b2ed8bd86" ON "auth" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_536067205638cca7e68e55c5e2" ON "auth" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."token_type_enum" AS ENUM('ACCESS_TOKEN', 'REFRESH_TOKEN', 'PASSWORD_TOKEN')`,
    );
    await queryRunner.query(
      `CREATE TABLE "token" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "tokenString" character varying(256) NOT NULL, "expires" TIMESTAMP NOT NULL, "type" "public"."token_type_enum" NOT NULL, "userId" uuid, CONSTRAINT "PK_82fae97f905930df5d62a702fc9" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2f00aef2888bc6f10559d2d25e" ON "token" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4ba6959764ed464c3373614027" ON "token" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "category" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "name" character varying NOT NULL, "roomQuantity" integer NOT NULL DEFAULT '0', "branchId" uuid, "parentId" uuid, "statusId" uuid, "avatarId" uuid, CONSTRAINT "REL_210935706c5cd6f9a7a805cc02" UNIQUE ("avatarId"), CONSTRAINT "PK_9c4e4a89e3674fc9f382d733f03" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c6d677f0ce77c6242f66a76509" ON "category" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1937f8aac57c07ee599667c685" ON "category" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."product_type_enum" AS ENUM('product', 'service', 'membership', 'food-beverage')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."product_periodunit_enum" AS ENUM('day', 'month', 'year')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."product_credittype_enum" AS ENUM('old_credits', 'credits')`,
    );
    await queryRunner.query(
      `CREATE TABLE "product" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "isOfAllBranch" boolean NOT NULL DEFAULT false, "name" character varying NOT NULL, "cost" real NOT NULL DEFAULT '0', "price" real NOT NULL DEFAULT '0', "profit" real NOT NULL DEFAULT '0', "costForMember" real, "priceForMember" real, "profitForMember" real, "type" "public"."product_type_enum" NOT NULL DEFAULT 'product', "periodUnit" "public"."product_periodunit_enum" NOT NULL DEFAULT 'month', "period" real, "credit" real DEFAULT '0', "creditType" "public"."product_credittype_enum", "isMember" boolean NOT NULL DEFAULT false, "avatarId" uuid, "statusId" uuid, "createdUserId" uuid, "lastUpdatedUserId" uuid, "categoryId" uuid, "durationId" uuid, CONSTRAINT "REL_e7471a79da9279e1f902f2d692" UNIQUE ("avatarId"), CONSTRAINT "PK_bebc9158e480b949565b4dc7a82" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9efdf752ef34afbb5927e601b6" ON "product" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_81dd851479985ba78a9f602140" ON "product" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "issue_coupon" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "isOfAllBranch" boolean NOT NULL DEFAULT false, "issueDate" TIMESTAMP, "issued" integer, "remain" integer, "used" integer, "remark" character varying, "statusId" uuid, "couponId" uuid, CONSTRAINT "PK_9fb9284a0e408b3a591d8d0309a" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6d446229df5422f1ffee6637ef" ON "issue_coupon" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_09a05d7cf1c2e8e84ff2f40b6f" ON "issue_coupon" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."coupon_periodunit_enum" AS ENUM('day', 'month', 'year')`,
    );
    await queryRunner.query(
      `CREATE TABLE "coupon" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "isOfAllBranch" boolean NOT NULL DEFAULT false, "name" character varying NOT NULL, "startDate" TIMESTAMP, "periodUnit" "public"."coupon_periodunit_enum" NOT NULL DEFAULT 'month', "period" integer NOT NULL DEFAULT '0', "discountValue" real NOT NULL DEFAULT '0', "avatarId" uuid, "statusId" uuid, "categoryId" uuid, "issueId" uuid, "discountTypeId" uuid, CONSTRAINT "REL_3b269b5467f9a9c7e4bea95b21" UNIQUE ("avatarId"), CONSTRAINT "PK_fcbe9d72b60eed35f46dc35a682" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_956654d64f49bb48dd490a74cb" ON "coupon" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d9b61d81c5b14a311ad7843dc9" ON "coupon" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "media" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "url" character varying NOT NULL, "key" character varying NOT NULL, "md5" character varying NOT NULL, CONSTRAINT "PK_f4e0fcac36e050de337b670d8bd" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_560a99f8f9095b3a35453c1400" ON "media" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_719c3627e724197bb8bfa83fa1" ON "media" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "user" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "fullname" character varying, "displayName" character varying, "uniqueID" character varying, "phone" character varying, "phoneCode" smallint NOT NULL DEFAULT '65', "username" character varying, "email" character varying, "referralSource" character varying, "birthday" TIMESTAMP, "note" text, "address" text, "description" text, "acceptMarketingNotifi" boolean, "genderId" uuid, "avatarId" uuid, "roleId" uuid, "branchId" uuid, CONSTRAINT "REL_58f5c71eaab331645112cf8cfa" UNIQUE ("avatarId"), CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8ce4c93ba419b56bd82e533724" ON "user" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5904a9d40152f354e4c7b0202f" ON "user" ("updated") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_e12875dfb3b1d92d7d7c5377e2" ON "user" ("email") `,
    );
    await queryRunner.query(
      `CREATE TABLE "day_off" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "startDate" TIMESTAMP NOT NULL, "endDate" TIMESTAMP NOT NULL, "description" character varying, "employeeId" uuid, CONSTRAINT "PK_4ebe4c08c950e3dbc87f0249811" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_90dac1cb8ca4d80352a0f1ce6b" ON "day_off" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_fa1c8e57a91c164b81560aa160" ON "day_off" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "employee" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "fullName" character varying, "displayName" character varying, "birthDay" TIMESTAMP, "phone" character varying, "email" character varying, "address" text, "startDate" TIMESTAMP, "endDate" TIMESTAMP, "employeeCPF" real, "employerCPF" real, "nric" character varying, "calendarColor" character varying, "maritalStatusId" uuid, "genderId" uuid, "salaryTypeId" uuid, "statusId" uuid, "branchId" uuid, CONSTRAINT "PK_3c2bc72f03fd5abbbc5ac169498" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e25c79f9a0bb0cc68b3d360ecd" ON "employee" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c17f84bb360b34b47025812250" ON "employee" ("updated") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_81afb288b526f7e8fed0e4200c" ON "employee" ("phone") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_817d1d427138772d47eca04885" ON "employee" ("email") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "unique_displayname_index" ON "employee" ("branchId", "displayName") WHERE deleted IS NULL`,
    );
    await queryRunner.query(
      `CREATE TABLE "currency" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "name" character varying NOT NULL, "symbol" character varying NOT NULL, "code" character varying NOT NULL, "prefix" boolean NOT NULL DEFAULT false, "enabled" boolean NOT NULL DEFAULT true, "syncedFixedRate" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_3cda65c731a6264f0e444cc9b91" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4aabc0c79491db35ed65ae220a" ON "currency" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ef492e222a0dec96e6d1551159" ON "currency" ("updated") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_77f11186dd58a8d87ad5fff024" ON "currency" ("name") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_723472e41cae44beb0763f4039" ON "currency" ("code") `,
    );
    await queryRunner.query(
      `CREATE TABLE "branch" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "name" character varying, "code" character varying, "phones" json, "order" integer, "address" character varying, "startTime" character varying, "endTime" character varying, "email" character varying, "statusId" uuid, "currencyId" uuid, CONSTRAINT "PK_2e39f426e2faefdaa93c5961976" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c23d8593cc9d70e327fd61cf16" ON "branch" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_15925240dbead9258f3512944a" ON "branch" ("updated") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_6e06693a4255ca9b0a9a366260" ON "branch" ("email") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."credit_setting_credittype_enum" AS ENUM('old_credits', 'credits')`,
    );
    await queryRunner.query(
      `CREATE TABLE "credit_setting" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "price" integer NOT NULL, "name" character varying NOT NULL, "credit" integer NOT NULL, "applyFromTheDate" TIMESTAMP NOT NULL, "creditType" "public"."credit_setting_credittype_enum", "statusId" uuid, CONSTRAINT "PK_ff40c32d54d3ddc2eba1fd8b501" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_680311a4072231d413d8c5ca43" ON "credit_setting" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_66468dc10dbd60e28fd3df9553" ON "credit_setting" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."credit_status_enum" AS ENUM('VALID', 'EXPIRED')`,
    );
    await queryRunner.query(
      `CREATE TABLE "credit" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "issueDate" TIMESTAMP NOT NULL, "expiryDate" TIMESTAMP NOT NULL, "total" real, "creditBalance" real, "status" "public"."credit_status_enum" NOT NULL DEFAULT 'VALID', "creditSettingId" uuid, "branchId" uuid, "customerId" uuid, CONSTRAINT "PK_c98add8e192ded18b69c3e345a5" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ddd2665fdc3ae95a9ea09b96f6" ON "credit" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_745d2e7dbf9dc318551a66c633" ON "credit" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "customer" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "code" bigint NOT NULL, "firstName" character varying, "lastName" character varying, "nric" character varying, "phone" character varying, "email" character varying, "address" text, "remark" text, "membershipNo" character varying, "expiryDate" TIMESTAMP, "firstVisit" TIMESTAMP, "birthDay" TIMESTAMP, "referralSource" character varying, "genderId" uuid, "nationalityId" uuid, "avatarId" uuid, "statusId" uuid, CONSTRAINT "REL_7a9d34477964f14ecac6e9fe03" UNIQUE ("avatarId"), CONSTRAINT "PK_a7a13f4cacb744524e44dfdad32" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3096cd9b6906e855d99ccb3d8a" ON "customer" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6d8aa7b020d850b5a9ff69a5d3" ON "customer" ("updated") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_dc5912edb8a02da5e0b9b57c35" ON "customer" ("phone") WHERE deleted IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_4c58be1b83ae68eece02e51964" ON "customer" ("email") WHERE deleted IS NULL`,
    );
    await queryRunner.query(
      `CREATE TABLE "order_detail" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "note" character varying, "quantity" integer NOT NULL DEFAULT '1', "discount" integer NOT NULL DEFAULT '0', "price" real NOT NULL DEFAULT '0', "startTime" TIMESTAMP, "endTime" TIMESTAMP, "orderId" uuid, "productId" uuid, "durationId" uuid, CONSTRAINT "PK_0afbab1fa98e2fb0be8e74f6b38" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_fcdf4bcfc9f48bf97b35b1ee08" ON "order_detail" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a4f517cc0875b3a6898f0adbe6" ON "order_detail" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "payment_method" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "name" character varying NOT NULL, "code" character varying, "order" integer NOT NULL DEFAULT '0', "statusId" uuid, CONSTRAINT "PK_7744c2b2dd932c9cf42f2b9bc3a" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9d38a9f610c75340989191eafa" ON "payment_method" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_77da6bf97902c0264cd9912fed" ON "payment_method" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "invoice_payment_method" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "paid" real, "statusId" uuid, "paymentMethodId" uuid, "branchId" uuid, CONSTRAINT "PK_cab012e6800e25372deeade2603" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0f0de44012385a793043f27022" ON "invoice_payment_method" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a76bea96dd8633903027d3c741" ON "invoice_payment_method" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."invoice_status_enum" AS ENUM('PART_PAID', 'PAID', 'UNPAID')`,
    );
    await queryRunner.query(
      `CREATE TABLE "invoice" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "code" character varying NOT NULL, "date" TIMESTAMP NOT NULL, "total" real DEFAULT '0', "paid" real DEFAULT '0', "unPaid" real DEFAULT '0', "status" "public"."invoice_status_enum" NOT NULL DEFAULT 'UNPAID', "discount" integer NOT NULL DEFAULT '0', "tax" real DEFAULT '0', "totalBeforeTax" real DEFAULT '0', "note" character varying, "branchId" uuid, "customerId" uuid, "appointmentId" uuid, "parentInvoiceId" uuid, "referralId" uuid, CONSTRAINT "PK_15d25c200d9bcd8a33f698daf18" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c3f0c58d8ac21386c4510fc416" ON "invoice" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6fac6f69d1ab24d7c35a8a04a2" ON "invoice" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "order" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "code" bigint NOT NULL, "note" character varying, "discount" integer NOT NULL DEFAULT '0', "isDraft" boolean NOT NULL DEFAULT false, "isPrinted" boolean NOT NULL DEFAULT false, "tax" real DEFAULT '0', "total" real DEFAULT '0', "totalBeforeTax" real DEFAULT '0', "discountProductId" character varying, "discountMoney" real DEFAULT '0', "couponCode" character varying, "payload" jsonb, "couponName" character varying, "appointmentId" uuid, "invoiceId" uuid, "statusId" uuid, "employeeId" uuid, "branchId" uuid, CONSTRAINT "PK_1031171c13130102495201e3e20" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b12d7bd71915765f5c32d59039" ON "order" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_993d99dbc2fc07869d3e8adbef" ON "order" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."appointment_status_enum" AS ENUM('NEW', 'STARTED', 'BOOKING', 'COMPLETED', 'CONFIRM', 'CANCEL', 'NOSHOW')`,
    );
    await queryRunner.query(
      `CREATE TABLE "appointment" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "startTime" TIMESTAMP NOT NULL, "endTime" TIMESTAMP, "checkIn" TIMESTAMP, "checkOut" TIMESTAMP, "status" "public"."appointment_status_enum" NOT NULL DEFAULT 'NEW', "note" character varying, "rfid" character varying, "customerId" uuid, "branchId" uuid, CONSTRAINT "PK_e8be1a53027415e709ce8a2db74" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e8df3f8aafefdfa1edbe1c7004" ON "appointment" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_76142cf989a11d0a42999875a8" ON "appointment" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "credit_history" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "detail" jsonb NOT NULL, "paid" real NOT NULL, "creditId" uuid, CONSTRAINT "PK_1f23079c40e17baba72a8f83d41" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_43ad2262f280b575f19433d3f3" ON "credit_history" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2be7048f2a47a6a9b96f4b448e" ON "credit_history" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "coupon_item" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "isOfAllBranch" boolean NOT NULL DEFAULT false, "code" character varying NOT NULL, "email" character varying, "isUsed" boolean NOT NULL DEFAULT false, "statusId" uuid, "issueCouponId" uuid, CONSTRAINT "PK_434114844d64fb92efa5bd01f36" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9044d1fd4c0db47acc62d4857a" ON "coupon_item" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c3fac10d58495bcc90f2d195f5" ON "coupon_item" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "employee_setting" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "product" numeric NOT NULL DEFAULT '0', "service" numeric NOT NULL DEFAULT '0', "membership" numeric NOT NULL DEFAULT '0', "salary" numeric NOT NULL DEFAULT '0', "ot" numeric NOT NULL DEFAULT '0', "salaryUnitId" uuid, "otUnitId" uuid, CONSTRAINT "PK_d98eee0375560c6dda8c6e8be72" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_274eed0abe91ff762c43f0e30a" ON "employee_setting" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1c737f5ad1a11f2c12af83cf74" ON "employee_setting" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "product_assignment" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "name" character varying NOT NULL, "cost" real NOT NULL, "price" real NOT NULL, "avatar" character varying, "refProductId" uuid, "customerId" uuid, CONSTRAINT "PK_9b61536ff8aaf9313ed11778a50" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_099193744c62b9ea338c5ea3c8" ON "product_assignment" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1ad445cb6a99f9b05cbc690921" ON "product_assignment" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "rfid" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "serialCode" character varying NOT NULL, "tagId" bigint NOT NULL, "token" character varying, "lockerNumber" integer NOT NULL, "groupId" uuid, "branchId" uuid, "appointmentId" uuid, "statusId" uuid, CONSTRAINT "PK_e876a8050948eaa61080db68ebc" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d9e982dcb81446f42f02ed91f8" ON "rfid" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a79bcb47fc937a5a2673d8d4c8" ON "rfid" ("updated") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_ec7d82d85b685910c09e827484" ON "rfid" ("serialCode") WHERE deleted IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "unique_locker_index" ON "rfid" ("branchId", "groupId", "lockerNumber") WHERE deleted IS NULL`,
    );
    await queryRunner.query(
      `CREATE TABLE "user_session" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "login" TIMESTAMP, "logout" TIMESTAMP, "tokenString" character varying(256) NOT NULL, "roleId" uuid, "userId" uuid, CONSTRAINT "PK_adf3b49590842ac3cf54cac451a" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_546d80c51083a22d66917a128c" ON "user_session" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_eb9c6f8d90fe0fad4997626e23" ON "user_session" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "virtual_working_hour" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "dayOfWeek" smallint NOT NULL, "startDate" TIMESTAMP WITH TIME ZONE NOT NULL, "endDate" TIMESTAMP WITH TIME ZONE, "employeeId" uuid, "workingHourId" uuid, "branchId" uuid, CONSTRAINT "UQ_e28bc21b742c34cabc65ba168b1" UNIQUE ("startDate", "branchId", "employeeId"), CONSTRAINT "REL_91a432cce58679a89516f8e8bd" UNIQUE ("workingHourId"), CONSTRAINT "PK_46a63bab37e0bf3d426beb78ffd" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6519372a798da55d8f28d52e6a" ON "virtual_working_hour" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_da0bb6687f95cf849c314979bb" ON "virtual_working_hour" ("updated") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_27636b771f9230735b3f06f7a5" ON "virtual_working_hour" ("startDate") `,
    );
    await queryRunner.query(
      `CREATE TABLE "working_hour" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "date" TIMESTAMP WITH TIME ZONE NOT NULL, "dayOfWeek" smallint NOT NULL, "employeeId" uuid, "branchId" uuid, CONSTRAINT "UQ_24f82575f9a621d57b85f0aeaa4" UNIQUE ("date", "branchId", "employeeId"), CONSTRAINT "PK_227995aef58f0f198f79ad42203" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_127646eadf8b45eeba37142b62" ON "working_hour" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_80beab664074f9e56a1703b8b5" ON "working_hour" ("updated") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3aacd5d11ef9eb99c119c08b97" ON "working_hour" ("date") `,
    );
    await queryRunner.query(
      `CREATE TABLE "wh_timework" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "workingHourId" uuid, "virtualWorkingHourId" uuid, CONSTRAINT "PK_1ea20070b20e18be8409c84e29f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b051acb2e65df6e8dff37b8889" ON "wh_timework" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_07d83526947be4a3ffab53e154" ON "wh_timework" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "wh_timer" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "hour" smallint NOT NULL, "minute" smallint NOT NULL, "refStartId" uuid, "refEndId" uuid, CONSTRAINT "REL_bd34e812dab4b9962ff4c001c3" UNIQUE ("refStartId"), CONSTRAINT "REL_2795b4681d08e482fae7cb3354" UNIQUE ("refEndId"), CONSTRAINT "PK_1adb29d9d35183e14f4191c648e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a89c463d7a1f0b8502862d6348" ON "wh_timer" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2c4a6f88a5b80690c8c830a110" ON "wh_timer" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."commission_logs_type_enum" AS ENUM('product', 'service', 'membership', 'food-beverage')`,
    );
    await queryRunner.query(
      `CREATE TABLE "commission_logs" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "quantity" integer NOT NULL DEFAULT '1', "price" real NOT NULL DEFAULT '0', "type" "public"."commission_logs_type_enum" NOT NULL DEFAULT 'product', "productId" uuid, "employeeId" uuid, "invoiceId" uuid, "userId" uuid, CONSTRAINT "PK_094089a11e1fdf055310e297af5" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_71a441244c03dd7b3e828664a4" ON "commission_logs" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f4a92caf46092c4888b22f9696" ON "commission_logs" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "commission_product_setting" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "employee" integer NOT NULL DEFAULT '15', "frontdesk" integer NOT NULL DEFAULT '10', "fBStaff" integer NOT NULL DEFAULT '10', "productId" uuid, CONSTRAINT "PK_4929168ee72cf8399eb0fe76925" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3ed421766f31f5d675fe8b7f21" ON "commission_product_setting" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_991298ddc3b16c90eb0f6f942e" ON "commission_product_setting" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "payslip_employee_setting" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "basicSalary" integer NOT NULL DEFAULT '20', "oTPay" integer NOT NULL DEFAULT '20', "allowance" integer NOT NULL DEFAULT '200', "employeeId" uuid, CONSTRAINT "PK_d4b9e1465a1a6a54f3c124f4ddf" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2da56ce222f4b858d1079dd6c9" ON "payslip_employee_setting" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2b0ccdff5d9364e848394573c3" ON "payslip_employee_setting" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "commission_setting" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "employee" integer NOT NULL DEFAULT '15', "frontdesk" integer NOT NULL DEFAULT '10', "fBStaff" integer NOT NULL DEFAULT '10', CONSTRAINT "PK_8ad5e203a8a11052388b181a461" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8ca5e43cb2d347a8db89b5cd2c" ON "commission_setting" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_127ed303c6cf78d1822140ac02" ON "commission_setting" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "payslip_setting" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "basicSalary" integer NOT NULL DEFAULT '20', "oTPay" integer NOT NULL DEFAULT '20', "allowance" integer NOT NULL DEFAULT '200', CONSTRAINT "PK_fd12d7c060084ad6e1485ac0f4d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e345d9be4773a56ba168fc63d9" ON "payslip_setting" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_439948e1ec6b436cc8211c332b" ON "payslip_setting" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "product_branches_branch" ("productId" uuid NOT NULL, "branchId" uuid NOT NULL, CONSTRAINT "PK_0af388475a2d70480d259e2923c" PRIMARY KEY ("productId", "branchId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_993adea15f9cc8e54dfb327656" ON "product_branches_branch" ("productId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_972cdbceab574cf28067c7a8a8" ON "product_branches_branch" ("branchId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "issue_coupon_branches_branch" ("issueCouponId" uuid NOT NULL, "branchId" uuid NOT NULL, CONSTRAINT "PK_150575d52938e6a7e0435c0ac2f" PRIMARY KEY ("issueCouponId", "branchId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_384ffe299c0cb14ef5d8150f70" ON "issue_coupon_branches_branch" ("issueCouponId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_eeb7d891e12f0bd300112ba681" ON "issue_coupon_branches_branch" ("branchId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "coupon_branches_branch" ("couponId" uuid NOT NULL, "branchId" uuid NOT NULL, CONSTRAINT "PK_2528ed274ba3b7c86fc31800304" PRIMARY KEY ("couponId", "branchId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a04e8eea563d3ff125b7849837" ON "coupon_branches_branch" ("couponId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b289b73728f01328cb0eeea772" ON "coupon_branches_branch" ("branchId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "coupon_products_product" ("couponId" uuid NOT NULL, "productId" uuid NOT NULL, CONSTRAINT "PK_0bd6a4b75fc235663fff0cdfc2b" PRIMARY KEY ("couponId", "productId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_006cd586575d86f4705186b869" ON "coupon_products_product" ("couponId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_bdd320620afd063015fa0976ae" ON "coupon_products_product" ("productId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "employee_services_product" ("employeeId" uuid NOT NULL, "productId" uuid NOT NULL, CONSTRAINT "PK_1c338f1a243c4f7295cf66d4cbd" PRIMARY KEY ("employeeId", "productId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d6fec1b6e87cdb13d13c2239c9" ON "employee_services_product" ("employeeId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_73ed91f67eb1a91d998f1afd2d" ON "employee_services_product" ("productId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "customer_preferreds_employee" ("customerId" uuid NOT NULL, "employeeId" uuid NOT NULL, CONSTRAINT "PK_b7ea5aa44e905d7a24182aac676" PRIMARY KEY ("customerId", "employeeId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b34aadd347d58a84d54f475e84" ON "customer_preferreds_employee" ("customerId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a6d3603177be24f8e05bc9944e" ON "customer_preferreds_employee" ("employeeId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "order_detail_employees_employee" ("orderDetailId" uuid NOT NULL, "employeeId" uuid NOT NULL, CONSTRAINT "PK_f27c749c9bf7e0c6d518772fd46" PRIMARY KEY ("orderDetailId", "employeeId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d5c7514f0b1e1a81c0566804ea" ON "order_detail_employees_employee" ("orderDetailId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d46cfabfd3c7534650f8b6db13" ON "order_detail_employees_employee" ("employeeId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "invoice_invoice_payments_invoice_payment_method" ("invoiceId" uuid NOT NULL, "invoicePaymentMethodId" uuid NOT NULL, CONSTRAINT "PK_89499115f2aacb679e2e1bd57c4" PRIMARY KEY ("invoiceId", "invoicePaymentMethodId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7e3f4f719536ce27808ab0e053" ON "invoice_invoice_payments_invoice_payment_method" ("invoiceId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0011c8b1a9261827b02b888745" ON "invoice_invoice_payments_invoice_payment_method" ("invoicePaymentMethodId") `,
    );
    await queryRunner.query(
      `CREATE TABLE "coupon_item_branches_branch" ("couponItemId" uuid NOT NULL, "branchId" uuid NOT NULL, CONSTRAINT "PK_f354defc10e032ad4db21607581" PRIMARY KEY ("couponItemId", "branchId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2200b366bcc10e361e46e797c1" ON "coupon_item_branches_branch" ("couponItemId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_69cf38c4f7e80d93f4ca15d6db" ON "coupon_item_branches_branch" ("branchId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "resource" ADD CONSTRAINT "FK_fe5891b7e529ab339018bd9d58d" FOREIGN KEY ("statusId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ADD CONSTRAINT "FK_cdb4db95384a1cf7a837c4c683e" FOREIGN KEY ("roleId") REFERENCES "role"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ADD CONSTRAINT "FK_287fe7669c4035bba465728974c" FOREIGN KEY ("resourceId") REFERENCES "resource"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "role" ADD CONSTRAINT "FK_3fc73ac1307382a3b92e79b4886" FOREIGN KEY ("statusId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "auth" ADD CONSTRAINT "FK_373ead146f110f04dad60848154" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "token" ADD CONSTRAINT "FK_94f168faad896c0786646fa3d4a" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "category" ADD CONSTRAINT "FK_56be20dbbf4499e02a7dec2260b" FOREIGN KEY ("branchId") REFERENCES "branch"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "category" ADD CONSTRAINT "FK_d5456fd7e4c4866fec8ada1fa10" FOREIGN KEY ("parentId") REFERENCES "category"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "category" ADD CONSTRAINT "FK_ada9c63b1bfb31bd904b3776c89" FOREIGN KEY ("statusId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "category" ADD CONSTRAINT "FK_210935706c5cd6f9a7a805cc029" FOREIGN KEY ("avatarId") REFERENCES "media"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ADD CONSTRAINT "FK_e7471a79da9279e1f902f2d692f" FOREIGN KEY ("avatarId") REFERENCES "media"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ADD CONSTRAINT "FK_9ec2c7792817b56a3533ca1d7aa" FOREIGN KEY ("statusId") REFERENCES "setting"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ADD CONSTRAINT "FK_70962d60ddff098ed14045974a9" FOREIGN KEY ("createdUserId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ADD CONSTRAINT "FK_034cfbbf7375507ddc3f7758491" FOREIGN KEY ("lastUpdatedUserId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ADD CONSTRAINT "FK_ff0c0301a95e517153df97f6812" FOREIGN KEY ("categoryId") REFERENCES "category"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ADD CONSTRAINT "FK_c35bbcb8009ac480b4700a94167" FOREIGN KEY ("durationId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "issue_coupon" ADD CONSTRAINT "FK_1121836749f9d02a840932d43c0" FOREIGN KEY ("statusId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "issue_coupon" ADD CONSTRAINT "FK_b298b696684276b7657616136fe" FOREIGN KEY ("couponId") REFERENCES "coupon"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon" ADD CONSTRAINT "FK_3b269b5467f9a9c7e4bea95b215" FOREIGN KEY ("avatarId") REFERENCES "media"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon" ADD CONSTRAINT "FK_93baceb995f0bcf98349b6fe259" FOREIGN KEY ("statusId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon" ADD CONSTRAINT "FK_7ac2227010228ec0f7d5280ca66" FOREIGN KEY ("categoryId") REFERENCES "category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon" ADD CONSTRAINT "FK_9aebd8e073c160515663f9b92bd" FOREIGN KEY ("issueId") REFERENCES "issue_coupon"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon" ADD CONSTRAINT "FK_a85d0722f3f6a522ff0e51808fa" FOREIGN KEY ("discountTypeId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "FK_6273b1aa12d5d17f8e1284200be" FOREIGN KEY ("genderId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "FK_58f5c71eaab331645112cf8cfa5" FOREIGN KEY ("avatarId") REFERENCES "media"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "FK_c28e52f758e7bbc53828db92194" FOREIGN KEY ("roleId") REFERENCES "role"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "FK_8b17d5d91bf27d0a33fb80ade8f" FOREIGN KEY ("branchId") REFERENCES "branch"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "day_off" ADD CONSTRAINT "FK_92ca44072861a97e6ce57335b8a" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee" ADD CONSTRAINT "FK_67d2975a3e5e140720f86eb1183" FOREIGN KEY ("maritalStatusId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee" ADD CONSTRAINT "FK_8638b68d9b366f6405fa8262161" FOREIGN KEY ("genderId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee" ADD CONSTRAINT "FK_1d63c8a9288d42747b5c5e20f7e" FOREIGN KEY ("salaryTypeId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee" ADD CONSTRAINT "FK_b6c7380bac45e746176960400aa" FOREIGN KEY ("statusId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee" ADD CONSTRAINT "FK_c36b6dc182259c56ee8c1cfecb3" FOREIGN KEY ("branchId") REFERENCES "branch"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "branch" ADD CONSTRAINT "FK_34f2d5f869b9d1270083058d601" FOREIGN KEY ("statusId") REFERENCES "setting"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "branch" ADD CONSTRAINT "FK_cb76541b958bf2407aac031fe7a" FOREIGN KEY ("currencyId") REFERENCES "currency"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "credit_setting" ADD CONSTRAINT "FK_cc32204457a6dd1906075cf54da" FOREIGN KEY ("statusId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "credit" ADD CONSTRAINT "FK_c3085ff7ae2710d8c760e67257c" FOREIGN KEY ("creditSettingId") REFERENCES "credit_setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "credit" ADD CONSTRAINT "FK_b141ee18961d3c62109fde45c08" FOREIGN KEY ("branchId") REFERENCES "branch"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "credit" ADD CONSTRAINT "FK_af9d0a0c166ec5affe700acc842" FOREIGN KEY ("customerId") REFERENCES "customer"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "customer" ADD CONSTRAINT "FK_fd885e75fc93121c9f7c4258259" FOREIGN KEY ("genderId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "customer" ADD CONSTRAINT "FK_62b279831e2dc8db00f9cdd7344" FOREIGN KEY ("nationalityId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "customer" ADD CONSTRAINT "FK_7a9d34477964f14ecac6e9fe037" FOREIGN KEY ("avatarId") REFERENCES "media"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "customer" ADD CONSTRAINT "FK_f28cf7cf65a7bf745e49686610a" FOREIGN KEY ("statusId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_detail" ADD CONSTRAINT "FK_88850b85b38a8a2ded17a1f5369" FOREIGN KEY ("orderId") REFERENCES "order"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_detail" ADD CONSTRAINT "FK_a3647bd11aed3cf968c9ce9b835" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_detail" ADD CONSTRAINT "FK_6731225d3f542b2b5986d258286" FOREIGN KEY ("durationId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "payment_method" ADD CONSTRAINT "FK_bb8709c989ede47fe1c92849f3b" FOREIGN KEY ("statusId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice_payment_method" ADD CONSTRAINT "FK_b774c8d51141b71c7d9cd426808" FOREIGN KEY ("statusId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice_payment_method" ADD CONSTRAINT "FK_6cab39113c69858466d15fdb91f" FOREIGN KEY ("paymentMethodId") REFERENCES "payment_method"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice_payment_method" ADD CONSTRAINT "FK_5291a91ba840453c5d6c3552ca1" FOREIGN KEY ("branchId") REFERENCES "branch"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD CONSTRAINT "FK_f6757552bd0776859af91c222ca" FOREIGN KEY ("branchId") REFERENCES "branch"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD CONSTRAINT "FK_925aa26ea12c28a6adb614445ee" FOREIGN KEY ("customerId") REFERENCES "customer"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD CONSTRAINT "FK_150d4d66fbf46ada8965e14294f" FOREIGN KEY ("appointmentId") REFERENCES "appointment"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD CONSTRAINT "FK_17034865ca60a8922ae9d163b58" FOREIGN KEY ("parentInvoiceId") REFERENCES "invoice"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" ADD CONSTRAINT "FK_1b7679b9b3b5199c3b748f98bc6" FOREIGN KEY ("referralId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "order" ADD CONSTRAINT "FK_28c738460931ccd500e37e83586" FOREIGN KEY ("appointmentId") REFERENCES "appointment"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "order" ADD CONSTRAINT "FK_a73f2bf30b1471eeeaafafd425f" FOREIGN KEY ("invoiceId") REFERENCES "invoice"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "order" ADD CONSTRAINT "FK_3b6667bfe775fa39753ca6af2dc" FOREIGN KEY ("statusId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "order" ADD CONSTRAINT "FK_9b451675cd9fe227a0dd5120e53" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "order" ADD CONSTRAINT "FK_9d915a5cee9e0cfdf0d7fc3c30a" FOREIGN KEY ("branchId") REFERENCES "branch"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "appointment" ADD CONSTRAINT "FK_c048c6004b69354f46183f93a85" FOREIGN KEY ("customerId") REFERENCES "customer"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "appointment" ADD CONSTRAINT "FK_54e83867f2699998a25aa23e6ba" FOREIGN KEY ("branchId") REFERENCES "branch"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "credit_history" ADD CONSTRAINT "FK_cc2ab1e4f7059bcb92f95e9ea89" FOREIGN KEY ("creditId") REFERENCES "credit"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon_item" ADD CONSTRAINT "FK_55293e740c1b3eb7e262feed45c" FOREIGN KEY ("statusId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon_item" ADD CONSTRAINT "FK_bea54f58ca98b197d23a3418faa" FOREIGN KEY ("issueCouponId") REFERENCES "issue_coupon"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee_setting" ADD CONSTRAINT "FK_a5e9dd8f33344aff873630cc396" FOREIGN KEY ("salaryUnitId") REFERENCES "setting"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee_setting" ADD CONSTRAINT "FK_4e1eca151ff371c3ef967d4d204" FOREIGN KEY ("otUnitId") REFERENCES "setting"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_assignment" ADD CONSTRAINT "FK_7431e218b11a78f5c87d346510d" FOREIGN KEY ("refProductId") REFERENCES "product"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_assignment" ADD CONSTRAINT "FK_8fc4a33dcac4a22e62bebe02540" FOREIGN KEY ("customerId") REFERENCES "customer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "rfid" ADD CONSTRAINT "FK_d56d3fb3abc12fac62a6b2ea0b8" FOREIGN KEY ("groupId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "rfid" ADD CONSTRAINT "FK_b75dde2ef71a795b0492a187bf9" FOREIGN KEY ("branchId") REFERENCES "branch"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "rfid" ADD CONSTRAINT "FK_a0bd9fbe29f3283633cb90e3477" FOREIGN KEY ("appointmentId") REFERENCES "appointment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "rfid" ADD CONSTRAINT "FK_32e2f4c2c465e5da47eb66ef068" FOREIGN KEY ("statusId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_session" ADD CONSTRAINT "FK_a7ec9ee280c3f8b173a188d31c6" FOREIGN KEY ("roleId") REFERENCES "role"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_session" ADD CONSTRAINT "FK_b5eb7aa08382591e7c2d1244fe5" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "virtual_working_hour" ADD CONSTRAINT "FK_806cdbd89dcc954047ce2a23506" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "virtual_working_hour" ADD CONSTRAINT "FK_91a432cce58679a89516f8e8bd7" FOREIGN KEY ("workingHourId") REFERENCES "working_hour"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "virtual_working_hour" ADD CONSTRAINT "FK_86c1ec438d54ea718d08646d743" FOREIGN KEY ("branchId") REFERENCES "branch"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "working_hour" ADD CONSTRAINT "FK_4b16d7800cd4cb53c9bcf26b51d" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "working_hour" ADD CONSTRAINT "FK_4f36a1efa1e7b12024950e57492" FOREIGN KEY ("branchId") REFERENCES "branch"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "wh_timework" ADD CONSTRAINT "FK_a05522a3e1cc011745cc1895afe" FOREIGN KEY ("workingHourId") REFERENCES "working_hour"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "wh_timework" ADD CONSTRAINT "FK_150b362f9dbde682be9b36364a2" FOREIGN KEY ("virtualWorkingHourId") REFERENCES "virtual_working_hour"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "wh_timer" ADD CONSTRAINT "FK_bd34e812dab4b9962ff4c001c30" FOREIGN KEY ("refStartId") REFERENCES "wh_timework"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "wh_timer" ADD CONSTRAINT "FK_2795b4681d08e482fae7cb33547" FOREIGN KEY ("refEndId") REFERENCES "wh_timework"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" ADD CONSTRAINT "FK_b22ae163ff86e328cd557b92f48" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" ADD CONSTRAINT "FK_c98b1b61a6ad003a99b9304a36d" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" ADD CONSTRAINT "FK_b63dea7c3e394f9b9345ca783b8" FOREIGN KEY ("invoiceId") REFERENCES "invoice"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" ADD CONSTRAINT "FK_736024dd461604f1ac497d8e9f9" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_product_setting" ADD CONSTRAINT "FK_cfa62c16b5ff40357db8e5b9eb3" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "payslip_employee_setting" ADD CONSTRAINT "FK_41de2fb3dfbfd49e827ef01467b" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_branches_branch" ADD CONSTRAINT "FK_993adea15f9cc8e54dfb3276560" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_branches_branch" ADD CONSTRAINT "FK_972cdbceab574cf28067c7a8a8c" FOREIGN KEY ("branchId") REFERENCES "branch"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "issue_coupon_branches_branch" ADD CONSTRAINT "FK_384ffe299c0cb14ef5d8150f70a" FOREIGN KEY ("issueCouponId") REFERENCES "issue_coupon"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "issue_coupon_branches_branch" ADD CONSTRAINT "FK_eeb7d891e12f0bd300112ba6819" FOREIGN KEY ("branchId") REFERENCES "branch"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon_branches_branch" ADD CONSTRAINT "FK_a04e8eea563d3ff125b7849837b" FOREIGN KEY ("couponId") REFERENCES "coupon"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon_branches_branch" ADD CONSTRAINT "FK_b289b73728f01328cb0eeea772d" FOREIGN KEY ("branchId") REFERENCES "branch"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon_products_product" ADD CONSTRAINT "FK_006cd586575d86f4705186b869a" FOREIGN KEY ("couponId") REFERENCES "coupon"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon_products_product" ADD CONSTRAINT "FK_bdd320620afd063015fa0976ae1" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee_services_product" ADD CONSTRAINT "FK_d6fec1b6e87cdb13d13c2239c90" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee_services_product" ADD CONSTRAINT "FK_73ed91f67eb1a91d998f1afd2d3" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "customer_preferreds_employee" ADD CONSTRAINT "FK_b34aadd347d58a84d54f475e846" FOREIGN KEY ("customerId") REFERENCES "customer"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "customer_preferreds_employee" ADD CONSTRAINT "FK_a6d3603177be24f8e05bc9944e6" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_detail_employees_employee" ADD CONSTRAINT "FK_d5c7514f0b1e1a81c0566804eaa" FOREIGN KEY ("orderDetailId") REFERENCES "order_detail"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_detail_employees_employee" ADD CONSTRAINT "FK_d46cfabfd3c7534650f8b6db133" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice_invoice_payments_invoice_payment_method" ADD CONSTRAINT "FK_7e3f4f719536ce27808ab0e0536" FOREIGN KEY ("invoiceId") REFERENCES "invoice"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice_invoice_payments_invoice_payment_method" ADD CONSTRAINT "FK_0011c8b1a9261827b02b8887454" FOREIGN KEY ("invoicePaymentMethodId") REFERENCES "invoice_payment_method"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon_item_branches_branch" ADD CONSTRAINT "FK_2200b366bcc10e361e46e797c17" FOREIGN KEY ("couponItemId") REFERENCES "coupon_item"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon_item_branches_branch" ADD CONSTRAINT "FK_69cf38c4f7e80d93f4ca15d6db5" FOREIGN KEY ("branchId") REFERENCES "branch"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "coupon_item_branches_branch" DROP CONSTRAINT "FK_69cf38c4f7e80d93f4ca15d6db5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon_item_branches_branch" DROP CONSTRAINT "FK_2200b366bcc10e361e46e797c17"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice_invoice_payments_invoice_payment_method" DROP CONSTRAINT "FK_0011c8b1a9261827b02b8887454"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice_invoice_payments_invoice_payment_method" DROP CONSTRAINT "FK_7e3f4f719536ce27808ab0e0536"`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_detail_employees_employee" DROP CONSTRAINT "FK_d46cfabfd3c7534650f8b6db133"`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_detail_employees_employee" DROP CONSTRAINT "FK_d5c7514f0b1e1a81c0566804eaa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "customer_preferreds_employee" DROP CONSTRAINT "FK_a6d3603177be24f8e05bc9944e6"`,
    );
    await queryRunner.query(
      `ALTER TABLE "customer_preferreds_employee" DROP CONSTRAINT "FK_b34aadd347d58a84d54f475e846"`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee_services_product" DROP CONSTRAINT "FK_73ed91f67eb1a91d998f1afd2d3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee_services_product" DROP CONSTRAINT "FK_d6fec1b6e87cdb13d13c2239c90"`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon_products_product" DROP CONSTRAINT "FK_bdd320620afd063015fa0976ae1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon_products_product" DROP CONSTRAINT "FK_006cd586575d86f4705186b869a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon_branches_branch" DROP CONSTRAINT "FK_b289b73728f01328cb0eeea772d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon_branches_branch" DROP CONSTRAINT "FK_a04e8eea563d3ff125b7849837b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "issue_coupon_branches_branch" DROP CONSTRAINT "FK_eeb7d891e12f0bd300112ba6819"`,
    );
    await queryRunner.query(
      `ALTER TABLE "issue_coupon_branches_branch" DROP CONSTRAINT "FK_384ffe299c0cb14ef5d8150f70a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_branches_branch" DROP CONSTRAINT "FK_972cdbceab574cf28067c7a8a8c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_branches_branch" DROP CONSTRAINT "FK_993adea15f9cc8e54dfb3276560"`,
    );
    await queryRunner.query(
      `ALTER TABLE "payslip_employee_setting" DROP CONSTRAINT "FK_41de2fb3dfbfd49e827ef01467b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_product_setting" DROP CONSTRAINT "FK_cfa62c16b5ff40357db8e5b9eb3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" DROP CONSTRAINT "FK_736024dd461604f1ac497d8e9f9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" DROP CONSTRAINT "FK_b63dea7c3e394f9b9345ca783b8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" DROP CONSTRAINT "FK_c98b1b61a6ad003a99b9304a36d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" DROP CONSTRAINT "FK_b22ae163ff86e328cd557b92f48"`,
    );
    await queryRunner.query(
      `ALTER TABLE "wh_timer" DROP CONSTRAINT "FK_2795b4681d08e482fae7cb33547"`,
    );
    await queryRunner.query(
      `ALTER TABLE "wh_timer" DROP CONSTRAINT "FK_bd34e812dab4b9962ff4c001c30"`,
    );
    await queryRunner.query(
      `ALTER TABLE "wh_timework" DROP CONSTRAINT "FK_150b362f9dbde682be9b36364a2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "wh_timework" DROP CONSTRAINT "FK_a05522a3e1cc011745cc1895afe"`,
    );
    await queryRunner.query(
      `ALTER TABLE "working_hour" DROP CONSTRAINT "FK_4f36a1efa1e7b12024950e57492"`,
    );
    await queryRunner.query(
      `ALTER TABLE "working_hour" DROP CONSTRAINT "FK_4b16d7800cd4cb53c9bcf26b51d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "virtual_working_hour" DROP CONSTRAINT "FK_86c1ec438d54ea718d08646d743"`,
    );
    await queryRunner.query(
      `ALTER TABLE "virtual_working_hour" DROP CONSTRAINT "FK_91a432cce58679a89516f8e8bd7"`,
    );
    await queryRunner.query(
      `ALTER TABLE "virtual_working_hour" DROP CONSTRAINT "FK_806cdbd89dcc954047ce2a23506"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_session" DROP CONSTRAINT "FK_b5eb7aa08382591e7c2d1244fe5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_session" DROP CONSTRAINT "FK_a7ec9ee280c3f8b173a188d31c6"`,
    );
    await queryRunner.query(
      `ALTER TABLE "rfid" DROP CONSTRAINT "FK_32e2f4c2c465e5da47eb66ef068"`,
    );
    await queryRunner.query(
      `ALTER TABLE "rfid" DROP CONSTRAINT "FK_a0bd9fbe29f3283633cb90e3477"`,
    );
    await queryRunner.query(
      `ALTER TABLE "rfid" DROP CONSTRAINT "FK_b75dde2ef71a795b0492a187bf9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "rfid" DROP CONSTRAINT "FK_d56d3fb3abc12fac62a6b2ea0b8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_assignment" DROP CONSTRAINT "FK_8fc4a33dcac4a22e62bebe02540"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product_assignment" DROP CONSTRAINT "FK_7431e218b11a78f5c87d346510d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee_setting" DROP CONSTRAINT "FK_4e1eca151ff371c3ef967d4d204"`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee_setting" DROP CONSTRAINT "FK_a5e9dd8f33344aff873630cc396"`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon_item" DROP CONSTRAINT "FK_bea54f58ca98b197d23a3418faa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon_item" DROP CONSTRAINT "FK_55293e740c1b3eb7e262feed45c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "credit_history" DROP CONSTRAINT "FK_cc2ab1e4f7059bcb92f95e9ea89"`,
    );
    await queryRunner.query(
      `ALTER TABLE "appointment" DROP CONSTRAINT "FK_54e83867f2699998a25aa23e6ba"`,
    );
    await queryRunner.query(
      `ALTER TABLE "appointment" DROP CONSTRAINT "FK_c048c6004b69354f46183f93a85"`,
    );
    await queryRunner.query(
      `ALTER TABLE "order" DROP CONSTRAINT "FK_9d915a5cee9e0cfdf0d7fc3c30a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "order" DROP CONSTRAINT "FK_9b451675cd9fe227a0dd5120e53"`,
    );
    await queryRunner.query(
      `ALTER TABLE "order" DROP CONSTRAINT "FK_3b6667bfe775fa39753ca6af2dc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "order" DROP CONSTRAINT "FK_a73f2bf30b1471eeeaafafd425f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "order" DROP CONSTRAINT "FK_28c738460931ccd500e37e83586"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" DROP CONSTRAINT "FK_1b7679b9b3b5199c3b748f98bc6"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" DROP CONSTRAINT "FK_17034865ca60a8922ae9d163b58"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" DROP CONSTRAINT "FK_150d4d66fbf46ada8965e14294f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" DROP CONSTRAINT "FK_925aa26ea12c28a6adb614445ee"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice" DROP CONSTRAINT "FK_f6757552bd0776859af91c222ca"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice_payment_method" DROP CONSTRAINT "FK_5291a91ba840453c5d6c3552ca1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice_payment_method" DROP CONSTRAINT "FK_6cab39113c69858466d15fdb91f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice_payment_method" DROP CONSTRAINT "FK_b774c8d51141b71c7d9cd426808"`,
    );
    await queryRunner.query(
      `ALTER TABLE "payment_method" DROP CONSTRAINT "FK_bb8709c989ede47fe1c92849f3b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_detail" DROP CONSTRAINT "FK_6731225d3f542b2b5986d258286"`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_detail" DROP CONSTRAINT "FK_a3647bd11aed3cf968c9ce9b835"`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_detail" DROP CONSTRAINT "FK_88850b85b38a8a2ded17a1f5369"`,
    );
    await queryRunner.query(
      `ALTER TABLE "customer" DROP CONSTRAINT "FK_f28cf7cf65a7bf745e49686610a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "customer" DROP CONSTRAINT "FK_7a9d34477964f14ecac6e9fe037"`,
    );
    await queryRunner.query(
      `ALTER TABLE "customer" DROP CONSTRAINT "FK_62b279831e2dc8db00f9cdd7344"`,
    );
    await queryRunner.query(
      `ALTER TABLE "customer" DROP CONSTRAINT "FK_fd885e75fc93121c9f7c4258259"`,
    );
    await queryRunner.query(
      `ALTER TABLE "credit" DROP CONSTRAINT "FK_af9d0a0c166ec5affe700acc842"`,
    );
    await queryRunner.query(
      `ALTER TABLE "credit" DROP CONSTRAINT "FK_b141ee18961d3c62109fde45c08"`,
    );
    await queryRunner.query(
      `ALTER TABLE "credit" DROP CONSTRAINT "FK_c3085ff7ae2710d8c760e67257c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "credit_setting" DROP CONSTRAINT "FK_cc32204457a6dd1906075cf54da"`,
    );
    await queryRunner.query(
      `ALTER TABLE "branch" DROP CONSTRAINT "FK_cb76541b958bf2407aac031fe7a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "branch" DROP CONSTRAINT "FK_34f2d5f869b9d1270083058d601"`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee" DROP CONSTRAINT "FK_c36b6dc182259c56ee8c1cfecb3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee" DROP CONSTRAINT "FK_b6c7380bac45e746176960400aa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee" DROP CONSTRAINT "FK_1d63c8a9288d42747b5c5e20f7e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee" DROP CONSTRAINT "FK_8638b68d9b366f6405fa8262161"`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee" DROP CONSTRAINT "FK_67d2975a3e5e140720f86eb1183"`,
    );
    await queryRunner.query(
      `ALTER TABLE "day_off" DROP CONSTRAINT "FK_92ca44072861a97e6ce57335b8a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT "FK_8b17d5d91bf27d0a33fb80ade8f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT "FK_c28e52f758e7bbc53828db92194"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT "FK_58f5c71eaab331645112cf8cfa5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT "FK_6273b1aa12d5d17f8e1284200be"`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon" DROP CONSTRAINT "FK_a85d0722f3f6a522ff0e51808fa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon" DROP CONSTRAINT "FK_9aebd8e073c160515663f9b92bd"`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon" DROP CONSTRAINT "FK_7ac2227010228ec0f7d5280ca66"`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon" DROP CONSTRAINT "FK_93baceb995f0bcf98349b6fe259"`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon" DROP CONSTRAINT "FK_3b269b5467f9a9c7e4bea95b215"`,
    );
    await queryRunner.query(
      `ALTER TABLE "issue_coupon" DROP CONSTRAINT "FK_b298b696684276b7657616136fe"`,
    );
    await queryRunner.query(
      `ALTER TABLE "issue_coupon" DROP CONSTRAINT "FK_1121836749f9d02a840932d43c0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" DROP CONSTRAINT "FK_c35bbcb8009ac480b4700a94167"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" DROP CONSTRAINT "FK_ff0c0301a95e517153df97f6812"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" DROP CONSTRAINT "FK_034cfbbf7375507ddc3f7758491"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" DROP CONSTRAINT "FK_70962d60ddff098ed14045974a9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" DROP CONSTRAINT "FK_9ec2c7792817b56a3533ca1d7aa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" DROP CONSTRAINT "FK_e7471a79da9279e1f902f2d692f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "category" DROP CONSTRAINT "FK_210935706c5cd6f9a7a805cc029"`,
    );
    await queryRunner.query(
      `ALTER TABLE "category" DROP CONSTRAINT "FK_ada9c63b1bfb31bd904b3776c89"`,
    );
    await queryRunner.query(
      `ALTER TABLE "category" DROP CONSTRAINT "FK_d5456fd7e4c4866fec8ada1fa10"`,
    );
    await queryRunner.query(
      `ALTER TABLE "category" DROP CONSTRAINT "FK_56be20dbbf4499e02a7dec2260b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "token" DROP CONSTRAINT "FK_94f168faad896c0786646fa3d4a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "auth" DROP CONSTRAINT "FK_373ead146f110f04dad60848154"`,
    );
    await queryRunner.query(
      `ALTER TABLE "role" DROP CONSTRAINT "FK_3fc73ac1307382a3b92e79b4886"`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" DROP CONSTRAINT "FK_287fe7669c4035bba465728974c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" DROP CONSTRAINT "FK_cdb4db95384a1cf7a837c4c683e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "resource" DROP CONSTRAINT "FK_fe5891b7e529ab339018bd9d58d"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_69cf38c4f7e80d93f4ca15d6db"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_2200b366bcc10e361e46e797c1"`,
    );
    await queryRunner.query(`DROP TABLE "coupon_item_branches_branch"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_0011c8b1a9261827b02b888745"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_7e3f4f719536ce27808ab0e053"`,
    );
    await queryRunner.query(
      `DROP TABLE "invoice_invoice_payments_invoice_payment_method"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_d46cfabfd3c7534650f8b6db13"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_d5c7514f0b1e1a81c0566804ea"`,
    );
    await queryRunner.query(`DROP TABLE "order_detail_employees_employee"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_a6d3603177be24f8e05bc9944e"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b34aadd347d58a84d54f475e84"`,
    );
    await queryRunner.query(`DROP TABLE "customer_preferreds_employee"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_73ed91f67eb1a91d998f1afd2d"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_d6fec1b6e87cdb13d13c2239c9"`,
    );
    await queryRunner.query(`DROP TABLE "employee_services_product"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_bdd320620afd063015fa0976ae"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_006cd586575d86f4705186b869"`,
    );
    await queryRunner.query(`DROP TABLE "coupon_products_product"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b289b73728f01328cb0eeea772"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_a04e8eea563d3ff125b7849837"`,
    );
    await queryRunner.query(`DROP TABLE "coupon_branches_branch"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_eeb7d891e12f0bd300112ba681"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_384ffe299c0cb14ef5d8150f70"`,
    );
    await queryRunner.query(`DROP TABLE "issue_coupon_branches_branch"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_972cdbceab574cf28067c7a8a8"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_993adea15f9cc8e54dfb327656"`,
    );
    await queryRunner.query(`DROP TABLE "product_branches_branch"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_439948e1ec6b436cc8211c332b"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_e345d9be4773a56ba168fc63d9"`,
    );
    await queryRunner.query(`DROP TABLE "payslip_setting"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_127ed303c6cf78d1822140ac02"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_8ca5e43cb2d347a8db89b5cd2c"`,
    );
    await queryRunner.query(`DROP TABLE "commission_setting"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_2b0ccdff5d9364e848394573c3"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_2da56ce222f4b858d1079dd6c9"`,
    );
    await queryRunner.query(`DROP TABLE "payslip_employee_setting"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_991298ddc3b16c90eb0f6f942e"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_3ed421766f31f5d675fe8b7f21"`,
    );
    await queryRunner.query(`DROP TABLE "commission_product_setting"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_f4a92caf46092c4888b22f9696"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_71a441244c03dd7b3e828664a4"`,
    );
    await queryRunner.query(`DROP TABLE "commission_logs"`);
    await queryRunner.query(`DROP TYPE "public"."commission_logs_type_enum"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_2c4a6f88a5b80690c8c830a110"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_a89c463d7a1f0b8502862d6348"`,
    );
    await queryRunner.query(`DROP TABLE "wh_timer"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_07d83526947be4a3ffab53e154"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b051acb2e65df6e8dff37b8889"`,
    );
    await queryRunner.query(`DROP TABLE "wh_timework"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_3aacd5d11ef9eb99c119c08b97"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_80beab664074f9e56a1703b8b5"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_127646eadf8b45eeba37142b62"`,
    );
    await queryRunner.query(`DROP TABLE "working_hour"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_27636b771f9230735b3f06f7a5"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_da0bb6687f95cf849c314979bb"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_6519372a798da55d8f28d52e6a"`,
    );
    await queryRunner.query(`DROP TABLE "virtual_working_hour"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_eb9c6f8d90fe0fad4997626e23"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_546d80c51083a22d66917a128c"`,
    );
    await queryRunner.query(`DROP TABLE "user_session"`);
    await queryRunner.query(`DROP INDEX "public"."unique_locker_index"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_ec7d82d85b685910c09e827484"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_a79bcb47fc937a5a2673d8d4c8"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_d9e982dcb81446f42f02ed91f8"`,
    );
    await queryRunner.query(`DROP TABLE "rfid"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_1ad445cb6a99f9b05cbc690921"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_099193744c62b9ea338c5ea3c8"`,
    );
    await queryRunner.query(`DROP TABLE "product_assignment"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_1c737f5ad1a11f2c12af83cf74"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_274eed0abe91ff762c43f0e30a"`,
    );
    await queryRunner.query(`DROP TABLE "employee_setting"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_c3fac10d58495bcc90f2d195f5"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_9044d1fd4c0db47acc62d4857a"`,
    );
    await queryRunner.query(`DROP TABLE "coupon_item"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_2be7048f2a47a6a9b96f4b448e"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_43ad2262f280b575f19433d3f3"`,
    );
    await queryRunner.query(`DROP TABLE "credit_history"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_76142cf989a11d0a42999875a8"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_e8df3f8aafefdfa1edbe1c7004"`,
    );
    await queryRunner.query(`DROP TABLE "appointment"`);
    await queryRunner.query(`DROP TYPE "public"."appointment_status_enum"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_993d99dbc2fc07869d3e8adbef"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b12d7bd71915765f5c32d59039"`,
    );
    await queryRunner.query(`DROP TABLE "order"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_6fac6f69d1ab24d7c35a8a04a2"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_c3f0c58d8ac21386c4510fc416"`,
    );
    await queryRunner.query(`DROP TABLE "invoice"`);
    await queryRunner.query(`DROP TYPE "public"."invoice_status_enum"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_a76bea96dd8633903027d3c741"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_0f0de44012385a793043f27022"`,
    );
    await queryRunner.query(`DROP TABLE "invoice_payment_method"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_77da6bf97902c0264cd9912fed"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_9d38a9f610c75340989191eafa"`,
    );
    await queryRunner.query(`DROP TABLE "payment_method"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_a4f517cc0875b3a6898f0adbe6"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_fcdf4bcfc9f48bf97b35b1ee08"`,
    );
    await queryRunner.query(`DROP TABLE "order_detail"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_4c58be1b83ae68eece02e51964"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_dc5912edb8a02da5e0b9b57c35"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_6d8aa7b020d850b5a9ff69a5d3"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_3096cd9b6906e855d99ccb3d8a"`,
    );
    await queryRunner.query(`DROP TABLE "customer"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_745d2e7dbf9dc318551a66c633"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_ddd2665fdc3ae95a9ea09b96f6"`,
    );
    await queryRunner.query(`DROP TABLE "credit"`);
    await queryRunner.query(`DROP TYPE "public"."credit_status_enum"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_66468dc10dbd60e28fd3df9553"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_680311a4072231d413d8c5ca43"`,
    );
    await queryRunner.query(`DROP TABLE "credit_setting"`);
    await queryRunner.query(
      `DROP TYPE "public"."credit_setting_credittype_enum"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_6e06693a4255ca9b0a9a366260"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_15925240dbead9258f3512944a"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_c23d8593cc9d70e327fd61cf16"`,
    );
    await queryRunner.query(`DROP TABLE "branch"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_723472e41cae44beb0763f4039"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_77f11186dd58a8d87ad5fff024"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_ef492e222a0dec96e6d1551159"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_4aabc0c79491db35ed65ae220a"`,
    );
    await queryRunner.query(`DROP TABLE "currency"`);
    await queryRunner.query(`DROP INDEX "public"."unique_displayname_index"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_817d1d427138772d47eca04885"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_81afb288b526f7e8fed0e4200c"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_c17f84bb360b34b47025812250"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_e25c79f9a0bb0cc68b3d360ecd"`,
    );
    await queryRunner.query(`DROP TABLE "employee"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_fa1c8e57a91c164b81560aa160"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_90dac1cb8ca4d80352a0f1ce6b"`,
    );
    await queryRunner.query(`DROP TABLE "day_off"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_e12875dfb3b1d92d7d7c5377e2"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_5904a9d40152f354e4c7b0202f"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_8ce4c93ba419b56bd82e533724"`,
    );
    await queryRunner.query(`DROP TABLE "user"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_719c3627e724197bb8bfa83fa1"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_560a99f8f9095b3a35453c1400"`,
    );
    await queryRunner.query(`DROP TABLE "media"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_d9b61d81c5b14a311ad7843dc9"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_956654d64f49bb48dd490a74cb"`,
    );
    await queryRunner.query(`DROP TABLE "coupon"`);
    await queryRunner.query(`DROP TYPE "public"."coupon_periodunit_enum"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_09a05d7cf1c2e8e84ff2f40b6f"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_6d446229df5422f1ffee6637ef"`,
    );
    await queryRunner.query(`DROP TABLE "issue_coupon"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_81dd851479985ba78a9f602140"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_9efdf752ef34afbb5927e601b6"`,
    );
    await queryRunner.query(`DROP TABLE "product"`);
    await queryRunner.query(`DROP TYPE "public"."product_credittype_enum"`);
    await queryRunner.query(`DROP TYPE "public"."product_periodunit_enum"`);
    await queryRunner.query(`DROP TYPE "public"."product_type_enum"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_1937f8aac57c07ee599667c685"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_c6d677f0ce77c6242f66a76509"`,
    );
    await queryRunner.query(`DROP TABLE "category"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_4ba6959764ed464c3373614027"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_2f00aef2888bc6f10559d2d25e"`,
    );
    await queryRunner.query(`DROP TABLE "token"`);
    await queryRunner.query(`DROP TYPE "public"."token_type_enum"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_536067205638cca7e68e55c5e2"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_52bb200ec9c4db636b2ed8bd86"`,
    );
    await queryRunner.query(`DROP TABLE "auth"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_0fcd8225d86d3bda963629683a"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_39861f6656e48cd488a023e227"`,
    );
    await queryRunner.query(`DROP TABLE "role"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_167a9d2dbb3667b896642404cc"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_2139f3b5ad8f7e095679fb50cf"`,
    );
    await queryRunner.query(`DROP TABLE "permission"`);
    await queryRunner.query(`DROP TYPE "public"."permission_status_enum"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_3d808ef101c28011f615dfec7d"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_13aa7974487bf577275c826779"`,
    );
    await queryRunner.query(`DROP TABLE "resource"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_2c9fe777c7cb794bb60678e353"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_5a82e1557167dffd637b2694ec"`,
    );
    await queryRunner.query(`DROP TABLE "setting"`);
  }
}
