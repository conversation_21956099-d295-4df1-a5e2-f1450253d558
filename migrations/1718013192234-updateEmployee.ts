import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateEmployee1718013192234 implements MigrationInterface {
  name = 'UpdateEmployee1718013192234';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX IF EXISTS "public"."IDX_817d1d427138772d47eca04885"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "public"."unique_displayname_index"`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "unique_displayname_index" ON "employee" ("branchId", "displayName", "email") WHERE deleted IS NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."unique_displayname_index"`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "unique_displayname_index" ON "employee" ("displayName", "branchId") WHERE (deleted IS NULL)`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_817d1d427138772d47eca04885" ON "employee" ("email") `,
    );
  }
}
