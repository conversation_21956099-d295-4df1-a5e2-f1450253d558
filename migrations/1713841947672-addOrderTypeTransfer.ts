import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddOrderTypeTransfer1713841947672 implements MigrationInterface {
  name = 'AddOrderTypeTransfer1713841947672';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."fb_order_ordertype_enum" RENAME TO "fb_order_ordertype_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."fb_order_ordertype_enum" AS ENUM('others', 'membership', 'food-beverage', 'transfer')`,
    );
    await queryRunner.query(
      `ALTER TABLE "fb_order" ALTER COLUMN "orderType" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "fb_order" ALTER COLUMN "orderType" TYPE "public"."fb_order_ordertype_enum" USING "orderType"::"text"::"public"."fb_order_ordertype_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "fb_order" ALTER COLUMN "orderType" SET DEFAULT 'others'`,
    );
    await queryRunner.query(`DROP TYPE "public"."fb_order_ordertype_enum_old"`);
    await queryRunner.query(
      `ALTER TYPE "public"."order_ordertype_enum" RENAME TO "order_ordertype_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."order_ordertype_enum" AS ENUM('others', 'membership', 'food-beverage', 'transfer')`,
    );
    await queryRunner.query(
      `ALTER TABLE "order" ALTER COLUMN "orderType" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "order" ALTER COLUMN "orderType" TYPE "public"."order_ordertype_enum" USING "orderType"::"text"::"public"."order_ordertype_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "order" ALTER COLUMN "orderType" SET DEFAULT 'others'`,
    );
    await queryRunner.query(`DROP TYPE "public"."order_ordertype_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."order_ordertype_enum_old" AS ENUM('others', 'membership', 'food-beverage')`,
    );
    await queryRunner.query(
      `ALTER TABLE "order" ALTER COLUMN "orderType" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "order" ALTER COLUMN "orderType" TYPE "public"."order_ordertype_enum_old" USING "orderType"::"text"::"public"."order_ordertype_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "order" ALTER COLUMN "orderType" SET DEFAULT 'others'`,
    );
    await queryRunner.query(`DROP TYPE "public"."order_ordertype_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."order_ordertype_enum_old" RENAME TO "order_ordertype_enum"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."fb_order_ordertype_enum_old" AS ENUM('others', 'membership', 'food-beverage')`,
    );
    await queryRunner.query(
      `ALTER TABLE "fb_order" ALTER COLUMN "orderType" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "fb_order" ALTER COLUMN "orderType" TYPE "public"."fb_order_ordertype_enum_old" USING "orderType"::"text"::"public"."fb_order_ordertype_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "fb_order" ALTER COLUMN "orderType" SET DEFAULT 'others'`,
    );
    await queryRunner.query(`DROP TYPE "public"."fb_order_ordertype_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."fb_order_ordertype_enum_old" RENAME TO "fb_order_ordertype_enum"`,
    );
  }
}
