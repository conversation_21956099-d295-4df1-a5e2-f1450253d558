import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateAppointment1716800826667 implements MigrationInterface {
  name = 'UpdateAppointment1716800826667';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TYPE "public"."appointment_status_enum" RENAME TO "appointment_status_enum_old"`);
    await queryRunner.query(`CREATE TYPE "public"."appointment_status_enum" AS ENUM('BOOKING', 'ARRIVED', 'REQUEST', 'FACIAL_IPL', 'REQUEST_ARRIVED', 'CANCEL')`);
    await queryRunner.query(`ALTER TABLE "appointment" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "appointment" ALTER COLUMN "status" TYPE "public"."appointment_status_enum" USING "status"::"text"::"public"."appointment_status_enum"`);
    await queryRunner.query(`ALTER TABLE "appointment" ALTER COLUMN "status" SET DEFAULT 'BOOKING'`);
    await queryRunner.query(`DROP TYPE "public"."appointment_status_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."appointment_status_enum_old" AS ENUM('NEW', 'STARTED', 'BOOKING', 'COMPLETED', 'CONFIRM', 'CANCEL', 'NOSHOW')`);
    await queryRunner.query(`ALTER TABLE "appointment" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "appointment" ALTER COLUMN "status" TYPE "public"."appointment_status_enum_old" USING "status"::"text"::"public"."appointment_status_enum_old"`);
    await queryRunner.query(`ALTER TABLE "appointment" ALTER COLUMN "status" SET DEFAULT 'NEW'`);
    await queryRunner.query(`DROP TYPE "public"."appointment_status_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."appointment_status_enum_old" RENAME TO "appointment_status_enum"`);
  }
}
