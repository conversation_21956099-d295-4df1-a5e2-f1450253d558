import { MigrationInterface, QueryRunner } from 'typeorm';

export class UdpateProductType1716186263777 implements MigrationInterface {
  name = 'UdpateProductType1716186263777';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."product_type_enum" RENAME TO "product_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."product_type_enum" AS ENUM('product', 'service', 'membership', 'food', 'beverage', 'coupon', 'food-beverage')`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ALTER COLUMN "type" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ALTER COLUMN "type" TYPE "public"."product_type_enum" USING "type"::"text"::"public"."product_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ALTER COLUMN "type" SET DEFAULT 'product'`,
    );
    await queryRunner.query(`DROP TYPE "public"."product_type_enum_old"`);
    await queryRunner.query(
      `ALTER TYPE "public"."commission_logs_type_enum" RENAME TO "commission_logs_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."commission_logs_type_enum" AS ENUM('product', 'service', 'membership', 'food', 'beverage', 'coupon', 'food-beverage')`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" ALTER COLUMN "type" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" ALTER COLUMN "type" TYPE "public"."commission_logs_type_enum" USING "type"::"text"::"public"."commission_logs_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" ALTER COLUMN "type" SET DEFAULT 'product'`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."commission_logs_type_enum_old"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."commission_logs_type_enum_old" AS ENUM('product', 'service', 'membership', 'food-beverage', 'coupon')`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" ALTER COLUMN "type" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" ALTER COLUMN "type" TYPE "public"."commission_logs_type_enum_old" USING "type"::"text"::"public"."commission_logs_type_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" ALTER COLUMN "type" SET DEFAULT 'product'`,
    );
    await queryRunner.query(`DROP TYPE "public"."commission_logs_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."commission_logs_type_enum_old" RENAME TO "commission_logs_type_enum"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."product_type_enum_old" AS ENUM('product', 'service', 'membership', 'food-beverage', 'coupon')`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ALTER COLUMN "type" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ALTER COLUMN "type" TYPE "public"."product_type_enum_old" USING "type"::"text"::"public"."product_type_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ALTER COLUMN "type" SET DEFAULT 'product'`,
    );
    await queryRunner.query(`DROP TYPE "public"."product_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."product_type_enum_old" RENAME TO "product_type_enum"`,
    );
  }
}
