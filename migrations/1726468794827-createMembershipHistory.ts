import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateMembershipHistory1726468794827
  implements MigrationInterface
{
  name = 'CreateMembershipHistory1726468794827';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "membership_history" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "paid" real NOT NULL, "membershipId" uuid, "invoiceId" uuid, CONSTRAINT "PK_2be1074a461a1126edc0a0cde6d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_fd3c06ec8565def5547cc379ce" ON "membership_history" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_49a51b668a4562d5060f0083b0" ON "membership_history" ("updated") `,
    );
    await queryRunner.query(
      `ALTER TABLE "membership_history" ADD CONSTRAINT "FK_20166dd31506ab08dce65ec23b5" FOREIGN KEY ("membershipId") REFERENCES "credit_history"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "membership_history" ADD CONSTRAINT "FK_145da6504b1157d3848399f9a2c" FOREIGN KEY ("invoiceId") REFERENCES "invoice"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "membership_history" DROP CONSTRAINT "FK_145da6504b1157d3848399f9a2c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "membership_history" DROP CONSTRAINT "FK_20166dd31506ab08dce65ec23b5"`,
    );
    await queryRunner.query(`DROP TABLE "membership_history"`);
  }
}
