import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddExpiryDate1710861980039 implements MigrationInterface {
  name = 'AddExpiryDate1710861980039';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "credit_history" ADD "expiryDate" TIMESTAMP WITH TIME ZONE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "credit_history" DROP COLUMN "expiryDate"`,
    );
  }
}
