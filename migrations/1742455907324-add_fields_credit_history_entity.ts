import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldsCreditHistoryEntity1742455907324
  implements MigrationInterface
{
  name = 'AddFieldsCreditHistoryEntity1742455907324';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "credit_history" ADD "refund" real`);
    await queryRunner.query(`ALTER TABLE "credit_history" ADD "value" real`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "credit_history" DROP COLUMN "value"`);
    await queryRunner.query(
      `ALTER TABLE "credit_history" DROP COLUMN "refund"`,
    );
  }
}
