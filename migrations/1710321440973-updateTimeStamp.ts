import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTimeStamp1710321440973 implements MigrationInterface {
  name = 'UpdateTimeStamp1710321440973';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "issue_coupon" ALTER COLUMN "issueDate" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "coupon" ALTER COLUMN "startDate" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "birthday" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "day_off" ALTER COLUMN "startDate" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "day_off" ALTER COLUMN "endDate" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "employee" ALTER COLUMN "birthDay" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "employee" ALTER COLUMN "startDate" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "employee" ALTER COLUMN "endDate" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "credit_setting" ALTER COLUMN "applyFromTheDate" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "credit" ALTER COLUMN "issueDate" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "credit" ALTER COLUMN "expiryDate" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "customer" ALTER COLUMN "expiryDate" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "customer" ALTER COLUMN "firstVisit" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "customer" ALTER COLUMN "birthDay" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "order_detail" ALTER COLUMN "startTime" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "order_detail" ALTER COLUMN "endTime" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "invoice" ALTER COLUMN "date" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "appointment" ALTER COLUMN "startTime" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "appointment" ALTER COLUMN "endTime" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "appointment" ALTER COLUMN "checkIn" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "appointment" ALTER COLUMN "checkOut" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "user_session" ALTER COLUMN "login" TYPE timestamptz`,
    );

    await queryRunner.query(
      `ALTER TABLE "user_session" ALTER COLUMN "logout" TYPE timestamptz`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
