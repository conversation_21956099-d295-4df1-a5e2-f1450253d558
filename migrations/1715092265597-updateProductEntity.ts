import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateProductEntity1715092265597 implements MigrationInterface {
  name = 'UpdateProductEntity1715092265597';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "issue_coupon" DROP CONSTRAINT IF EXISTS "FK_b298b696684276b7657616136fe"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ADD "startDate" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ADD "discountValue" real NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(`ALTER TABLE "product" ADD "issueId" uuid`);
    await queryRunner.query(`ALTER TABLE "product" ADD "discountTypeId" uuid`);
    await queryRunner.query(
      `ALTER TYPE "public"."product_type_enum" RENAME TO "product_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."product_type_enum" AS ENUM('product', 'service', 'membership', 'food-beverage', 'coupon')`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ALTER COLUMN "type" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ALTER COLUMN "type" TYPE "public"."product_type_enum" USING "type"::"text"::"public"."product_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ALTER COLUMN "type" SET DEFAULT 'product'`,
    );
    await queryRunner.query(`DROP TYPE "public"."product_type_enum_old"`);
    await queryRunner.query(
      `ALTER TYPE "public"."commission_logs_type_enum" RENAME TO "commission_logs_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."commission_logs_type_enum" AS ENUM('product', 'service', 'membership', 'food-beverage', 'coupon')`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" ALTER COLUMN "type" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" ALTER COLUMN "type" TYPE "public"."commission_logs_type_enum" USING "type"::"text"::"public"."commission_logs_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" ALTER COLUMN "type" SET DEFAULT 'product'`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."commission_logs_type_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "issue_coupon" DROP CONSTRAINT IF EXISTS "FK_b298b696684276b7657616136fe"`,
    );
    await queryRunner.query(
      `ALTER TABLE "issue_coupon" DROP COLUMN "couponId"`,
    );
    await queryRunner.query(`ALTER TABLE "issue_coupon" ADD "couponId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "issue_coupon" ADD CONSTRAINT "FK_b298b696684276b7657616136fe" FOREIGN KEY ("couponId") REFERENCES "product"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ADD CONSTRAINT "FK_16c7d28f161ebd37878c2bd6e64" FOREIGN KEY ("issueId") REFERENCES "issue_coupon"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ADD CONSTRAINT "FK_d7451a7339d91e064a11a2c240d" FOREIGN KEY ("discountTypeId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "product" DROP CONSTRAINT "FK_d7451a7339d91e064a11a2c240d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" DROP CONSTRAINT "FK_16c7d28f161ebd37878c2bd6e64"`,
    );
    await queryRunner.query(
      `ALTER TABLE "issue_coupon" DROP CONSTRAINT "FK_b298b696684276b7657616136fe"`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" DROP CONSTRAINT "FK_cdb4db95384a1cf7a837c4c683e"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."commission_logs_type_enum_old" AS ENUM('product', 'service', 'membership', 'food-beverage')`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" ALTER COLUMN "type" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" ALTER COLUMN "type" TYPE "public"."commission_logs_type_enum_old" USING "type"::"text"::"public"."commission_logs_type_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "commission_logs" ALTER COLUMN "type" SET DEFAULT 'product'`,
    );
    await queryRunner.query(`DROP TYPE "public"."commission_logs_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."commission_logs_type_enum_old" RENAME TO "commission_logs_type_enum"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."product_type_enum_old" AS ENUM('product', 'service', 'membership', 'food-beverage')`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ALTER COLUMN "type" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ALTER COLUMN "type" TYPE "public"."product_type_enum_old" USING "type"::"text"::"public"."product_type_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" ALTER COLUMN "type" SET DEFAULT 'product'`,
    );
    await queryRunner.query(`DROP TYPE "public"."product_type_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."product_type_enum_old" RENAME TO "product_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "product" DROP COLUMN "discountTypeId"`,
    );
    await queryRunner.query(`ALTER TABLE "product" DROP COLUMN "issueId"`);
    await queryRunner.query(
      `ALTER TABLE "product" DROP COLUMN "discountValue"`,
    );
    await queryRunner.query(`ALTER TABLE "product" DROP COLUMN "startDate"`);
    await queryRunner.query(
      `ALTER TABLE "issue_coupon" ADD CONSTRAINT "FK_b298b696684276b7657616136fe" FOREIGN KEY ("couponId") REFERENCES "coupon"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
