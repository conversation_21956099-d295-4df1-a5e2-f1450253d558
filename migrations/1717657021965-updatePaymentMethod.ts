import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePaymentMethod1717657021965 implements MigrationInterface {
  name = 'UpdatePaymentMethod1717657021965';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "payment_method" ADD "isOptionBillCode" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice_payment_method" ADD "billCode" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "invoice_payment_method" DROP COLUMN "billCode"`,
    );
    await queryRunner.query(
      `ALTER TABLE "payment_method" DROP COLUMN "isOptionBillCode"`,
    );
  }
}
