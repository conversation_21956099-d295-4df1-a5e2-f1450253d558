import { MigrationInterface, QueryRunner } from 'typeorm';

export class DeletefkRolePerm1711522299090 implements MigrationInterface {
  name = 'DeletefkRolePerm1711522299090';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "permission" DROP CONSTRAINT IF EXISTS "permission_roleId_fkey"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT  IF EXISTS "user_roleId_fkey"`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" DROP CONSTRAINT IF EXISTS "FK_cdb4db95384a1cf7a837c4c683e"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "permission" ADD CONSTRAINT "FK_cdb4db95384a1cf7a837c4c683e" FOREIGN KEY ("roleId") REFERENCES "role"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "user_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "role"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ADD CONSTRAINT "permission_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "role"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
