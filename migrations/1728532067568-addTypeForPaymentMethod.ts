import { MigrationInterface, QueryRunner } from "typeorm";

export class  AddTypeForPaymentMethod1728532067568 implements MigrationInterface {
    name = 'AddTypeForPaymentMethod1728532067568'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."payment_method_type_enum" AS ENUM('cash', 'card', 'foc', 'others')`);
        await queryRunner.query(`ALTER TABLE "payment_method" ADD "type" "public"."payment_method_type_enum" NOT NULL DEFAULT 'others'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "payment_method" DROP COLUMN "type"`);
        await queryRunner.query(`DROP TYPE "public"."payment_method_type_enum"`);
    }

}
