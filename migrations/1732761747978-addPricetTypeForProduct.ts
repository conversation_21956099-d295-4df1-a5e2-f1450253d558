import { MigrationInterface, QueryRunner } from "typeorm";

export class  AddPricetTypeForProduct1732761747978 implements MigrationInterface {
    name = 'AddPricetTypeForProduct1732761747978'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product" ADD "priceTypeId" uuid`);
        await queryRunner.query(`ALTER TABLE "product" ADD CONSTRAINT "FK_4281bc7b5c596a0c41e879ac4ce" FOREIGN KEY ("priceTypeId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product" DROP CONSTRAINT "FK_4281bc7b5c596a0c41e879ac4ce"`);
        await queryRunner.query(`ALTER TABLE "product" DROP COLUMN "priceTypeId"`);
    }

}
