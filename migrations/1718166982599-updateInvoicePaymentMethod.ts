import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateInvoicePaymentMethod1718166982599
  implements MigrationInterface
{
  name = 'UpdateInvoicePaymentMethod1718166982599';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "invoice_payment_method" ADD "roundNumber" real DEFAULT '0'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "invoice_payment_method" DROP COLUMN "roundNumber"`,
    );
  }
}
