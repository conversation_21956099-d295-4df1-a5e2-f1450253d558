import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCreditHistory1715056643909 implements MigrationInterface {
  name = 'UpdateCreditHistory1715056643909';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "credit_history" ADD "usable" real`);
    await queryRunner.query(`ALTER TABLE "credit_history" ADD "membershipId" uuid`);
    await queryRunner.query(`ALTER TABLE "credit_history" ADD CONSTRAINT "FK_bd40204a0afc327e3902e795d2e" FOREIGN KEY ("membershipId") REFERENCES "credit_history"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "credit_history" DROP CONSTRAINT "FK_bd40204a0afc327e3902e795d2e"`);
    await queryRunner.query(`ALTER TABLE "credit_history" DROP COLUMN "membershipId"`);
    await queryRunner.query(`ALTER TABLE "credit_history" DROP COLUMN "usable"`);
  }
}
