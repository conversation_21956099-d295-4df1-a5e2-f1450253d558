import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPurgedDate1722932288705 implements MigrationInterface {
  name = 'AddPurgedDate1722932288705';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "credit_history" ADD "purgedDate" TIMESTAMP WITH TIME ZONE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "credit_history" DROP COLUMN "purgedDate"`,
    );
  }
}
