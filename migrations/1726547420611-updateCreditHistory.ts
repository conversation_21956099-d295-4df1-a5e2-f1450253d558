import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCreditHistory1726547420611 implements MigrationInterface {
  name = 'UpdateCreditHistory1726547420611';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "credit_history" ADD "productId" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "credit_history" ADD CONSTRAINT "FK_146185407f3355f29dda154bd40" FOREIGN KEY ("productId") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "credit_history" DROP CONSTRAINT "FK_146185407f3355f29dda154bd40"`,
    );
    await queryRunner.query(
      `ALTER TABLE "credit_history" DROP COLUMN "productId"`,
    );
  }
}
