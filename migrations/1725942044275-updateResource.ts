import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateResource1725942044275 implements MigrationInterface {
  name = 'UpdateResource1725942044275';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "resource" ADD "active" boolean`);
    await queryRunner.query(`ALTER TABLE "resource" ADD "changePass" boolean`);
    await queryRunner.query(
      `ALTER TABLE "resource" ADD "listCalendar" boolean`,
    );
    await queryRunner.query(
      `ALTER TABLE "resource" ADD "addAppointment" boolean`,
    );
    await queryRunner.query(`ALTER TABLE "resource" ADD "export" boolean`);
    await queryRunner.query(`ALTER TABLE "resource" ADD "sendMail" boolean`);
    await queryRunner.query(`ALTER TABLE "resource" ADD "checkOut" boolean`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "checkOut"`);
    await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "sendMail"`);
    await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "export"`);
    await queryRunner.query(
      `ALTER TABLE "resource" DROP COLUMN "addAppointment"`,
    );
    await queryRunner.query(
      `ALTER TABLE "resource" DROP COLUMN "listCalendar"`,
    );
    await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "changePass"`);
    await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "active"`);
  }
}
