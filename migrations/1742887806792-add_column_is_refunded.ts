import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnIsRefunded1742887806792 implements MigrationInterface {
  name = 'AddColumnIsRefunded1742887806792';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "credit_history" ADD "isRefunded" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "credit_history" DROP COLUMN "isRefunded"`,
    );
  }
}
