import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateProduct1719373155669 implements MigrationInterface {
  name = 'UpdateProduct1719373155669';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "product" ADD "isNotApplyDiscount" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "product" DROP COLUMN "isNotApplyDiscount"`,
    );
  }
}
