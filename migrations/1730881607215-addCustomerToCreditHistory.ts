import { MigrationInterface, QueryRunner } from "typeorm";

export class  AddCustomerToCreditHistory1730881607215 implements MigrationInterface {
    name = 'AddCustomerToCreditHistory1730881607215'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "credit_history" ADD "customerId" uuid`);
        await queryRunner.query(`ALTER TABLE "credit_history" ADD CONSTRAINT "FK_4041098965b5ea95c7863c94e2f" FOREIGN KEY ("customerId") REFERENCES "customer"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "credit_history" DROP CONSTRAINT "FK_4041098965b5ea95c7863c94e2f"`);
        await queryRunner.query(`ALTER TABLE "credit_history" DROP COLUMN "customerId"`);
    }

}
