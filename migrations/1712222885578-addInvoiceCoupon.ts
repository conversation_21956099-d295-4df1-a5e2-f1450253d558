import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddInvoiceCoupon1712222885578 implements MigrationInterface {
  name = 'AddInvoiceCoupon1712222885578';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."invoice_coupon_coupontype_enum" AS ENUM('Code', 'Money', 'Percentage')`,
    );
    await queryRunner.query(
      `CREATE TABLE "invoice_coupon" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "discountValue" real NOT NULL DEFAULT '0', "couponName" character varying NOT NULL, "couponCode" character varying NOT NULL, "couponType" "public"."invoice_coupon_coupontype_enum", "order" integer NOT NULL DEFAULT '0', "couponItemId" uuid, "branchId" uuid, "discountProductId" uuid, CONSTRAINT "PK_a4486644b8b386a56dfbe1533cb" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_025c94d72ac50889c8deaacfc6" ON "invoice_coupon" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c0125afda79e0114d9c963483e" ON "invoice_coupon" ("updated") `,
    );
    await queryRunner.query(
      `CREATE TABLE "invoice_invoice_coupon_invoice_coupon" ("invoiceId" uuid NOT NULL, "invoiceCouponId" uuid NOT NULL, CONSTRAINT "PK_8d56308ca8e7ebfe0b0e0baf167" PRIMARY KEY ("invoiceId", "invoiceCouponId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_78b99fbc88e8d5bb37cf694fde" ON "invoice_invoice_coupon_invoice_coupon" ("invoiceId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_cab61d21cc99c48717f4264f63" ON "invoice_invoice_coupon_invoice_coupon" ("invoiceCouponId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice_coupon" ADD CONSTRAINT "FK_24aa52db4105deebb3e00275431" FOREIGN KEY ("couponItemId") REFERENCES "coupon_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice_coupon" ADD CONSTRAINT "FK_0f9777dcc8972b0cf37721abfdd" FOREIGN KEY ("branchId") REFERENCES "branch"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice_coupon" ADD CONSTRAINT "FK_35241d01ff998962561f6e3ab5b" FOREIGN KEY ("discountProductId") REFERENCES "product"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice_invoice_coupon_invoice_coupon" ADD CONSTRAINT "FK_78b99fbc88e8d5bb37cf694fde7" FOREIGN KEY ("invoiceId") REFERENCES "invoice"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice_invoice_coupon_invoice_coupon" ADD CONSTRAINT "FK_cab61d21cc99c48717f4264f637" FOREIGN KEY ("invoiceCouponId") REFERENCES "invoice_coupon"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "invoice_invoice_coupon_invoice_coupon" DROP CONSTRAINT "FK_cab61d21cc99c48717f4264f637"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice_invoice_coupon_invoice_coupon" DROP CONSTRAINT "FK_78b99fbc88e8d5bb37cf694fde7"`,
    );

    await queryRunner.query(
      `ALTER TABLE "invoice_coupon" DROP CONSTRAINT "FK_35241d01ff998962561f6e3ab5b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice_coupon" DROP CONSTRAINT "FK_0f9777dcc8972b0cf37721abfdd"`,
    );
    await queryRunner.query(
      `ALTER TABLE "invoice_coupon" DROP CONSTRAINT "FK_24aa52db4105deebb3e00275431"`,
    );

    await queryRunner.query(
      `DROP TABLE "invoice_invoice_coupon_invoice_coupon"`,
    );

    await queryRunner.query(`DROP TABLE "invoice_coupon"`);
    await queryRunner.query(
      `DROP TYPE "public"."invoice_coupon_coupontype_enum"`,
    );
  }
}
