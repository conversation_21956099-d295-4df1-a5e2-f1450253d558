import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateOrderAndInvoice1715670155258 implements MigrationInterface {
  name = 'UpdateOrderAndInvoice1715670155258';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TYPE "public"."invoice_status_enum" RENAME TO "invoice_status_enum_old"`);
    await queryRunner.query(`CREATE TYPE "public"."invoice_status_enum" AS ENUM('PART_PAID', 'PAID', 'UNPAID', 'VOID')`);
    await queryRunner.query(`ALTER TABLE "invoice" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "invoice" ALTER COLUMN "status" TYPE "public"."invoice_status_enum" USING "status"::"text"::"public"."invoice_status_enum"`);
    await queryRunner.query(`ALTER TABLE "invoice" ALTER COLUMN "status" SET DEFAULT 'UNPAID'`);
    await queryRunner.query(`DROP TYPE "public"."invoice_status_enum_old"`);
    await queryRunner.query(`ALTER TYPE "public"."order_status_enum" RENAME TO "order_status_enum_old"`);
    await queryRunner.query(`CREATE TYPE "public"."order_status_enum" AS ENUM('PART_PAID', 'PAID', 'UNPAID', 'VOID')`);
    await queryRunner.query(`ALTER TABLE "order" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "order" ALTER COLUMN "status" TYPE "public"."order_status_enum" USING "status"::"text"::"public"."order_status_enum"`);
    await queryRunner.query(`ALTER TABLE "order" ALTER COLUMN "status" SET DEFAULT 'UNPAID'`);
    await queryRunner.query(`DROP TYPE "public"."order_status_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."order_status_enum_old" AS ENUM('PART_PAID', 'PAID', 'UNPAID')`);
    await queryRunner.query(`ALTER TABLE "order" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "order" ALTER COLUMN "status" TYPE "public"."order_status_enum_old" USING "status"::"text"::"public"."order_status_enum_old"`);
    await queryRunner.query(`ALTER TABLE "order" ALTER COLUMN "status" SET DEFAULT 'UNPAID'`);
    await queryRunner.query(`DROP TYPE "public"."order_status_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."order_status_enum_old" RENAME TO "order_status_enum"`);
    await queryRunner.query(`CREATE TYPE "public"."invoice_status_enum_old" AS ENUM('PART_PAID', 'PAID', 'UNPAID')`);
    await queryRunner.query(`ALTER TABLE "invoice" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "invoice" ALTER COLUMN "status" TYPE "public"."invoice_status_enum_old" USING "status"::"text"::"public"."invoice_status_enum_old"`);
    await queryRunner.query(`ALTER TABLE "invoice" ALTER COLUMN "status" SET DEFAULT 'UNPAID'`);
    await queryRunner.query(`DROP TYPE "public"."invoice_status_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."invoice_status_enum_old" RENAME TO "invoice_status_enum"`);
  }
}
