import { MigrationInterface, QueryRunner } from "typeorm";

export class  AddSomePermission1729479633298 implements MigrationInterface {
    name = 'AddSomePermission1729479633298'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "resource" ADD "changePaymentSingleDay" boolean`);
        await queryRunner.query(`ALTER TABLE "resource" ADD "changePaymentAllDays" boolean`);
        await queryRunner.query(`ALTER TABLE "resource" ADD "editSingleDay" boolean`);
        await queryRunner.query(`ALTER TABLE "resource" ADD "editAllDays" boolean`);
        await queryRunner.query(`ALTER TABLE "resource" ADD "refund" boolean`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "refund"`);
        await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "editAllDays"`);
        await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "editSingleDay"`);
        await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "changePaymentAllDays"`);
        await queryRunner.query(`ALTER TABLE "resource" DROP COLUMN "changePaymentSingleDay"`);
    }

}
