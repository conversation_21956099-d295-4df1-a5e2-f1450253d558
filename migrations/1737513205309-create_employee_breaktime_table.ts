import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateEmployeeBreaktimeTable1737513205309
  implements MigrationInterface
{
  name = 'CreateEmployeeBreaktimeTable1737513205309';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "employee_break_time" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "startTime" TIMESTAMP WITH TIME ZONE, "endTime" TIMESTAMP WITH TIME ZONE, "type" character varying, "employeeId" uuid, "branchId" uuid, CONSTRAINT "PK_971078b5ab26c2a749b8424d11c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6cd17b979310072e3195aa6624" ON "employee_break_time" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5c1e5344f0f66f3f90ef2fa839" ON "employee_break_time" ("updated") `,
    );
    await queryRunner.query(
      `ALTER TABLE "employee_break_time" ADD CONSTRAINT "FK_acdba5626861c26cedf2d45bf5d" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee_break_time" ADD CONSTRAINT "FK_a7812b835e7f733675f0161d2ab" FOREIGN KEY ("branchId") REFERENCES "branch"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_5c1e5344f0f66f3f90ef2fa839"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_6cd17b979310072e3195aa6624"`,
    );
    await queryRunner.query(`DROP TABLE "employee_break_time"`);
  }
}
