import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTransferOrder1713756510098 implements MigrationInterface {
  name = 'AddTransferOrder1713756510098';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "order" ADD "transferById" uuid`);
    await queryRunner.query(
      `ALTER TABLE "order" ADD CONSTRAINT "FK_ab4cd0a514f2ed7f2a96637117d" FOREIGN KEY ("transferById") REFERENCES "customer"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "order" DROP CONSTRAINT "FK_ab4cd0a514f2ed7f2a96637117d"`,
    );
    await queryRunner.query(`ALTER TABLE "order" DROP COLUMN "transferById"`);
  }
}
