import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateRfid1719903209919 implements MigrationInterface {
  name = 'UpdateRfid1719903209919';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."unique_locker_index"`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_ec7d82d85b685910c09e827484" ON "rfid" ("serialCode") WHERE deleted IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "unique_locker_index" ON "rfid" ("branchId", "groupId", "lockerNumber") WHERE deleted IS NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."unique_locker_index"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_ec7d82d85b685910c09e827484"`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "unique_locker_index" ON "rfid" ("serialCode", "lockerNumber", "groupId", "branchId") WHERE (deleted IS NULL)`,
    );
  }
}
