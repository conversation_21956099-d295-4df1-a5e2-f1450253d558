import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateProductIsPassport1720710369753
  implements MigrationInterface
{
  name = 'UpdateProductIsPassport1720710369753';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "product" ADD "isPassport" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "product" DROP COLUMN "isPassport"`);
  }
}
