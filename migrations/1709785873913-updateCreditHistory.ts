import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCreditHistory1709785873913 implements MigrationInterface {
  name = 'UpdateCreditHistory1709785873913';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "credit_history" ADD "isMembershipPkg" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "credit_history" DROP COLUMN "isMembershipPkg"`,
    );
  }
}
