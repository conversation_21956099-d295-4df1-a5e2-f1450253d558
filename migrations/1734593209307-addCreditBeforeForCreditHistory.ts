import { MigrationInterface, QueryRunner } from "typeorm";

export class  AddCreditBeforeForCreditHistory1734593209307 implements MigrationInterface {
    name = 'AddCreditBeforeForCreditHistory1734593209307'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "credit_history" ADD "creditBefore" real`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "credit_history" DROP COLUMN "creditBefore"`);
    }

}
