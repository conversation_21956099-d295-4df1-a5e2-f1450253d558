import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateInvoiceCoupon1712562760437 implements MigrationInterface {
  name = 'UpdateInvoiceCoupon1712562760437';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "invoice_coupon" ALTER COLUMN "couponName" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "invoice_coupon" ALTER COLUMN "couponCode" DROP NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "invoice_coupon" ALTER COLUMN "couponCode" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "invoice_coupon" ALTER COLUMN "couponName" SET NOT NULL`);
  }
}
