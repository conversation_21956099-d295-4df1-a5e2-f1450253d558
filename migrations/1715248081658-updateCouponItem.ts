import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCouponItem1715248081658 implements MigrationInterface {
  name = 'UpdateCouponItem1715248081658';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "coupon_item" ADD "startDate" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon_item" ADD "expiryDate" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_detail" ADD "couponCode" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "order_detail" DROP COLUMN "couponCode"`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon_item" DROP COLUMN "expiryDate"`,
    );
    await queryRunner.query(
      `ALTER TABLE "coupon_item" DROP COLUMN "startDate"`,
    );
  }
}
