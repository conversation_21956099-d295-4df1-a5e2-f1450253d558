import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCodeOrderAutoGen1709782688502 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "order" DROP COLUMN "code"`);
    await queryRunner.query(`ALTER TABLE "order" ADD "code" bigint NOT NULL`);
    await queryRunner.query(`
            CREATE SEQUENCE order_code_seq
            START 10000001;
          `);
    await queryRunner.query(`
            ALTER TABLE "order"
            ALTER COLUMN "code" SET DEFAULT nextval('order_code_seq');
          `);
  }

  public async down(): Promise<void> {}
}
