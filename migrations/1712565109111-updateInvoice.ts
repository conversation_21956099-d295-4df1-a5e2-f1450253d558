import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateInvoice1712565109111 implements MigrationInterface {
  name = 'UpdateInvoice1712565109111';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "invoice" DROP COLUMN "discount"`);
    await queryRunner.query(`ALTER TABLE "invoice" ADD "discount" real DEFAULT '0'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "invoice" DROP COLUMN "discount"`);
    await queryRunner.query(`ALTER TABLE "invoice" ADD "discount" integer NOT NULL DEFAULT '0'`);
  }
}
