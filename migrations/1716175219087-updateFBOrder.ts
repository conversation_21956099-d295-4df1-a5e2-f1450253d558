import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateFBOrder1716175219087 implements MigrationInterface {
  name = 'UpdateFBOrder1716175219087';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."fb_order_producttype_enum" AS ENUM('product', 'service', 'membership', 'food', 'beverage', 'coupon', 'food-beverage')`);
    await queryRunner.query(`ALTER TABLE "fb_order" ADD "productType" "public"."fb_order_producttype_enum"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "fb_order" DROP COLUMN "productType"`);
    await queryRunner.query(`DROP TYPE "public"."fb_order_producttype_enum"`);
  }
}
