import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldRefundDtoCreditHistoryEntity1742807246362
  implements MigrationInterface
{
  name = 'AddFieldRefundDtoCreditHistoryEntity1742807246362';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "credit_history" ADD "refundDto" jsonb`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "credit_history" DROP COLUMN "refundDto"`,
    );
  }
}
