import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateOrder1709722287880 implements MigrationInterface {
  name = 'UpdateOrder1709722287880';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "order" ADD "subTotal" real DEFAULT '0'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "order" DROP COLUMN "subTotal"`);
  }
}
