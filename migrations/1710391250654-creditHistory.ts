import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreditHistory1710391250654 implements MigrationInterface {
  name = 'CreditHistory1710391250654';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "credit_history" ADD "opening" real`);
    await queryRunner.query(`ALTER TABLE "credit_history" ADD "closing" real`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "credit_history" DROP COLUMN "closing"`,
    );
    await queryRunner.query(
      `ALTER TABLE "credit_history" DROP COLUMN "opening"`,
    );
  }
}
