import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTagIdGenAuto1709722031892 implements MigrationInterface {
  name = 'UpdateTagIdGenAuto1709722031892';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "rfid" DROP COLUMN "tagId"`);
    await queryRunner.query(`ALTER TABLE "rfid" ADD "tagId" bigint NOT NULL`);
    await queryRunner.query(`
          CREATE SEQUENCE rfid_tagId_seq
          START 10000001;
        `);
    await queryRunner.query(`
          ALTER TABLE "rfid"
          ALTER COLUMN "tagId" SET DEFAULT nextval('rfid_tagId_seq');
        `);
  }

  public async down(): Promise<void> {}
}
