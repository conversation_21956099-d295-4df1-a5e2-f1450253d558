import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateStausFbOrder1713877259913 implements MigrationInterface {
  name = 'updateStausFbOrder1713877259913';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."fb_order_status_enum" RENAME TO "fb_order_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."fb_order_status_enum" AS ENUM('NEW', 'PENDING', 'COMPLETED', 'CANCEL')`,
    );
    await queryRunner.query(
      `ALTER TABLE "fb_order" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE fb_order ALTER COLUMN status DROP NOT NULL`,
    );
    await queryRunner.query(`UPDATE fb_order SET status = NULL`);
    await queryRunner.query(
      `ALTER TABLE "fb_order" ALTER COLUMN "status" TYPE "public"."fb_order_status_enum" USING "status"::"text"::"public"."fb_order_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "fb_order" ALTER COLUMN "status" SET DEFAULT 'NEW'`,
    );
    await queryRunner.query(`DROP TYPE "public"."fb_order_status_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."fb_order_status_enum_old" AS ENUM('PART_PAID', 'PAID', 'UNPAID')`,
    );
    await queryRunner.query(
      `ALTER TABLE "fb_order" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "fb_order" ALTER COLUMN "status" TYPE "public"."fb_order_status_enum_old" USING "status"::"text"::"public"."fb_order_status_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "fb_order" ALTER COLUMN "status" SET DEFAULT 'UNPAID'`,
    );
    await queryRunner.query(`DROP TYPE "public"."fb_order_status_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."fb_order_status_enum_old" RENAME TO "fb_order_status_enum"`,
    );
  }
}
