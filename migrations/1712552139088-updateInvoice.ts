import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateInvoice1712552139088 implements MigrationInterface {
  name = 'UpdateInvoice1712552139088';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "invoice" ADD "subTotal" real DEFAULT '0'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "invoice" DROP COLUMN "subTotal"`);
  }
}
