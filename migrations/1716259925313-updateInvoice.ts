import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateInvoice1716259925313 implements MigrationInterface {
  name = 'UpdateInvoice1716259925313';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "invoice" ADD "payload" jsonb`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "invoice" DROP COLUMN "payload"`);
  }
}
