import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCreditHistory1716172814298 implements MigrationInterface {
  name = 'UpdateCreditHistory1716172814298';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "credit_history" ADD "invoiceId" uuid`);
    await queryRunner.query(`ALTER TABLE "credit_history" ADD CONSTRAINT "FK_c4aada7f7c5006609ccda87ebc6" FOREIGN KEY ("invoiceId") REFERENCES "invoice"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "credit_history" DROP CONSTRAINT "FK_c4aada7f7c5006609ccda87ebc6"`);
    await queryRunner.query(`ALTER TABLE "credit_history" DROP COLUMN "invoiceId"`);
  }
}
