import { MigrationInterface, QueryRunner } from 'typeorm';

export class DeleteNameOfPerm1711511929404 implements MigrationInterface {
  name = 'DeleteNameOfPerm1711511929404';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "name"`);
    await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "code"`);
    await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "status"`);
    await queryRunner.query(`DROP TYPE "public"."permission_status_enum"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."permission_status_enum" AS ENUM('Active', 'Inactive')`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ADD "status" "public"."permission_status_enum" NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ADD "name" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ADD "code" character varying NOT NULL`,
    );
  }
}
