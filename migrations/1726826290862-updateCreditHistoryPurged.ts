import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCreditHistoryPurged1726826290862
  implements MigrationInterface
{
  name = 'UpdateCreditHistoryPurged1726826290862';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "credit_history" ADD "purged" real`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "credit_history" DROP COLUMN "purged"`,
    );
  }
}
