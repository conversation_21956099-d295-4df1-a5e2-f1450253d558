import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCreditHistory1714032396327 implements MigrationInterface {
  name = 'UpdateCreditHistory1714032396327';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "credit_history" ADD "isRefund" boolean NOT NULL DEFAULT false`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "credit_history" DROP COLUMN "isRefund"`);
  }
}
