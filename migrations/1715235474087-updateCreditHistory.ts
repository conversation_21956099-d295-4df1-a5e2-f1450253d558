import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCreditHistory1715235474087 implements MigrationInterface {
  name = 'UpdateCreditHistory1715235474087';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "credit_history" ADD "refundedById" uuid`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "credit_history" DROP COLUMN "refundedById"`);
  }
}
