import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateOrderDetail1712741245976 implements MigrationInterface {
  name = 'UpdateOrderDetail1712741245976';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "order_detail" ADD "fbOrderId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "order_detail" ADD CONSTRAINT "FK_6e037049baa3fde216e508a02e0" FOREIGN KEY ("fbOrderId") REFERENCES "fb_order"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "order_detail" DROP CONSTRAINT "FK_6e037049baa3fde216e508a02e0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "order_detail" DROP COLUMN "fbOrderId"`,
    );
  }
}
