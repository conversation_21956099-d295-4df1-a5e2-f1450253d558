import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePermission1725941158482 implements MigrationInterface {
  name = 'UpdatePermission1725941158482';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "permission" ADD "active" boolean`);
    await queryRunner.query(
      `ALTER TABLE "permission" ADD "changePass" boolean`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ADD "listCalendar" boolean`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" ADD "addAppointment" boolean`,
    );
    await queryRunner.query(`ALTER TABLE "permission" ADD "export" boolean`);
    await queryRunner.query(`ALTER TABLE "permission" ADD "sendMail" boolean`);
    await queryRunner.query(`ALTER TABLE "permission" ADD "checkOut" boolean`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "checkOut"`);
    await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "sendMail"`);
    await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "export"`);
    await queryRunner.query(
      `ALTER TABLE "permission" DROP COLUMN "addAppointment"`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" DROP COLUMN "listCalendar"`,
    );
    await queryRunner.query(
      `ALTER TABLE "permission" DROP COLUMN "changePass"`,
    );
    await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "active"`);
  }
}
