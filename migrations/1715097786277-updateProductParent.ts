import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateProductParent1715097786277 implements MigrationInterface {
  name = 'UpdateProductParent1715097786277';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "product" ADD "parentId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "product" ADD CONSTRAINT "FK_77e467b32f0a8b7a1e5503eecac" FOREIGN KEY ("parentId") REFERENCES "product"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "product" DROP CONSTRAINT "FK_77e467b32f0a8b7a1e5503eecac"`,
    );
    await queryRunner.query(`ALTER TABLE "product" DROP COLUMN "parentId"`);
  }
}
