import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateInvoiceCoupon1713942229210 implements MigrationInterface {
  name = 'UpdateInvoiceCoupon1713942229210';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "invoice_coupon" ADD "percent" real NOT NULL DEFAULT '0'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "invoice_coupon" DROP COLUMN "percent"`);
  }
}
