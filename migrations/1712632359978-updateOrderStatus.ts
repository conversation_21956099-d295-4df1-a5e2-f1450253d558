import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateOrderStatus1712632359978 implements MigrationInterface {
  name = 'UpdateOrderStatus1712632359978';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "order" RENAME COLUMN "statusId" TO "status"`,
    );
    await queryRunner.query(
      `ALTER TABLE "order" DROP CONSTRAINT IF EXISTS "order_statusId_fkey"`,
    );
    await queryRunner.query(`ALTER TABLE "order" DROP COLUMN "status"`);
    await queryRunner.query(
      `CREATE TYPE "public"."order_status_enum" AS ENUM('PART_PAID', 'PAID', 'UNPAID')`,
    );
    await queryRunner.query(
      `ALTER TABLE "order" ADD "status" "public"."order_status_enum" NOT NULL DEFAULT 'UNPAID'`,
    );
  }

  public async down(queryRunner: Query<PERSON>unner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "order" DROP COLUMN "status"`);
    await queryRunner.query(`DROP TYPE "public"."order_status_enum"`);
    await queryRunner.query(`ALTER TABLE "order" ADD "status" uuid`);
    await queryRunner.query(
      `ALTER TABLE "order" ADD CONSTRAINT "order_statusId_fkey" FOREIGN KEY ("statusId") REFERENCES "setting"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }
}
