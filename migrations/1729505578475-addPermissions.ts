import { MigrationInterface, QueryRunner } from "typeorm";

export class  AddPermissions1729505578475 implements MigrationInterface {
    name = 'AddPermissions1729505578475'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "permission" ADD "changePaymentSingleDay" boolean`);
        await queryRunner.query(`ALTER TABLE "permission" ADD "changePaymentAllDays" boolean`);
        await queryRunner.query(`ALTER TABLE "permission" ADD "editSingleDay" boolean`);
        await queryRunner.query(`ALTER TABLE "permission" ADD "editAllDays" boolean`);
        await queryRunner.query(`ALTER TABLE "permission" ADD "refund" boolean`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "refund"`);
        await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "editAllDays"`);
        await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "editSingleDay"`);
        await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "changePaymentAllDays"`);
        await queryRunner.query(`ALTER TABLE "permission" DROP COLUMN "changePaymentSingleDay"`);
    }

}
