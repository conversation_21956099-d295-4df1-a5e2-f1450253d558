import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateBranhForAuditTrail1715680744739
  implements MigrationInterface
{
  name = 'UpdateBranhForAuditTrail1715680744739';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "audit_trail" ADD "branchId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "audit_trail" ADD CONSTRAINT "FK_b8c9c546082ef9f0d00f668ea71" FOREIGN KEY ("branchId") REFERENCES "branch"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "audit_trail" DROP CONSTRAINT "FK_b8c9c546082ef9f0d00f668ea71"`,
    );
    await queryRunner.query(`ALTER TABLE "audit_trail" DROP COLUMN "branchId"`);
  }
}
