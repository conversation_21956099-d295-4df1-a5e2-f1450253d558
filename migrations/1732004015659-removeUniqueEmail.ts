import { MigrationInterface, QueryRunner } from "typeorm";

export class  RemoveUniqueEmail1732004015659 implements MigrationInterface {
    name = 'RemoveUniqueEmail1732004015659'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_4c58be1b83ae68eece02e51964"`);
        await queryRunner.query(`CREATE INDEX "IDX_4c58be1b83ae68eece02e51964" ON "customer" ("email") WHERE deleted IS NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_4c58be1b83ae68eece02e51964"`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_4c58be1b83ae68eece02e51964" ON "customer" ("email") WHERE (deleted IS NULL)`);
    }

}
