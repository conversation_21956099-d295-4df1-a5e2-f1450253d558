import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsCompleteForOrder1712637994888 implements MigrationInterface {
  name = 'AddIsCompleteForOrder1712637994888';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "order" ADD "isComplete" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "order" DROP COLUMN "isComplete"`);
  }
}
