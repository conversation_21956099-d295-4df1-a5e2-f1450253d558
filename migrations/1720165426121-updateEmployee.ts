import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateEmployee1720165426121 implements MigrationInterface {
  name = 'UpdateEmployee1720165426121';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "employee" ADD "order" bigint`);
    await queryRunner.query(
      `ALTER TABLE "employee" ADD "orderTimestamp" TIMESTAMP WITH TIME ZONE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "employee" DROP COLUMN "orderTimestamp"`,
    );
    await queryRunner.query(`ALTER TABLE "employee" DROP COLUMN "order"`);
  }
}
