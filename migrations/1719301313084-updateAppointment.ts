import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateAppointment1719301313084 implements MigrationInterface {
  name = 'UpdateAppointment1719301313084';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "appointment" ADD "order" bigint NULL`,
    );
    await queryRunner.query(`
            CREATE SEQUENCE appointment_order_seq
            START 1;
        `);
    await queryRunner.query(`
            ALTER TABLE "appointment"
            ALTER COLUMN "order" SET DEFAULT nextval('appointment_order_seq');
        `);
    await queryRunner.query(
      `ALTER TABLE "appointment" ADD "orderTimestamp" TIMESTAMP WITH TIME ZONE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "appointment" DROP COLUMN "orderTimestamp"`,
    );
    await queryRunner.query(`ALTER TABLE "appointment" DROP COLUMN "order"`);
  }
}
