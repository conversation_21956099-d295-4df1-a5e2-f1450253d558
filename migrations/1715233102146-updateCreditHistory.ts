import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCreditHistory1715233102146 implements MigrationInterface {
  name = 'UpdateCreditHistory1715233102146';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "credit_history" ADD "refundValue" real`);
    await queryRunner.query(`ALTER TABLE "credit_history" ADD "refundBeforeTax" real`);
    await queryRunner.query(`ALTER TABLE "credit_history" ADD "tax" real`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "credit_history" DROP COLUMN "tax"`);
    await queryRunner.query(`ALTER TABLE "credit_history" DROP COLUMN "refundBeforeTax"`);
    await queryRunner.query(`ALTER TABLE "credit_history" DROP COLUMN "refundValue"`);
  }
}
