import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateEmployeeCode1726816232604 implements MigrationInterface {
  name = 'UpdateEmployeeCode1726816232604';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "employee" ADD "code" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "employee" ADD CONSTRAINT "UQ_348a4a9894eef0760bfe0a26328" UNIQUE ("code")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "employee" DROP CONSTRAINT "UQ_348a4a9894eef0760bfe0a26328"`,
    );
    await queryRunner.query(`ALTER TABLE "employee" DROP COLUMN "code"`);
  }
}
