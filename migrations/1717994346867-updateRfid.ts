import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateRfid1717994346867 implements MigrationInterface {
  name = 'UpdateRfid1717994346867';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX IF EXISTS "public"."IDX_ec7d82d85b685910c09e827484"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "public"."unique_locker_index"`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "unique_locker_index" ON "rfid" ("branchId", "groupId", "lockerNumber", "serialCode") WHERE deleted IS NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."unique_locker_index"`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "unique_locker_index" ON "rfid" ("lockerNumber", "groupId", "branchId") WHERE (deleted IS NULL)`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_ec7d82d85b685910c09e827484" ON "rfid" ("serialCode") WHERE (deleted IS NULL)`,
    );
  }
}
