import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCodeTypeForCustomer1699670542136
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "customer" DROP COLUMN "code"`);
    await queryRunner.query(
      `ALTER TABLE "customer" ADD "code" bigint NOT NULL`,
    );
    await queryRunner.query(`
          CREATE SEQUENCE customer_code_seq
          START 10000001;
        `);
    await queryRunner.query(`
          ALTER TABLE "customer"
          ALTER COLUMN code SET DEFAULT nextval('customer_code_seq');
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "customer" DROP COLUMN "code"`);
    await queryRunner.query(
      `ALTER TABLE "customer" ADD "code" character varying`,
    );
  }
}
