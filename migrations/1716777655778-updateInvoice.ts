import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateInvoice1716777655778 implements MigrationInterface {
  name = 'UpdateInvoice1716777655778';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "invoice" ADD "voidDate" TIMESTAMP WITH TIME ZONE`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "invoice" DROP COLUMN "voidDate"`);
  }
}
