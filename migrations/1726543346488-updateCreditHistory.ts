import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCreditHistory1726543346488 implements MigrationInterface {
  name = 'UpdateCreditHistory1726543346488';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "credit_history" ADD "lastConsumed" TIMESTAMP WITH TIME ZONE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "credit_history" DROP COLUMN "lastConsumed"`,
    );
  }
}
