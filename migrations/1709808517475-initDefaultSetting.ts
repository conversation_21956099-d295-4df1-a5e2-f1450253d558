import { MigrationInterface, QueryRunner } from 'typeorm';
import { genUUID } from 'src/core/crypto/crypto.provider';
import { genInsertQuery } from 'src/core/database/gen-insert-query.parse';
import * as moment from 'moment';
export class InitDefaultSetting1709808517475 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const existCommission = await queryRunner.query(
      `SELECT * FROM commission_setting`,
    );
    if (existCommission.length === 0) {
      const queryCommissionSetting = genInsertQuery('commission_setting', [
        {
          id: genUUID('commission', 'commission_setting'),
          employee: 15,
          frontdesk: 10,
          fBStaff: 10,
        },
      ]);
      await queryRunner.query(queryCommissionSetting);
    }

    const existPayslip = await queryRunner.query(
      `SELECT * FROM payslip_setting`,
    );
    if (existPayslip.length === 0) {
      const queryPayslipSetting = genInsertQuery('payslip_setting', [
        {
          id: genUUID('payslip', 'payslip_setting'),
          basicSalary: 20,
          oTPay: 20,
          allowance: 200,
        },
      ]);
      await queryRunner.query(queryPayslipSetting);
    }

    const existPaymentMethod = await queryRunner.query(
      `SELECT * FROM payment_method`,
    );
    if (existPaymentMethod.length === 0) {
      const queryPaymentMethod = genInsertQuery('payment_method', [
        {
          id: genUUID('master', 'method'),
          name: 'Master',
          code: 'master',
          order: 6,
          statusId: genUUID(`status-Active`, 'Setting'),
        },
        {
          id: genUUID('old_credits', 'method'),
          name: 'Old Credits',
          code: 'old_credits',
          order: 2,
          statusId: genUUID(`status-Active`, 'Setting'),
        },
        {
          id: genUUID('nets', 'method'),
          name: 'Nets',
          code: 'nets',
          order: 4,
          statusId: genUUID(`status-Active`, 'Setting'),
        },
        {
          id: genUUID('paynow', 'method'),
          name: 'Paynow',
          code: 'paynow',
          order: 8,
          statusId: genUUID(`status-Active`, 'Setting'),
        },
        {
          id: genUUID('lazada', 'method'),
          name: 'Lazada',
          code: 'lazada',
          order: 10,
          statusId: genUUID(`status-Active`, 'Setting'),
        },
        {
          id: genUUID('credits', 'method'),
          name: 'Credits',
          code: 'credits',
          order: 1,
          statusId: genUUID(`status-Active`, 'Setting'),
        },
        {
          id: genUUID('cash', 'method'),
          name: 'Cash',
          code: 'cash',
          order: 3,
          statusId: genUUID(`status-Active`, 'Setting'),
        },
        {
          id: genUUID('shopify', 'method'),
          name: 'Shopify',
          code: 'shopify',
          order: 9,
          statusId: genUUID(`status-Active`, 'Setting'),
        },
        {
          id: genUUID('amex', 'method'),
          name: 'AMEX',
          code: 'amex',
          order: 7,
          statusId: genUUID(`status-Active`, 'Setting'),
        },
        {
          id: genUUID('visa', 'method'),
          name: 'Visa',
          code: 'visa',
          order: 5,
          statusId: genUUID(`status-Active`, 'Setting'),
        },
      ]);
      await queryRunner.query(queryPaymentMethod);
    }

    const existCreditSetting = await queryRunner.query(
      `SELECT * FROM credit_setting`,
    );
    if (existCreditSetting.length === 0) {
      const queryCreditSetting = genInsertQuery('credit_setting', [
        {
          id: genUUID('credits', 'setting'),
          name: 'Credits',
          creditType: 'credits',
          price: 1,
          credit: 1,
          applyFromTheDate: moment().utcOffset('+00'),
          statusId: genUUID(`status-Active`, 'Setting'),
        },
        {
          id: genUUID('old_credits', 'setting'),
          name: 'Credit Old',
          creditType: 'old_credits',
          price: 3,
          credit: 2,
          applyFromTheDate: moment().utcOffset('+00'),
          statusId: genUUID(`status-Active`, 'Setting'),
        },
      ]);

      await queryRunner.query(queryCreditSetting);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
