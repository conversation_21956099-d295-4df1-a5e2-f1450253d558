import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateAuditTrail1715668535249 implements MigrationInterface {
  name = 'CreateAuditTrail1715668535249';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "audit_trail" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "operation" character varying NOT NULL, "description" text, "startTime" TIMESTAMP WITH TIME ZONE, "endTime" TIMESTAMP WITH TIME ZONE, "orderId" uuid, "invoiceId" uuid, "createdById" uuid, CONSTRAINT "PK_91aade8e45ada93f7dc98ca7ced" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_651e9353cbfef39df26e78a278" ON "audit_trail" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_df7426201d26be3006a98871cf" ON "audit_trail" ("updated") `,
    );
    await queryRunner.query(
      `ALTER TABLE "audit_trail" ADD CONSTRAINT "FK_8ee55cb6f7471d7931292108a9c" FOREIGN KEY ("orderId") REFERENCES "order"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "audit_trail" ADD CONSTRAINT "FK_18904e7d15d908519547f606937" FOREIGN KEY ("invoiceId") REFERENCES "invoice"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "audit_trail" ADD CONSTRAINT "FK_ad39e388e60f6d3c7b45b109c7c" FOREIGN KEY ("createdById") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "audit_trail" DROP CONSTRAINT "FK_ad39e388e60f6d3c7b45b109c7c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "audit_trail" DROP CONSTRAINT "FK_18904e7d15d908519547f606937"`,
    );
    await queryRunner.query(
      `ALTER TABLE "audit_trail" DROP CONSTRAINT "FK_8ee55cb6f7471d7931292108a9c"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_df7426201d26be3006a98871cf"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_651e9353cbfef39df26e78a278"`,
    );
    await queryRunner.query(`DROP TABLE "audit_trail"`);
  }
}
