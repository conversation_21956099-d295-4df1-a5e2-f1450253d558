import { RoleLevel } from 'src/admin/role/role.entity';
import { genUUID } from 'src/core/crypto/crypto.provider';
import { genInsertQuery } from 'src/core/database/gen-insert-query.parse';
import { RoleBuiltIn } from 'src/core/database/type';
import { MigrationInterface, QueryRunner } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import * as bcrypt from 'bcryptjs';

export class InitRoleAndUser1699637385126 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const query = genInsertQuery('role', [
      {
        id: RoleBuiltIn.SUPER_ADMIN,
        name: 'SUPERADMIN',
        level: RoleLevel.SUPER_ADMIN,
        code: 'super_admin',
        statusId: genUUID(`status-Active`, 'Setting'),
      },
    ]);

    await queryRunner.query(query);

    const superAdminId = genUUID('superadmin', 'User');
    const userQuery = genInsertQuery('user', [
      {
        id: genUUID('superadmin', 'User'),
        email: '<EMAIL>',
        username: 'superadmin',
        phone: '**********',
        roleId: RoleBuiltIn.SUPER_ADMIN,
      },
    ]);
    const authQuery = genInsertQuery('auth', [
      {
        id: uuidv4(),
        userId: superAdminId,
        password: bcrypt.hashSync('123456', bcrypt.genSaltSync(10)),
      },
    ]);

    await queryRunner.query(userQuery);
    await queryRunner.query(authQuery);
  }

  public async down(): Promise<void> {
    //none
  }
}
