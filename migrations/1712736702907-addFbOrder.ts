import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFbOrder1712736702907 implements MigrationInterface {
  name = 'AddFbOrder1712736702907';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."fb_order_status_enum" AS ENUM('PART_PAID', 'PAID', 'UNPAID')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."fb_order_ordertype_enum" AS ENUM('others', 'membership', 'food-beverage')`,
    );
    await queryRunner.query(
      `CREATE TABLE "fb_order" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted" TIMESTAMP WITH TIME ZONE, "__v" integer NOT NULL DEFAULT '0', "code" bigint NOT NULL, "note" character varying, "discount" integer NOT NULL DEFAULT '0', "isDraft" boolean NOT NULL DEFAULT false, "isPrinted" boolean NOT NULL DEFAULT false, "status" "public"."fb_order_status_enum" NOT NULL DEFAULT 'UNPAID', "tax" real DEFAULT '0', "total" real DEFAULT '0', "subTotal" real DEFAULT '0', "totalBeforeTax" real DEFAULT '0', "discountProductId" character varying, "discountMoney" real DEFAULT '0', "couponCode" character varying, "payload" jsonb, "couponName" character varying, "orderType" "public"."fb_order_ordertype_enum" NOT NULL DEFAULT 'others', "isComplete" boolean NOT NULL DEFAULT false, "appointmentId" uuid, "invoiceId" uuid, "employeeId" uuid, "branchId" uuid, CONSTRAINT "PK_e8b0a3f09942ea7a8107ccc1c3f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_378a7c2b0651758107507023bd" ON "fb_order" ("created") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_72534cfd3648d1f43be79a0a76" ON "fb_order" ("updated") `,
    );
    await queryRunner.query(
      `ALTER TABLE "fb_order" ADD CONSTRAINT "FK_2f3d429ed04b4edbf8f0a4c2309" FOREIGN KEY ("appointmentId") REFERENCES "appointment"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "fb_order" ADD CONSTRAINT "FK_7d0c02e02c2d8d99e8fc1784493" FOREIGN KEY ("invoiceId") REFERENCES "invoice"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "fb_order" ADD CONSTRAINT "FK_9ef6998d07875b3bf55062ca07f" FOREIGN KEY ("employeeId") REFERENCES "employee"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "fb_order" ADD CONSTRAINT "FK_61e32c77dd5720440dafa43aa34" FOREIGN KEY ("branchId") REFERENCES "branch"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `CREATE SEQUENCE IF NOT EXISTS "fb_order_code_seq" START 10000001 OWNED BY "fb_order"."code"`,
    );
    await queryRunner.query(
      `ALTER TABLE "fb_order" ALTER COLUMN "code" SET DEFAULT nextval('"fb_order_code_seq"')`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "fb_order" DROP CONSTRAINT "FK_61e32c77dd5720440dafa43aa34"`,
    );
    await queryRunner.query(
      `ALTER TABLE "fb_order" DROP CONSTRAINT "FK_9ef6998d07875b3bf55062ca07f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "fb_order" DROP CONSTRAINT "FK_7d0c02e02c2d8d99e8fc1784493"`,
    );
    await queryRunner.query(
      `ALTER TABLE "fb_order" DROP CONSTRAINT "FK_2f3d429ed04b4edbf8f0a4c2309"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_72534cfd3648d1f43be79a0a76"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_378a7c2b0651758107507023bd"`,
    );
    await queryRunner.query(`DROP TABLE "fb_order"`);
    await queryRunner.query(`DROP TYPE "public"."fb_order_ordertype_enum"`);
    await queryRunner.query(`DROP TYPE "public"."fb_order_status_enum"`);
  }
}
