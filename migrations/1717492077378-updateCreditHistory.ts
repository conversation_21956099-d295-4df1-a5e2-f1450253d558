import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCreditHistory1717492077378 implements MigrationInterface {
  name = 'UpdateCreditHistory1717492077378';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "credit_history" ADD "isPurgeExpired" boolean NOT NULL DEFAULT false`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "credit_history" DROP COLUMN "isPurgeExpired"`,
    );
  }
}
