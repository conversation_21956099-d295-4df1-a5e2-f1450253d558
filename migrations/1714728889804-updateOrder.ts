import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateOrder1714728889804 implements MigrationInterface {
  name = 'UpdateOrder1714728889804';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "order" ADD "isEdited" boolean NOT NULL DEFAULT false`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "order" DROP COLUMN "isEdited"`);
  }
}
