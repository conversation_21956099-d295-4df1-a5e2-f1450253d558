import { MigrationInterface, QueryRunner } from "typeorm";

export class  AddTypeForAppointment1733281706530 implements MigrationInterface {
    name = 'AddTypeForAppointment1733281706530'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."appointment_type_enum" AS ENUM('appointment', 'break-time', 'block-time')`);
        await queryRunner.query(`ALTER TABLE "appointment" ADD "type" "public"."appointment_type_enum" NOT NULL DEFAULT 'appointment'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "appointment" DROP COLUMN "type"`);
        await queryRunner.query(`DROP TYPE "public"."appointment_type_enum"`);
    }

}
