version: '3.9'

services:
  postgres: 
    image: postgres:15.2
    ports:
      - 54321:5432
    environment: 
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_DB=${DB_NAME}

  onsen-backend:
    depends_on:
      - postgres
    build:
      context: ./
      dockerfile: ./Dockerfile
    logging:
      options:
        max-size: '5m'
    restart: unless-stopped
    volumes:
      - ./:/usr/src/app:cached
    ports:
      - 3003:3000
    env_file:
      - .env

