# STAGE BUILD PROJECT
FROM node:18-alpine
RUN apk --no-cache add ca-certificates nano bash python3 make g++

# Create app directory
WORKDIR /usr/src/app

# Install production modules
COPY package*.json ./
RUN npm install

# Copy sourcecode for building
COPY . .
RUN npm run build
# RUN mv ./dist/* /usr/src/app


# Add Entrypoint
COPY ./entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

# DONE
EXPOSE 3000/tcp
CMD [ "/bin/bash", "/usr/local/bin/entrypoint.sh" ]