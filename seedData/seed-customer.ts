import { genUUID } from 'src/core/crypto/crypto.provider';
import { genInsertQuery } from 'src/core/database/gen-insert-query.parse';
import { SpecializaitonBuiltIn } from 'src/core/database/type';
import { MigrationInterface, QueryRunner } from 'typeorm';
import * as xlsx from 'xlsx';
import path from 'path';

export class SeedWorkers1688652701929 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    const workbook = xlsx.readFile(path + './customers.xlsx');
    const first_worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const data = xlsx.utils.sheet_to_json(first_worksheet, {
      header: 1,
      defval: '',
    });
    const mapNameSpecializationIds = {};
    const mapWorkerSpecializationIds = {};

    const specializations = [
      {
        id: SpecializaitonBuiltIn.Drivers,
        name: 'Drivers',
      },
      {
        id: SpecializaitonBuiltIn.Welders,
        name: 'Welders',
      },
      {
        id: SpecializaitonBuiltIn.ScaffoldErector,
        name: 'Scaffold Erector',
      },
      {
        id: SpecializaitonBuiltIn.ScaffoldSup,
        name: 'Scaffold Sup',
      },
      {
        id: SpecializaitonBuiltIn.BCSS,
        name: 'BCSS',
      },
      {
        id: SpecializaitonBuiltIn.MWAH,
        name: 'MWAH',
      },
      {
        id: SpecializaitonBuiltIn.WAHSup,
        name: 'WAH Sup',
      },
      {
        id: SpecializaitonBuiltIn.LiftingSup,
        name: 'Lifting Sup',
      },
      {
        id: SpecializaitonBuiltIn.RiggerSignal,
        name: 'Rigger & Signal',
      },
      {
        id: SpecializaitonBuiltIn.FireWatch,
        name: 'Fire Watch',
      },
      {
        id: SpecializaitonBuiltIn.SMO,
        name: 'SMO',
      },
      {
        id: SpecializaitonBuiltIn.ConfinedSpace,
        name: 'Confined Space',
      },
      {
        id: SpecializaitonBuiltIn.ProcessPlant,
        name: 'Process Plant',
      },
      {
        id: SpecializaitonBuiltIn.Electrical,
        name: 'Electrical',
      },
      {
        id: SpecializaitonBuiltIn.ScissorLift,
        name: 'Scissor Lift',
      },
      {
        id: SpecializaitonBuiltIn.Any,
        name: 'Any',
      },
    ];

    for (const i of data[0] as string[]) {
      for (const j of specializations) {
        if (j.name === i) {
          mapNameSpecializationIds[i] = j.id;
        }
      }
    }

    const listWorkers = [];
    const listTeams = new Set();
    const listVehicles = new Set();

    data.slice(1, data.length).forEach((row: string[]) => {
      const w = {
        name: row[0],
        contact: `${row[1].toString().replace(' ', '')}`,
        teamName: row[2],
      };
      const specializations = [];
      row.slice(4, row.length - 1).forEach((rData, index) => {
        if (+rData === 1) {
          specializations.push({
            id: mapNameSpecializationIds[data[0][index + 4]],
            name: data[0][index + 4],
          });
        }
      });
      mapWorkerSpecializationIds[row[0]] = specializations;
      listWorkers.push(w);

      listTeams.add(row[2]);
      if (row[row.length - 1] !== '') {
        listVehicles.add(row[row.length - 1]);
      }
    });

    const mapTeamIds = {};
    const initTeams = [];
    const initVehicles = [];

    Array.from(listVehicles).forEach((v: string, index) => {
      initVehicles.push({
        license: v,
      });
    });
    const createVehicles = genInsertQuery('vehicle', initVehicles);
    await queryRunner.query(createVehicles);

    Array.from(listTeams).forEach((t: string, index) => {
      const teamId = genUUID(t, 'job_type');
      initTeams.push({
        id: teamId,
        name: t,
        color_code: '#cbb9b9',
      });
      mapTeamIds[t] = teamId;
    });
    const createTeams = genInsertQuery('job_type', initTeams);
    await queryRunner.query(createTeams);
    // teams.forEach((t) => {
    //   mapTeamIds[t.name] = t.id;
    // });
    const initWorkerSpecialization = [];

    const newListWorkers = [];
    listWorkers.map((w) => {
      const workerId = genUUID(w.name, 'worker');
      for (const s of mapWorkerSpecializationIds[w.name]) {
        initWorkerSpecialization.push({
          workerId: workerId,
          specializationId: s.id,
        });
      }

      newListWorkers.push({
        id: workerId,
        name: w.name,
        contact: w.contact,
        jobTypeId: mapTeamIds[w.teamName],
      });
    });

    const createWorker = genInsertQuery('worker', newListWorkers);
    await queryRunner.query(createWorker);

    const query = genInsertQuery(
      'worker_specializations',
      initWorkerSpecialization,
    );
    await queryRunner.query(query);

    // workers.forEach((w) => {
    //   for (const s of mapWorkerSpecializationIds[w.name]) {
    //   }
    //   initWorkerSpecialization.push({
    //     workerId: 'ASDF',
    //     specializationId: 'asdf',
    //   });
    // });
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
