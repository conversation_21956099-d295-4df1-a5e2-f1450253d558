import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { map } from 'lodash';

import { Observable } from 'rxjs';
import { CrudRequest } from '../crud/crud';
import { PARSED_CRUD_REQUEST_KEY } from '../crud/crud/constants';

@Injectable()
export class FilterHeaderBranchInterceptor implements NestInterceptor {
  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const req = context.switchToHttp().getRequest();
    const headers = req.headers;
    const crudRequest: CrudRequest = req?.[PARSED_CRUD_REQUEST_KEY];
    const branchIds = headers?.branchid?.split(',') || [];
    if (branchIds.length > 0) {
      // this.crudReq(crudRequest).in('branches.id', branchIds).push();
    }
    return next.handle().pipe();
  }

  private crudReq(crudRequest) {
    let cond;
    return {
      in: function (field, values) {
        cond = {
          [field]: {
            $in: values,
          },
        };
        return this;
      },
      eq: function (field, value) {
        cond = {
          [field]: {
            $eq: value,
          },
        };
        return this;
      },
      push: () => crudRequest.parsed.search.$and.push(cond),
    };
  }
}
