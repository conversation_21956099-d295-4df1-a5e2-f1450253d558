import { HttpException } from '@nestjs/common';
import { CommonHttpStatus } from 'src/core/common/common-http.status';

export class ReferencedEntryDeletionException extends HttpException {
  constructor(resource: string) {
    let message = `Referenced Entry Deletion ${resource}`;
    if (resource === 'issue_coupon') {
      message = 'The coupon is being issued';
    }
    super(
      {
        message,
      },
      CommonHttpStatus.BAD_REQUEST,
    );
  }
}
