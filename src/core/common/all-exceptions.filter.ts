import {
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { BaseExceptionFilter } from '@nestjs/core';
import { CommonHttpStatus } from './common-http.status';

@Catch()
export class AllExceptionsFilter extends BaseExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const statusCode =
      exception instanceof HttpException
        ? exception.getStatus()
        : exception?.['statusCode'] || HttpStatus.INTERNAL_SERVER_ERROR;

    const responseError =
      exception instanceof HttpException ? exception.getResponse() : {};
    const message = responseError?.['message'];

    if (statusCode === CommonHttpStatus.NOT_ACCEPTABLE) {
      response.status(statusCode).json([
        {
          field: exception?.['response']?.['args']?.['key'].replaceAll(
            /\"/g,
            '',
          ),
          constraints: message.replace(
            '%{key}',
            exception?.['response']?.['args']?.['key'].replaceAll(/\"/g, ''),
          ),
        },
      ]);
    } else if (statusCode !== 400) {
      response.status(statusCode).json({
        statusCode,
        message: Array.isArray(message)
          ? message[0]
          : exception?.['response']?.['args']?.['key'].replaceAll(/\"/g, '')
          ? [
              {
                field: exception?.['response']?.['args']?.['key'].replaceAll(
                  /\"/g,
                  '',
                ),
                constraints: message.replace(
                  '%{key}',
                  exception?.['response']?.['args']?.['key'].replaceAll(
                    /\"/g,
                    '',
                  ),
                ),
              },
            ]
          : message?.replace(
              '%{key}',
              exception?.['response']?.['args']?.['key'].replaceAll(/\"/g, ''),
            ),
      });
    }

    super.catch(exception, host);
  }
}
