import { ValidationError } from 'class-validator';

export function customValidationExceptionFactory(
  errors: ValidationError[],
): any {
  const customValidationErrors = (errors: ValidationError[]) => {
    return errors.map((error: ValidationError) => {
      let constraints = null;
      if (error.children) {
        constraints = customValidationErrors(error.children);
      }
      return {
        field: error.property,
        constraints: error.constraints ? Object.values(error.constraints)[0] : constraints,
      };
    });
  };
  const processedErrors = customValidationErrors(errors);

  return {
    statusCode: 400,
    message: processedErrors,
  };
}
