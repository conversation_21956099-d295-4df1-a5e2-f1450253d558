import { Inject, Injectable } from '@nestjs/common';
import { CONFIG_OPTIONS } from './constants';
import { EnvConfig } from './interfaces';

@Injectable()
export class ConfigService {
  private envConfig: EnvConfig;

  constructor(@Inject(CONFIG_OPTIONS) options: EnvConfig) {
    this.envConfig = options;
  }

  get<T = any>(key: string, defaultValue?: T): T | undefined {
    // get from process env
    const processValue = process.env[key];
    if (processValue !== undefined) {
      return processValue as unknown as T;
    }

    // get from internal value
    const splitPaths = key?.split('.');
    if (splitPaths?.length == 0) {
      return defaultValue;
    }

    let internalValue = this.envConfig;
    while (splitPaths?.[0] && internalValue) {
      internalValue = internalValue?.[splitPaths.shift()];
    }

    // const internalValue = this.envConfig[key];
    if (internalValue !== undefined) {
      return internalValue as unknown as T;
    }

    return defaultValue;
  }
}
