import { ApiProperty } from '@nestjs/swagger';
import {} from 'class-transformer';
import { Column, ManyToMany, JoinTable } from 'typeorm';
import { DocEntity } from './doc.entity';
import { IsOptional } from 'class-validator';
import { Branch } from 'src/admin/branch/branch.entity';

export abstract class GroupDocEntity extends DocEntity {
  @ManyToMany(() => Branch)
  @ApiProperty({ type: () => Branch, isArray: true })
  @JoinTable()
  branches: Branch[]; //NOTE:¸ When this list empty , mean membership set to all branches

  @Column({ type: 'boolean', default: false })
  @IsOptional()
  isOfAllBranch: boolean; // todo/improve: Find a good way to replace this. I have checked that can't filter when relation 'field branches' empty
}
