import * as moment from 'moment';
import { HOUR, MINUTE } from '../common/common.types';

export function timerToString(timer: { hour: HOUR; minute: MINUTE }) {
  return `${timer.hour}:${timer.minute}`;
}

export function isDateEqual(a: Date, b?: Date) {
  if (!b) {
    return false;
  }
  const diff = moment(a).diff(b);
  return diff === 0;
}

export function clampMaxDate(target: Date, max?: Date) {
  if (!max) {
    return target;
  }
  const diff = moment(target).diff(max);
  // target > max
  return diff > 0 ? max : target;
}

export function clampMinDate(target: Date, min?: Date) {
  if (!min) {
    return target;
  }
  const diff = moment(target).diff(min);
  // target < min
  return diff < 0 ? min : target;
}

export function clearDateTime(d: Date | string) {
  const c = new Date(d);
  c.setHours(0, 0, 0, 0);
  return c;
}
