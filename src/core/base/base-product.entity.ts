import { Column, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, OneToOne } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { User } from 'src/admin/user/user.entity';
import { GroupDocEntity } from './group-doc.entity';
import { Setting } from 'src/admin/setting/setting.entity';
import { Media } from 'src/admin/media/media.entity';

export abstract class BaseProduct extends GroupDocEntity {
  @Column()
  @ApiProperty()
  name: string;

  @Column({ type: 'real', default: 0 })
  @ApiPropertyOptional()
  cost?: number;

  @Column({ type: 'real', default: 0 })
  @ApiPropertyOptional()
  price?: number;

  @Column({ type: 'real', default: 0 })
  @ApiPropertyOptional()
  profit?: number;

  @Column({ type: 'real', nullable: true })
  @ApiPropertyOptional()
  costForMember?: number;

  @Column({ type: 'real', nullable: true })
  @ApiPropertyOptional()
  priceForMember?: number;

  @Column({ type: 'real', nullable: true })
  @ApiPropertyOptional()
  profitForMember?: number;

  @ApiProperty({ required: false })
  @OneToOne(() => Media, (media) => media.product)
  @JoinColumn()
  avatar: Media;

  @ManyToOne(() => Setting, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Setting })
  status: Setting;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  createdUser?: User;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  lastUpdatedUser?: User;
}
