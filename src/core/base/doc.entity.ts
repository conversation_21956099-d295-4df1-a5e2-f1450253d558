import { ApiProperty } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';
import {
  CreateDateColumn,
  DeleteDateColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  Index,
  VersionColumn,
} from 'typeorm';

export abstract class DocEntity {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  @ApiProperty({
    format: 'uuid',
  })
  id?: string;

  @CreateDateColumn({ type: 'timestamptz' })
  @ApiProperty({
    required: false,
    readOnly: true,
    type: 'string',
    format: 'date-time',
  })
  @Index()
  created?: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  @ApiProperty({
    required: false,
    readOnly: true,
    type: 'string',
    format: 'date-time',
  })
  @Index()
  updated?: Date;

  @DeleteDateColumn({ type: 'timestamptz' })
  @Exclude()
  deleted?: Date;

  @VersionColumn({
    default: 0,
  })
  @Exclude()
  __v?: number;
}
