import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class ExecutionTimeMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const start = Date.now();
    res.on('finish', () => {
      const end = Date.now();
      const executionTime = end - start;
      console.log(
        `\n@@@ REQUEST: ${req.path} \nMethod: ${
          req.method
        } \nQuery: ${JSON.stringify(req.query)} \nBody: ${JSON.stringify(
          req.body,
        )} \nTook: ${executionTime}ms`,
      );
    });
    next();
  }
}
