import e from 'express';

export enum CreditInputType {
  DEFAULT = 'default',
  CSV = 'csv',
}

export enum UserType {
  ADMIN = 'admin',
  END_USER = 'end_user',
  MANAGER = 'manager',
  PARTNER = 'partner',
  STAFF = 'staff',
}

export enum UserGender {
  FEMALE = 'Female',
  MALE = 'Male',
  OTHER = 'Other',
}

export enum GroupGender {
  FEMALE = 'Female',
  MALE = 'Male',
}

export enum RecordStatus {
  ACTIVE = 'Active',
  INACTIVE = 'Inactive',
}

export enum DiscountType {
  MONEY = 'Money',
  PERCENT = 'Percentage',
}

export enum CouponType {
  CODE = 'Code',
  MONEY = 'Money',
  PERCENT = 'Percentage',
}

export enum DayOfWeek {
  SUNDAY,
  MONDAY,
  TUESDAY,
  WEDNESDAY,
  THURSDAY,
  FRIDAY,
  SATURDAY,
}

export enum BookingStage {
  ARRIVED = 'arrived',
  BOOKED = 'booked',
  CANCELED = 'canceled',
  COMPLETED = 'completed',
  CONFIRMED = 'confirmed',
  MESSAGED = 'messaged',
  NEW = 'new',
  WALK_IN = 'walk-in',
  NO_SHOW = 'no-show',
  REJECTED = 'rejected',
  STARTED = 'started',
}

export enum BookingAction {
  BOOKED = 'booked',
  CHECKOUT = 'checkout',
  VOIDED = 'voided',
}

export enum ProductUnit {
  Ml = 'ml',
  GRAM = 'gram',
}

export enum StockOrderStage {
  CANCELLED = 'cancelled',
  RECEIVED = 'received',
  WAITING = 'waiting',
}

export enum StockOrderType {
  PURCHASE = 'purchase',
  TRANSFER = 'transfer',
}

export enum StockAdjustmentReason {
  ADJUSTMENT = 'adjustment',
  DAMAGED = 'damaged',
  DISABLED = 'disabled',
  INTERNAL_USE = 'internal-use',
  INVOICE_VOIDED = 'invoice-voided',
  LOST = 'lost',
  NEW_STOCK = 'new-stock',
  ORDER = 'order',
  OTHER = 'other',
  OUT_OF_DATE = 'out-of-date',
  REFUND = 'refund',
  RETURN = 'return',
  SALE = 'sale',
  TRANSFER = 'transfer',
}

export enum InvoiceStage {
  COMPLETED = 'completed',
  EXCHANGE = 'exchange',
  PART_PAID = 'part-paid',
  REFUND = 'refund',
  UNPAID = 'unpaid',
  VOIDED = 'voided',
}

export enum PaymentType {
  EXCHANGE = 'exchange',
  PAID = 'paid',
  REFUND = 'refund',
}

export enum PaymentMethod {
  AMEX = 'amex',
  BUNDLE = 'bundle',
  CASH = 'cash',
  CREDIT = 'credit',
  FAVEPAY = 'favepay',
  FOMO_PAYMENT = 'fomo-payment',
  GRABPAY = 'grabpay',
  MASTER = 'master',
  NETS = 'nets',
  OTHER = 'other',
  PAYLAH = 'paylah',
  PAYNOW = 'paynow',
  SHOPEEPAY = 'shopeepay',
  UNIONPAY = 'unionpay',
  VISA = 'visa',
}

export enum FomoOrderStatus {
  CREATED = 'CREATED',
  FAIL = 'FAIL',
  ERROR = 'ERROR',
  SUCCESS = 'SUCCESS',
  REFUND = 'REFUND',
  CLOSED = 'CLOSED',
  VOID = 'VOID',
}

export enum PeriodUnit {
  DAY = 'day',
  MONTH = 'month',
  YEAR = 'year',
}

export enum CheckoutItemType {
  BUNDLE_PAYMENT = 'bundlePayment',
  BUNDLE = 'bundle',
  CREDIT = 'credit',
  PRODUCT = 'product',
  SERVICE = 'service',
}

export enum PartnerLogMode {
  NEW = 'new',
  CHANGE = 'change',
  DELETE = 'delete',
}

export enum PartnerLogType {
  CUSTOMER = 'customer',
  BOOKING = 'booking',
}

export enum AdminLogType {
  ADD_ONE_MANAGER = 'add-one-manager',
  ADD_ONE_PARTNER = 'add-one-partner',
  DELETE_ONE_PARTNER = 'delete-one-partner',
  EDIT_ONE_PARTNER = 'edit-one-partner',
  EDIT_PARTNER_LICENCE = 'edit-partner-licence',
  LOGIN = 'login',
  UP_ONE_IMAGE = 'up-one-image',
  UP_SOME_IMAGES = 'up-some-images',
  VIEW_LIST_IMAGE = 'view-list-image',
  VIEW_LIST_PARTNER = 'view-list-partner',
  VIEW_ONE_PARTNER = 'view-one-partner',
  VIEW_PARTNER_LICENCE = 'view-partner-licence',
}

export enum BookingStepTime {
  FIVE_MINUTE = 5,
  FIFTEEN_MINUTE = 15,
  PART_HOUR = 30,
  ONE_HOUR = 60,
}

export enum OnlineBookingPaymentMethod {
  FOMO = 'fomo',
  NO_PAYMENT = 'no-payment',
}

export enum MessageState {
  ERROR = 'error',
  SENDING = 'sending',
  SENT = 'sent',
}

export enum AppointmentStatus {
  BOOKING = 'BOOKING',
  ARRIVED = 'ARRIVED',
  REQUEST = 'REQUEST',
  FACIAL_IPL = 'FACIAL_IPL',
  REQUEST_ARRIVED = 'REQUEST_ARRIVED',
  CANCEL = 'CANCEL',
}

export enum InvoiceStatus {
  PART_PAID = 'PART_PAID',
  PAID = 'PAID',
  UNPAID = 'UNPAID',
  VOID = 'VOID',
}

export enum ImportLogType {
  CUSTOMER = 'customer',
}

export enum SalaryType {
  CPF = 'CPF',
  FWL = 'FWL',
}

export enum SalaryPaidType {
  BANK_DEPOSIT = 'bank-deposit',
  CASH = 'cash',
  CHEQUE = 'cheque',
}

export enum SMSAudienceType {
  CLIENTS_BY_BIRTHDAY = 'clients-by-birthday',
  LAPSED_CLIENTS = 'lapsed-clients',
  LOYAL_CLIENTS = 'loyal-clients',
  NEW_CLIENTS = 'new-clients',
  ONLINE_BOOKING_CLIENTS = 'online-booking-clients',
  RECENT_CLIENTS = 'recent-clients',
  TOP_SPENDING_CLIENTS = 'top-spending-clients',
}

export enum MaritalStatus {
  SINGLE = 'single',
  MARRIED = 'married',
}

export enum ProductType {
  PRODUCT = 'product',
  SERVICE = 'service',
  MEMBERSHIP = 'membership',
  FOOD = 'food',
  BEVERAGE = 'beverage',
  COUPON = 'coupon',
  FOOD_BEVERAGE = 'food-beverage',
}

export enum CreditType {
  OLD = 'old_credits',
  NEW = 'credits',
}

export enum CreditStatus {
  VALID = 'VALID',
  EXPIRED = 'EXPIRED',
}

export enum PrepaidFilter {
  EXPIRED_DATE = 'expired_date',
  LAST_CONSUMED = 'last_consumed',
  PURCHASE_DATE = 'purchase_date',
}

export enum OpeningClosingCreditType {
  SHOW_PERIOD = 'period',
  SHOW_CUSTOMER = 'customer',
  SHOW_INVOICE = 'invoice',
}

export enum SaleTax {
  TAX_9 = 9,
}

export enum OrderType {
  OTHERS = 'others',
  MEMBERSHIP = 'membership',
  FOOD_BEVERAGE = 'food-beverage',
  TRANSFER = 'transfer',
}

export enum FbOrderStatus {
  NEW = 'NEW',
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  CANCEL = 'CANCEL',
}

export enum Prefix {
  INVOICE = 'IN',
  ORDER = 'Order #',
}

export enum PriceType {
  NON_MEMBER = 'non-member',
  MEMBER = 'member',
  FOR_ALL = 'for-all',
}

export enum AppointmentType {
  APPOINTMENT = 'appointment',
  BREAK_TIME = 'break-time',
  BLOCK_TIME = 'block-time',
}
