export const genInsertQuery = (
  tableName: string,
  data: any[],
  ignore?: boolean,
) => {
  if (data?.length == 0) {
    throw new Error('Data is required');
  }

  // validating key
  const firstObjKeys = Object.keys(data[0] || {});
  data.map((row) => {
    const keys = Object.keys(row || {});
    const mismatchedKeys = 'Make sure all item shared the same keys set';

    if (keys?.length != firstObjKeys?.length) {
      throw new Error(mismatchedKeys);
    }
    keys.map((key) => {
      if (!firstObjKeys.includes(key)) {
        throw new Error(mismatchedKeys);
      }
    });
  });

  // parse key
  const keys = data.map((row) =>
    Object.keys(row || {})
      .map((i) => '"' + i + '"')
      .join(', '),
  );

  // parse value
  const values = data.map((row) =>
    Object.values(row || {})
      .map((i: string) =>
        i !== null ? "'" + i.toString().replace(/'/g, '`') + "'" : 'NULL',
      )
      .join(', '),
  );

  if (ignore) {
    return (
      'INSERT IGNORE INTO "' +
      tableName +
      '" ' +
      `(${keys[0]}) VALUES ${values.map((val) => `(${val})`).join(', ')};`
    );
  }

  return (
    'INSERT INTO "' +
    tableName +
    '" ' +
    `(${keys[0]}) VALUES ${values.map((val) => `(${val})`).join(', ')};`
  );
};
