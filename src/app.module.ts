import {
  <PERSON><PERSON><PERSON>,
  OnModuleInit,
  NestModule,
  DynamicModule,
  MiddlewareConsumer,
} from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from '@nestjs/config';
import { dataSourceOptions } from './core/database/typeorm.config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppAdminModules } from './admin/app-admin.module';
import { ConnectionOptions } from 'typeorm';
import { ExecutionTimeMiddleware } from './core/middlewares/executionTime.middleware';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({
      name: 'default',
      ...dataSourceOptions,
    }),
    ScheduleModule.forRoot(),
    AppAdminModules,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule implements OnModuleInit, NestModule {
  // constructor(private readonly devUtilService: DevUtilService) {}
  static forRoot(connOptions: ConnectionOptions): DynamicModule {
    return {
      module: AppModule,
      controllers: [AppController],
      imports: [TypeOrmModule.forRoot(connOptions)],
      providers: [AppService],
    };
  }
  async onModuleInit() {
    // update resources to db
    // await this.devUtilService.syncResource();
  }
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(ExecutionTimeMiddleware).forRoutes('*');
  }
}
