import { HttpAdapterHost, NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as pkg from '../package.json';
import { customValidationExceptionFactory } from './core/common/custom-validation-exception.factory';
import { AllExceptionsFilter } from './core/common/all-exceptions.filter';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, { cors: true });

  app.useGlobalPipes(
    new ValidationPipe({
      exceptionFactory: customValidationExceptionFactory,
    }),
  );
  app.enableCors();

  // Exception global custom
  const { httpAdapter } = app.get(HttpAdapterHost);
  app.useGlobalFilters(new AllExceptionsFilter(httpAdapter));

  // Api document
  const baseDocument = new DocumentBuilder()
    .setTitle(pkg.name)
    .setDescription(pkg.description)
    .setVersion(pkg.version)
    .addBearerAuth();

  const apiDocument = SwaggerModule.createDocument(app, baseDocument.build());

  SwaggerModule.setup('documents', app, apiDocument, {
    swaggerOptions: {
      docExpansion: 'none',
    },
  });

  await app.listen(+process.env.PORT || 3000);
}
bootstrap();
