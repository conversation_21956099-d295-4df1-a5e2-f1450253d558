<mjml>
  <mj-head>
    <mj-font name="Inter" href="https://fonts.googleapis.com/css?family=Inter" />
    <mj-attributes>
      <mj-text padding="0" />
      <mj-all font-family="Inter, sans-serif" />
      <mj-class name="base-text" font-size="12px" color="#000000" font-weight="400" />
      <mj-class name="heading" font-size="24px" color="#000000" font-weight="600" />
    </mj-attributes>
    <mj-style>
      .column-width {
      max-width: 700px !important;
      }
    </mj-style>
  </mj-head>
  <mj-body>
    <mj-section css-class="column-width" padding-top="0px" padding-bottom="48px">
      <mj-column>
        <mj-table>
          <tr>
            <td>
              <h2 style="text-align: center">Thank you for your order</h2>
            </td>
          </tr>
          <tr>
            <td style="text-align: center">
              We’ve received your payment and here is your coupon code
            </td>
          </tr>
        </mj-table>
      </mj-column>
    </mj-section>
    <mj-section css-class="column-width" padding-top="5px" padding-bottom="5px">
      <mj-column>
        <mj-table padding-top="5px" padding-bottom="5px">
          <tr>
            <th width="312px" style="color: #00000080; text-align: left;font-weight: normal; padding-bottom: 12px">Item</th>
            <th width="200px" style="color: #00000080; text-align: left;font-weight: normal; padding-bottom: 12px">ID code</th>
            <th width="100px" style="color: #00000080; text-align: left;font-weight: normal; padding-bottom: 12px">Expired date</th>
          </tr>
          {{#each coupons}}
            <tr>
              {{#if this.isFirst}}
                <td rowspan={{this.length}}>{{this.name}}</td>
              {{/if}}
              <td>{{this.code}}</td>
              <td>{{this.expiredDate}}</td>
            </tr>
            {{#if this.isRectangle}}
              <tr>
                <td colspan="3">
                  <div style="width: 100%; height: 0.5px; margin-top: 12px; margin-bottom: 12px; border-bottom: 0.5px dashed #00000080"></div>
                </td>
              </tr>
            {{/if}}
          {{/each}}
        </mj-table>
      </mj-column>
    </mj-section>
  </mj-body>
</mjml>