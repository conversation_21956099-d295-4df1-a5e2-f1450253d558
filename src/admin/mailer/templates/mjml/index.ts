import * as mjml from 'mjml';
import * as fs from 'fs';
import { SEND_COUPON_CODE } from './constant';

export const renderMJML = async (templateCode: string) => {
  try {
    let mjmlFile = '';
    const srcPath = '/src/admin/mailer/templates/mjml';
    switch (templateCode) {
      case SEND_COUPON_CODE:
        mjmlFile = process.cwd() + srcPath + '/payment-coupon-code.mjml';
        break;
      default:
        break;
    }

    const mjmlContent = fs.readFileSync(mjmlFile, 'utf8');
    const html = await mjml(mjmlContent);
    return html;
  } catch (error) {
    console.error(error);
  }
};
