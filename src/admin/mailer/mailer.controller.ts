import { <PERSON><PERSON>ataRequired } from '@sendgrid/mail';
import { Controller, Post, Body } from '@nestjs/common';
import { MailerService } from './mailer.service';
import { ApiOperation } from '@nestjs/swagger';

@Controller('mailer')
export class MailerController {
  constructor(private readonly mailerService: MailerService) {}

  async sendEmail(data: Omit<MailDataRequired, 'from'>) {
    this.mailerService.sendMail(data);
  }

  @Post('test')
  @ApiOperation({ summary: 'Test send email' })
  async testSendEmail(
    @Body() data: { to: string; subject: string; text?: string; html?: string },
  ) {
    return this.mailerService.sendMail({
      to: data.to,
      subject: data.subject,
      text: data.text || 'Test email',
      html: data.html || '<h1>This is a test email</h1>',
    });
  }
}
