import { Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import { ConfigService } from '@nestjs/config';
import handlebars from 'handlebars';
import { MailDataRequired } from '@sendgrid/mail';

@Injectable()
export class MailerService {
  private readonly transporter: nodemailer.Transporter;

  constructor(private readonly configService: ConfigService) {
    this.transporter = nodemailer.createTransport({
      host: this.configService.get<string>('SMTP_HOST'),
      port: this.configService.get<number>('SMTP_PORT'),
      secure: false,
      tls: {
        rejectUnauthorized: false,
        ciphers: 'SSLv3',
      },
      auth: {
        user: this.configService.get<string>('SMTP_USER'),
        pass: this.configService.get<string>('SMTP_PASS'),
      },
    });
  }

  private compileTemplate(html: string, context: any = {}) {
    const template = handlebars.compile(html);
    return template(context);
  }

  async sendMail(data: Omit<MailDataRequired, 'from'>) {
    const { to, subject, text, html } = data;

    return new Promise((resolve) => {
      this.transporter
        .sendMail({
          from: this.configService.get<string>('SMTP_SENDER'),
          to: Array.isArray(to)
            ? to.map((t) => (typeof t === 'string' ? t : t.email)).join(', ')
            : typeof to === 'string'
            ? to
            : to.email,
          subject,
          text,
          html,
        })
        .then(() => {
          resolve({ status: 'success' });
        })
        .catch((err) => {
          console.error('Send mail error:', err);
          resolve({ status: 'failed' });
        });
    });
  }

  async sendHtmlStringMail(
    data: { to: string | string[]; subject: string; text?: string },
    htmlContent: string,
    context: Record<string, any> = {},
  ) {
    return this.sendMail({
      ...data,
      html: this.compileTemplate(htmlContent, context),
    });
  }
}
