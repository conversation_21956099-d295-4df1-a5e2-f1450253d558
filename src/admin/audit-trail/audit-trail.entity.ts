import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { DocEntity } from 'src/core/base/doc.entity';
import { Column, Entity, ManyToOne } from 'typeorm';
import { User } from '../user/user.entity';
import { Order } from '../order/order.entity';
import { Invoice } from '../invoice/invoice.entity';
import { Branch } from '../branch/branch.entity';

export enum AuditTrailOperation {
  CHECKOUT = 'Check Out',
  ORDER = 'Order',
  EDIT = 'Edit',
  TRANSFER = 'Transfer',
}

@Entity()
export class AuditTrail extends DocEntity {
  @ManyToOne(() => Order, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Order })
  order: Order;

  @ManyToOne(() => Invoice, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Invoice })
  invoice: Invoice;

  @Column()
  @ApiProperty()
  operation: string;

  @Column({
    nullable: true,
    type: 'text',
  })
  @ApiPropertyOptional()
  description: string;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  startTime: Date;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  endTime: Date;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => User })
  createdBy: User;

  @ManyToOne(() => Branch, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Branch })
  branch: Branch;
}
