import { TypeOrmModule } from '@nestjs/typeorm';
import { AuditTrailController } from './audit-trail.controller';
import { AuditTrail } from './audit-trail.entity';
import { AuditTrailService } from './audit-trail.service';
import { Module } from '@nestjs/common';
import { CloudinaryModule } from 'src/core/cloudinary/cloudinary.module';

@Module({
  imports: [TypeOrmModule.forFeature([AuditTrail]), CloudinaryModule],
  controllers: [AuditTrailController],
  providers: [AuditTrailService],
  exports: [AuditTrailService],
})
export class AuditTrailModule {}
