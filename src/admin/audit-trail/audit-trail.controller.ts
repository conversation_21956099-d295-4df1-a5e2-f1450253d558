import { CrudController, CrudRequest, ParsedRequest } from 'src/core/crud/crud';
import { AuditTrail } from './audit-trail.entity';
import { AuditTrailService } from './audit-trail.service';
import { Post, UseInterceptors, UploadedFile, Body } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Express } from 'express';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';

@BaseCrud(
  {
    model: {
      type: AuditTrail,
    },
    routes: {
      only: ['getOneBase'],
    },
  },
  {},
)
export class AuditTrailController implements CrudController<AuditTrail> {
  constructor(public service: AuditTrailService) {}
  get base(): CrudController<AuditTrail> {
    return this;
  }
}
