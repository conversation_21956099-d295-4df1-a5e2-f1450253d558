import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { AuditTrail, AuditTrailOperation } from './audit-trail.entity';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { CloudinaryService } from 'src/core/cloudinary/cloudinary.service';
import { Product } from '../product/product.entity';
import * as moment from 'moment-timezone';
import { UTCtimeZone } from 'src/core/common/common.utils';

@Injectable()
export class AuditTrailService extends BaseCrudService<AuditTrail> {
  constructor(
    @InjectRepository(AuditTrail) repo: Repository<AuditTrail>,
    private readonly cloudinaryService: CloudinaryService,
  ) {
    super(repo);
  }

  async createRecordLog(
    queryRunner,
    {
      createdById,
      branchId,
      action,
      payload,
      startTime,
      newPayload,
      invoiceId,
      orderId,
      oldCustomerName,
      newCustomerName,
      createdByName,
    }: {
      createdById: string;
      branchId: string;
      action: string;
      startTime: string;
      payload: any;
      newPayload?: any;
      invoiceId?: string;
      orderId?: string;
      oldCustomerName?: string;
      newCustomerName?: string;
      createdByName?: string;
    },
  ) {
    const clientZone = startTime?.slice(-6) || moment().format('Z z');
    const time = moment
      .tz(startTime, clientZone)
      .tz(UTCtimeZone)
      .format('YYYY-MM-DDTHH:mm:ss.SSS');

    const description = ['Record was created'];
    const oldProducts = payload.map((item) => {
      return {
        id: item.product.id,
        quantity: item.quantity,
      };
    });
    const newProducts = newPayload.map((item) => {
      return {
        id: item?.product?.id,
        quantity: item.quantity,
      };
    });
    const { addedItems, removedItems } = this.compareProducts(
      oldProducts,
      newProducts,
    );

    const infoProducts = {};
    (
      (await queryRunner.manager.find(Product, {
        where: {
          id: In([
            ...addedItems.map((p) => p.id),
            ...removedItems.map((p) => p.id),
          ]),
        },
      })) || []
    ).forEach((product) => {
      infoProducts[product.id] = product;
    });

    switch (action) {
      case AuditTrailOperation.ORDER:
      case AuditTrailOperation.EDIT:
      case AuditTrailOperation.CHECKOUT:
        addedItems.forEach((item) => {
          description.push(
            `${item.quantity > 1 ? item.quantity + ' items' : 'Item'} ${
              infoProducts[item.id]?.name
            } was ${
              action === AuditTrailOperation.CHECKOUT ? 'paid' : 'added'
            }`,
          );
        });
        removedItems.forEach((item) => {
          description.push(
            `${item.quantity > 1 ? item.quantity + ' items' : 'Item'} ${
              infoProducts[item.id]?.name
            } was removed`,
          );
        });
        if (action === AuditTrailOperation.EDIT) {
          const exsistLogOrder = await queryRunner.manager.findOne(AuditTrail, {
            where: { order: { id: orderId } },
            relations: ['createdBy'],
            sort: [
              {
                field: 'created',
                order: 'DESC',
              },
            ],
          });
          if (exsistLogOrder && exsistLogOrder.createdBy?.id !== createdById) {
            description.push(
              `Modified By was changed from ${
                exsistLogOrder?.createdBy?.displayName ||
                exsistLogOrder?.createdBy?.fullname
              } to ${createdByName}`,
            );
          }
          if (exsistLogOrder) {
            description.push(
              `Modified  was changed from {{${exsistLogOrder?.startTime.toISOString()}}} to {{${time}Z}}`,
            );
          }
        }
        break;
      case AuditTrailOperation.TRANSFER:
        description.push(
          `Modified was changed from ${oldCustomerName} to ${newCustomerName}`,
        );
        description.push(
          `Modified by was changed by ${createdByName}`,
        );
        break;

      default:
        break;
    }

    return await queryRunner.manager.insert(AuditTrail, {
      invoice: invoiceId ? { id: invoiceId } : undefined,
      order: orderId ? { id: orderId } : undefined,
      operation: action,
      createdBy: { id: createdById },
      branch: { id: branchId },
      startTime: startTime,
      endTime: startTime,
      description: description.join(','),
    });
  }

  private compareProducts(oldProducts, newProducts) {
    const addedItems = [];
    const removedItems = [];

    // Map old products by id for faster lookup
    const oldProductsMap = oldProducts.reduce((acc, product) => {
      acc[product.id] = product;
      return acc;
    }, {});

    // Loop through new products
    for (const newProduct of newProducts) {
      const oldProduct = oldProductsMap[newProduct.id];

      if (!oldProduct) {
        addedItems.push(newProduct);
      } else {
        if (oldProduct.quantity > newProduct.quantity) {
          removedItems.push({
            id: oldProduct.id,
            quantity: oldProduct.quantity - newProduct.quantity,
          });
        } else if (oldProduct.quantity < newProduct.quantity) {
          addedItems.push({
            id: newProduct.id,
            quantity: newProduct.quantity - oldProduct.quantity,
          });
        }
        delete oldProductsMap[newProduct.id]; // Remove from map to track processed products
      }
    }

    // Any remaining products in oldProductsMap are removed
    for (const id in oldProductsMap) {
      removedItems.push(oldProductsMap[id]);
    }

    return {
      addedItems,
      removedItems,
    };
  }
}
