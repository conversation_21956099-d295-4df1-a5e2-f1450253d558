import {
  CrudController,
  CrudRequest,
  Override,
  ParsedBody,
  ParsedRequest,
} from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { OrderDetail } from './order-detail.entity';
import { OrderDetailService } from './order-detail.service';
import { OrderDetailDto } from './dto/createOrderDetail.dto';
import { Body, Delete, Param, Patch, Post, Query } from '@nestjs/common';
import { updateOrderDetailDto } from './dto/updateOrderDetailDto.dto';

@BaseCrud(
  {
    model: {
      type: OrderDetail,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    params: {
      employeeId: {
        field: 'employeeId',
        type: 'string',
      },
      orderDetailId: {
        field: 'OrderDetailId',
        type: 'uuid',
      },
    },
    dto: {
      create: OrderDetailDto,
      update: updateOrderDetailDto,
    },
    query: {
      join: {
        duration: { eager: true, allow: ['id', 'name'] },
        employees: { eager: true, allow: ['id', 'fullName', 'displayName'] },
        order: { eager: true, allow: ['id', 'items'] },
        product: { eager: true, allow: ['id', 'type', 'name'] },
      },
    },
  },
  {
    // grantPerm: userPerm,
    // group: ResourceGroup.SYSTEM,
  },
)
export class OrderDetailController extends BaseCrudController<OrderDetail> {
  constructor(public service: OrderDetailService) {
    super(service);
  }
  get base(): CrudController<OrderDetail> {
    return this;
  }

  @Delete('/:id/employee/:employeeId')
  async removeUserOutOfOrderDetail(
    @Param('id') orderDetailId: string,
    @Param('employeeId') employeeId: string,
    @ParsedRequest() CrudReq: CrudRequest,
  ) {
    await this.service.removeUserOutOfOrderDetail(
      CrudReq,
      orderDetailId,
      employeeId,
    );
  }

  @Patch('/:id/employee/:employeeId')
  async updateOrderDetail(
    @Param('id') orderDetailId: string,
    @Param('employeeId') employeeId: string,
    @ParsedRequest() CrudReq: CrudRequest,
    @Body() dto: any,
  ) {
    return await this.service.updateOrderDetail(
      CrudReq,
      orderDetailId,
      employeeId,
      dto,
    );
  }

  @Override('updateOneBase')
  async updateOne(
    @ParsedRequest() CrudReq: CrudRequest,
    @Body() dto: any,
    @Param('id') orderDetailId: string,
  ) {
    return await this.service.updateOrderDetailV2(
      CrudReq,
      orderDetailId,
      dto,
    );
  }

  @Override('deleteOneBase')
  async deleteOne(@ParsedRequest() CrudReq: CrudRequest) {
    await this.service.deleteOne(CrudReq);
  }

  @Override('getOneBase')
  async getOne(@Param('id') id: string) {
    console.log('id: ', id);
    
    return await this.service.getOneOrderDetail(id);
  }
}
