import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { OrderDetailController } from './order-detail.controller';
import { OrderDetail } from './order-detail.entity';
import { OrderDetailService } from './order-detail.service';
import { AppointmentModule } from '../appointment/appointment.module';
import { Employee } from '../employee/employee.entity';
import { Appointment } from '../appointment/appointment.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([OrderDetail, Employee, Appointment]),
    AppointmentModule,
  ],
  controllers: [OrderDetailController],
  providers: [OrderDetailService],
  exports: [OrderDetailService],
})
export class OrderDetailModule {}
