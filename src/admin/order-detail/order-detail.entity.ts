import { <PERSON>tity, Column, ManyToOne, ManyToMany, JoinTable } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { Setting } from '../setting/setting.entity';
import { Employee } from '../employee/employee.entity';
import { Order } from '../order/order.entity';
import { Product } from '../product/product.entity';
import { FbOrder } from '../fb-order/fb-order.entity';
import { AppointmentStatus } from 'src/core/enums/entity';

@Entity()
export class OrderDetail extends DocEntity {
  @Column({
    nullable: true,
  })
  @ApiPropertyOptional({ type: () => String })
  note?: string;

  @ManyToOne(() => Order, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Order })
  @IsNotEmpty()
  order: Order;

  @ManyToOne(() => FbOrder, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => FbOrder })
  @IsNotEmpty()
  fbOrder: FbOrder;

  @ManyToOne(() => Product, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Product })
  product: Product;

  @Column({ type: 'integer', default: 1 })
  @ApiProperty()
  quantity: number;

  @Column({ type: 'integer', default: 0 })
  @ApiPropertyOptional()
  discount?: number;

  @Column({ type: 'real', default: 0 })
  @ApiPropertyOptional()
  price?: number;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  startTime?: Date;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  endTime?: Date;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Setting })
  duration: Setting;

  @ManyToMany(() => Employee)
  @ApiPropertyOptional({ type: () => Employee, isArray: true })
  @JoinTable()
  employees?: Employee[];

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional({ type: () => String })
  couponCode?: string;

  expiryDate?: string;

  @Column({
    default: AppointmentStatus.BOOKING,
    type: 'enum',
    enum: Object.values(AppointmentStatus),
  })
  @ApiProperty()
  status: AppointmentStatus;
}
