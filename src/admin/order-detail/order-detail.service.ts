import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import { OrderDetail } from './order-detail.entity';
import { CrudRequest } from 'src/core/crud/crud';
import { isValidUUID } from 'src/core/crud/crud-request/request-query.validator';
import { AppointmentService } from '../appointment/appointment.service';
import { Employee } from '../employee/employee.entity';
import { Appointment } from '../appointment/appointment.entity';
import { Product } from '../product/product.entity';
import { Order } from '../order/order.entity';
import { AppointmentStatus } from 'src/core/enums/entity';

@Injectable()
export class OrderDetailService extends BaseCrudService<OrderDetail> {
  constructor(
    @InjectRepository(OrderDetail) repo: Repository<OrderDetail>,
    private readonly appointmentService: AppointmentService,
    @InjectRepository(Appointment)
    private appointmentRepo: Repository<Appointment>,
  ) {
    super(repo);
  }

  async removeUserOutOfOrderDetail(
    crudReq: CrudRequest,
    orderDetailId: string,
    employeeId: string,
  ) {
    try {
      const orderDetail = await this.repo.findOne({
        where: { id: orderDetailId },
        relations: ['employees'],
      });
      if (
        orderDetail.employees.length === 1 &&
        orderDetail.employees[0].id === employeeId
      ) {
        await this.repo.softDelete({ id: orderDetailId });
      }

      return await this.repo.manager.delete('order_detail_employees_employee', {
        orderDetailId,
        employeeId,
      });
    } catch (e) {
      console.log('e: ', e);
    }
  }

  async updateOrderDetail(
    crudReq: CrudRequest,
    orderDetailId: string,
    employeeId: string,
    dto: any,
  ) {
    const orderDetail = await this.repo.findOne({
      where: { id: orderDetailId },
      relations: ['employees', 'order', 'order.invoice', 'order.appointment'],
    });

    if (!orderDetail) {
      throw new NotFoundException('Order detail not found');
    }

    try {
      if (employeeId) {
        await this.repo.manager.delete('order_detail_employees_employee', {
          orderDetailId,
        });
        await this.repo.manager.insert('order_detail_employees_employee', {
          orderDetailId,
          employeeId,
        });
      }

      if (
        dto.employees &&
        Array.isArray(dto.employees) &&
        dto.employees.length > 0
      ) {
        for (const employeeData of dto.employees) {
          const employee = await this.repo.manager.findOne(Employee, {
            where: { id: employeeData.id },
          });

          if (!employee) {
            throw new BadRequestException(
              `Employee with ID ${employeeData.id} not found`,
            );
          }

          const validTime =
            await this.appointmentService.getRecordsWithOverlappingHours(
              employee.id,
              dto.startTime,
              dto.endTime,
              orderDetail.order.id,
              orderDetail.id,
            );

          if (validTime.totalTask >= 3) {
            throw new BadRequestException(
              `This time frame of ${
                employee.displayName || employee.fullName
              } is full. Please choose another time frame!`,
            );
          }

          if (employeeId !== employee.id) {
            await this.repo.manager.insert('order_detail_employees_employee', {
              orderDetailId,
              employeeId: employee.id,
            });
          }
        }
      }

      delete dto.employees;

      if (orderDetail.order?.appointment?.id && dto.startTime && dto.endTime) {
        await this.appointmentRepo.save({
          id: orderDetail.order.appointment.id,
          startTime: dto.startTime,
          endTime: dto.endTime,
        });

        if (orderDetail.order.appointment?.checkOut === null) {
          await this.repo.manager.transaction(async (trans) => {
            const productInfo = await trans.findOne(Product, {
              where: { id: dto?.product?.id },
            });

            if (productInfo) {
              const totalBeforeTax = productInfo.price * 1;
              const subTotal = parseFloat(totalBeforeTax.toFixed(2));
              const total = parseFloat(totalBeforeTax.toFixed(2));

              await trans.update(
                OrderDetail,
                { id: orderDetailId },
                {
                  price: productInfo.price,
                  product: { id: productInfo.id },
                },
              );

              await trans.save(Order, {
                id: orderDetail.order.id,
                total,
                totalBeforeTax,
                subTotal,
              });

              const orderUpdateRecord = await trans.findOne(Order, {
                where: { id: orderDetail.order.id },
                relations: ['items', 'items.product'],
              });

              await trans.update(
                Order,
                { id: orderDetail.order.id },
                { payload: orderUpdateRecord },
              );
            }
          });
        }
      }

      return await this.repo.update({ id: orderDetailId }, dto);
    } catch (e) {
      console.error('Error updating order detail:', e.message);
      throw new BadRequestException('Failed to update order detail');
    }
  }

  async updateOrderDetailV2(
    crudReq: CrudRequest,
    orderDetailId: string,
    dto: any,
  ) {
    try {
      const orderDetail = await this.repo.findOne({
        where: { id: orderDetailId },
        relations: ['employees', 'order', 'order.invoice', 'order.appointment'],
      });

      let newEmployees = [];
      let oldEmployees = [];

      if (dto?.employees && dto?.employees?.length > 0) {
        for (const emp of dto.employees) {
          const employee = await this.repo.manager.findOne(Employee, {
            where: { id: emp.id },
          });

          const validTime =
            await this.appointmentService.getRecordsWithOverlappingHours(
              emp.id,
              dto.startTime,
              dto.endTime,
              orderDetail.order.id,
              orderDetail.id,
            );

          if (validTime.totalTask >= 3) {
            throw new BadRequestException(
              `This time frame of ${
                employee.displayName || employee.fullName
              } is full. Please choose another time frame!`,
            );
          }

          if (orderDetail.employees.length > 0) {
            for (const e of orderDetail.employees) {
              if (e.id !== emp.id) {
                newEmployees.push(emp);
              } else {
                oldEmployees.push(emp);
              }
            }
          } else {
            newEmployees.push(emp);
          }
        }
      } else if (dto?.employees?.length === 0) {
        await this.repo.manager.delete('order_detail_employees_employee', {
          orderDetailId,
        });
      }

      if (orderDetail.employees.length > 0) {
        for (const e of orderDetail.employees) {
          if (!oldEmployees.some((oldEmp) => oldEmp.id === e.id)) {
            await this.repo.manager.delete('order_detail_employees_employee', {
              orderDetailId,
              employeeId: e.id,
            });
          }
        }
      }

      if (newEmployees.length > 0) {
        for (const e of newEmployees) {
          await this.repo.manager.insert('order_detail_employees_employee', {
            orderDetailId,
            employeeId: e.id,
          });
        }
      }

      delete dto.employees;

      if (dto.status === AppointmentStatus.ARRIVED) {
        await this.repo.update(
          { id: orderDetailId },
          { status: AppointmentStatus.ARRIVED },
        );
      }

      if (orderDetail.order?.appointment?.id && dto.startTime && dto.endTime) {
        await this.appointmentRepo.save({
          id: orderDetail.order?.appointment?.id,
          startTime: dto.startTime,
          endTime: dto.endTime,
        });

        if (orderDetail.order.appointment?.checkOut === null) {
          await this.repo.manager.transaction(async (trans) => {
            const productInfo = await trans.findOne(Product, {
              where: { id: dto?.product?.id },
            });

            await trans.update(
              OrderDetail,
              { id: orderDetailId },
              {
                price: productInfo.price,
                product: {
                  id: productInfo.id,
                },
                status: dto.status || orderDetail.status,
                note: dto.note || orderDetail.note,
              },
            );

            const totalBeforeTax = productInfo?.price * 1;
            const subTotal = parseFloat(totalBeforeTax.toFixed(2));
            const total = parseFloat(totalBeforeTax.toFixed(2));

            await trans.save(Order, {
              id: orderDetail.order.id,
              total,
              totalBeforeTax: parseFloat(totalBeforeTax.toFixed(2)),
              subTotal,
            });

            const orderUpdateRecord = await trans.findOne(Order, {
              where: { id: orderDetail.order.id },
              relations: ['items', 'items.product', 'items.employees'],
            });

            await trans.update(
              Order,
              { id: orderDetail.order.id },
              { payload: orderUpdateRecord },
            );
          });
        }
      }

      return await this.repo.update({ id: orderDetailId }, dto);
    } catch (e) {
      console.error('Error updating order detail:', e);
      return { success: false, message: e.message };
    }
  }

  async getOneOrderDetail(id: string) {
    return this.repo
      .createQueryBuilder('orderDetail')
      .leftJoinAndSelect('orderDetail.order', 'order')
      .leftJoinAndSelect('order.branch', 'branch')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .leftJoinAndSelect('order.appointment', 'appointment')
      .leftJoinAndSelect('appointment.customer', 'customer')
      .leftJoinAndSelect('orderDetail.duration', 'duration')
      .leftJoinAndSelect('orderDetail.employees', 'employees')
      .where('orderDetail.id = :id', { id })
      .getOne();
  }
}
