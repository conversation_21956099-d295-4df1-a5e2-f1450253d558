import { Controller, Get, Post, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { SeedService } from './seed.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('SeedData')
@Controller({
  path: 'seed',
})
export class SeedController {
  constructor(private readonly notificationService: SeedService) {}

  //Import Customer
  @UseGuards(JwtAuthGuard)
  @Post('customer')
  @ApiOperation({ summary: 'Import Customer' })
  async importCustomer() {
    return await this.notificationService.importCustomer();
  }

  //Import Employee
  @UseGuards(JwtAuthGuard)
  @Post('employee')
  @ApiOperation({ summary: 'Import Employee' })
  async importEmployee() {
    return await this.notificationService.importEmployee();
  }

  //Import Rfid
  @UseGuards(JwtAuthGuard)
  @Post('rfid')
  @ApiOperation({ summary: 'Import Rfid' })
  async importRfid() {
    return await this.notificationService.importRfid();
  }
}
