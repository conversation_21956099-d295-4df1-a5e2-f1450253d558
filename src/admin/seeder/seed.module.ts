import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { SeedService } from './seed.service';
import { Customer } from '../customer/customer.entity';
import { SeedController } from './seed.controller';
import { Setting } from '../setting/setting.entity';
import { Branch } from '../branch/branch.entity';
import { Employee } from '../employee/employee.entity';
import { Rfid } from '../rfid/rfid.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Customer, Setting, Branch, Employee, Rfid]),
  ],
  controllers: [SeedController],
  providers: [SeedService],
  exports: [SeedService],
})
export class SeedModule {}
