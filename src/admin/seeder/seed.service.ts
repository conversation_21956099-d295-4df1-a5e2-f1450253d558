import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as path from 'path';
import * as XLSX from 'xlsx';
import { Customer } from '../customer/customer.entity';
import { Setting } from '../setting/setting.entity';
import { Employee } from '../employee/employee.entity';
import { Rfid } from '../rfid/rfid.entity';
import { Branch } from '../branch/branch.entity';

@Injectable()
export class SeedService {
  constructor(
    @InjectRepository(Customer)
    private customerRepo: Repository<Customer>,
    @InjectRepository(Setting)
    private settingRepo: Repository<Setting>,
    @InjectRepository(Employee)
    private employeeRepo: Repository<Employee>,
    @InjectRepository(Rfid)
    private rfidRepo: Repository<Rfid>,
    @InjectRepository(Branch)
    private branchRepo: Repository<Branch>,
  ) {}

  async importCustomer() {
    const activeSatus = await this.settingRepo.findOne({
      where: { name: 'Active' },
    });
    const maleGender = await this.settingRepo.findOne({
      where: { name: 'Male', type: 'gender' },
    });
    const femaleGender = await this.settingRepo.findOne({
      where: { name: 'Female', type: 'gender' },
    });
    const otherGender = await this.settingRepo.findOne({
      where: { name: 'Other', type: 'gender' },
    });
    const countries = {};

    (
      (await this.settingRepo.find({
        where: { type: 'nationality' },
      })) || []
    ).forEach((c) => {
      countries[c.name.toLowerCase()] = c;
    });

    const customers = [];
    //read file xlsx
    const filename = 'customers.xlsx'; // Replace with your actual filename
    const filePath = path.join(__dirname, '../../../../seedData/', filename);

    const workbook = XLSX.readFile(filePath);
    const first_worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const data = XLSX.utils.sheet_to_json(first_worksheet, {
      header: 1,
      defval: '',
    });

    const allCustomers = {};
    (await this.customerRepo.find()).map((c) => (allCustomers[c.code] = c.id));

    for (const row of data.slice(2, data.length)) {
      const code =
        typeof row[1] === 'number'
          ? 10000000 + +row[1]
          : 10000000 + +row[1].replace('CS', 0);
      // await data.slice(2, data.length).map(async (row: string[]) => {
      if (allCustomers[code]) continue;
      customers.push({
        // id: allCustomers[code] ? allCustomers[code] : undefined,
        code,
        firstName: row[4],
        lastName: row[5],
        status: activeSatus,
        address: row[25],
        nric: row[6],
        gender:
          row[10] === 'Male'
            ? maleGender
            : row[10] === 'Female'
            ? femaleGender
            : otherGender,
        firstVisit: new Date((+row[8] - 25569) * 24 * 60 * 60 * 1000),
        birthDay: new Date((+row[11] - 25569) * 24 * 60 * 60 * 1000),
        phone: `+${row[19]}`,
        email: row[24] === '--' || row[24] === '' ? null : row[24],
        nationality: countries[row[14].toLowerCase()] || null,
      });
    }

    //save to db
    try {
      await Promise.all(
        customers.map(async (c) => await this.customerRepo.save(c)),
      );
    } catch (e) {
      console.log('error: ', e);
    }
    return true;
  }

  async importEmployee() {
    const activeSatus = await this.settingRepo.findOne({
      where: { name: 'Active' },
    });
    const branch = await this.branchRepo.findOne({
      where: { name: 'SINGAPORE' },
    });

    const employees = [
      'APRIL',
      'PINKY',
      'MALEE',
      'POM',
      'AMY',
      'NANCY',
      'FE',
      'JU',
      'MANOW',
      'LINDA',
      'NOKNOI',
      'ANNA',
      'JAM',
      'AIM',
      'LISA',
      'ICE',
      'RITA',
      'WENDY',
      'JASMINE',
      'KAE',
      'NIKKI',
    ];
    //save to db
    for (const e of employees) {
      await this.employeeRepo.save({
        fullName: e,
        displayName: e,

        branch,
        status: activeSatus,
      });
    }
    return true;
  }

  async importRfid() {
    const maleGroup = await this.settingRepo.findOne({
      where: { name: 'Male', type: 'group' },
    });
    const femaleGroup = await this.settingRepo.findOne({
      where: { name: 'Female', type: 'group' },
    });
    const activeSatus = await this.settingRepo.findOne({
      where: { name: 'Active' },
    });
    const branch = await this.branchRepo.findOne({
      where: { name: 'SINGAPORE' },
    });

    for (let i = 1; i < 138; i++) {
      await this.rfidRepo.save({
        branch,
        lockerNumber: i,
        status: activeSatus,
        group: maleGroup,
        serialCode: '1' + String(i).padStart(3, '0'),
      });

      if (i >= 109) {
        continue;
      }

      await this.rfidRepo.save({
        branch,
        lockerNumber: i,
        status: activeSatus,
        group: femaleGroup,
        serialCode: String(i).padStart(3, '0'),
      });
    }

    return true;
  }
}
