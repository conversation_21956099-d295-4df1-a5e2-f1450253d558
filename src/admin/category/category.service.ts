import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { Category } from './category.entity';
import { CrudRequest } from 'src/core/crud/crud';
import { CategoryGroup } from './category.group';
import { Product } from '../product/product.entity';
import { IssueCoupon } from '../inventory/issue-coupon.entity';
import { ProductType } from 'src/core/enums/entity';

@Injectable()
export class CategoryService extends BaseCrudService<Category> {
  constructor(@InjectRepository(Category) repo: Repository<Category>) {
    super(repo);
  }

  async getManyCategory(
    req: CrudRequest,
    branchIds?: string[],
    keySearch?: string,
  ) {
    let isMember = null;
    let rootCategoryNames = Object.values(CategoryGroup).map((c) => c.name);
    if (req.parsed?.filter) {
      for (const f of req.parsed.filter) {
        if (f.field === 'name') {
          rootCategoryNames = f.value;
        } else if (f.field === 'isMember') {
          isMember = f.value === 'true' || f.value === true ? true : false;
        }
      }
    }

    const query = this.repo
      .createQueryBuilder('category')
      .select([
        'category.id',
        'category.name',
        'category.roomQuantity',
        'children.id',
        'children.name',
        'children.created',
        'children.roomQuantity',
        'parent.id',
        'parent.name',
        'avatar.id',
        'avatar.url',
        'status.id',
        'status.type',
        'status.name',
        'status.order',
        'status.value',
        'grandchildren.id',
        'grandchildren.name',
        'grandchildren.roomQuantity',
        'grandparent.id',
        'grandparent.name',
        'grandchildrenAvatar.id',
        'grandchildrenAvatar.url',
        'grandchildrenStatus.id',
        'grandchildrenStatus.type',
        'grandchildrenStatus.name',
        'grandchildrenStatus.created',
        'childrenStatus.id',
        'childrenStatus.type',
        'childrenStatus.name',
        'childrenAvatar.id',
        'childrenAvatar.url',
      ])
      // .select('granditemscategory.id', 'granditemscategoryId')
      .leftJoin('category.children', 'children')
      .leftJoin('category.avatar', 'avatar')
      .leftJoin('category.status', 'status')
      // .leftJoin('category.branch', 'branch')
      // .leftJoin('category.status', 'childrenStatus')
      .leftJoin('children.parent', 'parent')
      .leftJoin('children.avatar', 'childrenAvatar')
      .leftJoin('children.status', 'childrenStatus')

      .leftJoin('children.children', 'grandchildren')
      .leftJoin('grandchildren.avatar', 'grandchildrenAvatar')
      .leftJoin('grandchildren.status', 'grandchildrenStatus')
      // .leftJoin('children.children.avatar', 'grandchildrenAvater')
      // .leftJoin('children.children.status', 'grandchildrenStatus')
      .leftJoin('grandchildren.parent', 'grandparent');

    query
      .where('category.name IN (:...names)', {
        names: rootCategoryNames,
      })
      .andWhere('category.parent IS NULL');
    // if (branchIds.length > 0) {
    //   query.andWhere('category.branch IN (:...branchIds)', { branchIds });
    // }

    let needCheckCouponValid = false;
    if (req.parsed?.join && req.parsed?.join.length > 0) {
      for (const f of req.parsed.join) {
        if (f.field === 'items') {
          needCheckCouponValid = true;
          query
            .addSelect([
              'granditems.id',
              'granditems.name',
              'granditems.price',
              'granditems.priceForMember',
              'granditems.isMember',
              'granditems.periodUnit',
              'granditems.period',
              'granditems.isPassport',
              'items.priceForMember',
              'granditems.type',
              'granditemsAvatar.id',
              'granditemsAvatar.url',
              'grandduration.id',
              'grandduration.type',
              'grandduration.name',
              'grandduration.order',
              'grandduration.value',
              'granditemscategory.id',
              'granditemscategory.name',
              'items.id',
              'items.name',
              'items.price',
              'items.isMember',
              'items.priceForMember',
              'items.type',
              'items.periodUnit',
              'items.period',
              'items.isPassport',
              'itemsAvatar.id',
              'itemsAvatar.url',
              'itemscategory.name',
              'itemscategory.id',
              'itemsBranches.id',
              'granditemsBranches.id',
              'duration.id',
              'duration.type',
              'duration.name',
              'duration.order',
              'duration.value',
              'itemsStatus.id',
              'itemsStatus.type',
              'itemsStatus.name',
              'granditemsStatus.id',
              'granditemsStatus.type',
              'granditemsStatus.name',
              'itemsPriceType.name',
              'granditemsPriceType.name',
            ])
            .leftJoin('grandchildren.items', 'granditems')
            .leftJoin('granditems.branches', 'granditemsBranches')
            .leftJoin('granditems.avatar', 'granditemsAvatar')
            .leftJoin('granditems.duration', 'grandduration')
            .leftJoin('granditems.category', 'granditemscategory')
            .leftJoin('granditems.status', 'granditemsStatus')
            .leftJoin('children.items', 'items')
            .leftJoin('items.branches', 'itemsBranches')
            .leftJoin('items.avatar', 'itemsAvatar')
            .leftJoin('items.duration', 'duration')
            .leftJoin('items.category', 'itemscategory')
            .leftJoin('items.status', 'itemsStatus')
            .leftJoin('items.priceType', 'itemsPriceType')
            .leftJoin('granditems.priceType', 'granditemsPriceType');
        }

        if (branchIds.length > 0) {
          query.andWhere(
            '(itemsBranches.id IN (:...branchIds) OR granditemsBranches.id IN (:...branchIds))',
            { branchIds },
          );
        }
        if (keySearch) {
          query.andWhere(
            '(granditems.name ILIKE :keySearch OR items.name ILIKE :keySearch)',
            {
              keySearch: `%${keySearch}%`,
            },
          );
        }

        query.andWhere('childrenStatus.name = :status', { status: 'Active' });
      }
    }

    query.orderBy('category.created', 'DESC');
    query.addOrderBy('children.created', 'DESC');
    query.addOrderBy('grandchildren.created', 'DESC');
    const categories = await query.getMany();

    if (isMember === null) {
      const processedCategories = await Promise.all(
        categories.map(async (category) => {
          const processedChildren = await Promise.all(
            category.children?.map(async (child) => {
              // Process items for child
              const filteredItems = child.items
                ? await Promise.all(
                    child.items.map(async (item) => {
                      if (item.status?.name !== 'Active') {
                        return false;
                      }

                      // For coupon type products, check issue date and available slots
                      if (item.type === ProductType.COUPON) {
                        const today = new Date();
                        today.setHours(0, 0, 0, 0);

                        // Get all issue coupons for this product
                        const issueCoupons = await this.repo.manager.find(
                          IssueCoupon,
                          {
                            where: {
                              coupon: { id: item.id },
                              status: { name: 'ACTIVE' },
                            },
                            relations: ['status'],
                          },
                        );

                        // Filter issue coupons that are valid
                        const validIssueCoupons = issueCoupons.filter((ic) => {
                          const issueDate = new Date(ic.issueDate);
                          issueDate.setHours(0, 0, 0, 0);
                          return issueDate <= today && ic.remain > 0;
                        });

                        return validIssueCoupons.length > 0;
                      }

                      return true;
                    }),
                  )
                : [];

              // Process grandchildren for child
              const processedGrandchildren = await Promise.all(
                child.children?.map(async (grandChild) => {
                  // Process items for grandchild
                  const filteredGrandItems = grandChild.items
                    ? await Promise.all(
                        grandChild.items.map(async (item) => {
                          if (item.status?.name !== 'Active') {
                            return false;
                          }

                          // For coupon type products, check issue date and available slots
                          if (item.type === ProductType.COUPON) {
                            const today = new Date();
                            today.setHours(0, 0, 0, 0);

                            // Get all issue coupons for this product
                            const issueCoupons = await this.repo.manager.find(
                              IssueCoupon,
                              {
                                where: {
                                  coupon: { id: item.id },
                                  status: { name: 'ACTIVE' },
                                },
                                relations: ['status'],
                              },
                            );

                            // Filter issue coupons that are valid
                            const validIssueCoupons = issueCoupons.filter(
                              (ic) => {
                                const issueDate = new Date(ic.issueDate);
                                issueDate.setHours(0, 0, 0, 0);
                                return issueDate <= today && ic.remain > 0;
                              },
                            );

                            return validIssueCoupons.length > 0;
                          }

                          return true;
                        }),
                      )
                    : [];

                  return {
                    ...grandChild,
                    items: grandChild.items?.filter(
                      (_, index) => filteredGrandItems[index],
                    ),
                  };
                }) || [],
              );

              return {
                ...child,
                items: child.items?.filter((_, index) => filteredItems[index]),
                children: processedGrandchildren,
              };
            }) || [],
          );

          return {
            ...category,
            children: processedChildren,
          };
        }),
      );

      return processedCategories;
    }

    let couponIdValid = [];

    const getValidCoupons = async () => {
      if (!needCheckCouponValid) return [];

      const validCoupons = await this.repo.manager
        .createQueryBuilder(IssueCoupon, 'issueCoupon')
        .innerJoinAndSelect('issueCoupon.coupon', 'coupon')
        .innerJoinAndSelect('issueCoupon.status', 'issueStatus')
        .innerJoinAndSelect('issueCoupon.branches', 'branches')
        .where('issueStatus.name = :status', { status: 'Active' })
        .andWhere('issueCoupon.remain > 0')
        .andWhere(
          branchIds.length > 0 ? 'branches.id IN (:...branchIds)' : '1=1',
          { branchIds },
        )
        .getMany();

      return validCoupons.filter((issue) => issue.coupon);
    };

    couponIdValid = await getValidCoupons();

    return categories.map((category) => {
      if (category.name === 'SERVICE') {
        return {
          ...category,
          children: category.children?.map((child) => ({
            ...child,
            children: child.children?.map((grandChild) => ({
              ...grandChild,
              items: grandChild.items?.filter((item) => {
                const isPriceTypeValid = isMember
                  ? ['for-all', 'member'].includes(
                      item.priceType?.name?.toLowerCase(),
                    )
                  : ['for-all', 'non-member'].includes(
                      item.priceType?.name?.toLowerCase(),
                    );

                if (item.type === ProductType.COUPON) {
                  const issueCoupon = couponIdValid.find(
                    (issue) => issue.coupon.id === item.id,
                  );
                  if (issueCoupon) {
                    item['issued'] = issueCoupon.issue;
                    item['remain'] = issueCoupon.remain;
                    item['used'] = issueCoupon.used;
                  }
                }

                return item.status.name === 'Active' && isPriceTypeValid;
              }),
            })),
          })),
        };
      }

      if (category.name === 'COUPON') {
        return {
          ...category,
          children: category.children?.map((child) => ({
            ...child,
            items: child.items?.filter((item) => {
              const isCouponValid = couponIdValid
                .map((issue) => issue.coupon.id)
                .includes(item.id);

              if (!isCouponValid) {
                return false;
              }

              const issueCoupon = couponIdValid.find(
                (issue) => issue.coupon.id === item.id,
              );

              // Modify the item directly
              item['issued'] = issueCoupon.issue;
              item['remain'] = issueCoupon.remain;
              item['used'] = issueCoupon.used;

              return true;
            }),
          })),
        };
      }

      if (['PRODUCT'].includes(category.name)) {
        return {
          ...category,
          children: category.children?.map((child) => ({
            ...child,
            items: child.items?.filter((item) => {
              const isPriceTypeValid = isMember
                ? ['for-all', 'member'].includes(
                    item.priceType?.name?.toLowerCase(),
                  )
                : ['for-all', 'non-member'].includes(
                    item.priceType?.name?.toLowerCase(),
                  );

              if (item.type === ProductType.COUPON) {
                const issueCoupon = couponIdValid.find(
                  (issue) => issue.coupon.id === item.id,
                );
                if (issueCoupon) {
                  item['issued'] = issueCoupon.issue;
                  item['remain'] = issueCoupon.remain;
                  item['used'] = issueCoupon.used;
                }
              }

              return item.status.name === 'Active' && isPriceTypeValid;
            }),
            children: child.children?.map((grandChild) => ({
              ...grandChild,
              items: grandChild.items?.filter((item) => {
                const isPriceTypeValid = isMember
                  ? ['for-all', 'member'].includes(
                      item.priceType?.name?.toLowerCase(),
                    )
                  : ['for-all', 'non-member'].includes(
                      item.priceType?.name?.toLowerCase(),
                    );

                if (item.type === ProductType.COUPON) {
                  const issueCoupon = couponIdValid.find(
                    (issue) => issue.coupon.id === item.id,
                  );
                  if (issueCoupon) {
                    item['issued'] = issueCoupon.issue;
                    item['remain'] = issueCoupon.remain;
                    item['used'] = issueCoupon.used;
                  }
                }

                return item.status.name === 'Active' && isPriceTypeValid;
              }),
            })),
          })),
        };
      }

      if (['FOOD', 'BEVERAGE', 'FOODBEVERAGE'].includes(category.name)) {
        return {
          ...category,
          children: category.children?.map((child) => ({
            ...child,
            items: child.items?.filter((item) => {
              if (item.type === ProductType.COUPON) {
                const issueCoupon = couponIdValid.find(
                  (issue) => issue.coupon.id === item.id,
                );
                if (issueCoupon) {
                  item['issued'] = issueCoupon.issue;
                  item['remain'] = issueCoupon.remain;
                  item['used'] = issueCoupon.used;
                }
              }
              return item.status.name === 'Active';
            }),
            children: child.children?.map((grandChild) => ({
              ...grandChild,
              items: grandChild.items?.filter((item) => {
                if (item.type === ProductType.COUPON) {
                  const issueCoupon = couponIdValid.find(
                    (issue) => issue.coupon.id === item.id,
                  );
                  if (issueCoupon) {
                    item['issued'] = issueCoupon.issue;
                    item['remain'] = issueCoupon.remain;
                    item['used'] = issueCoupon.used;
                  }
                }
                return item.status.name === 'Active';
              }),
            })),
          })),
        };
      }

      return category;
    });
  }

  async deleteOneCategory(req: CrudRequest) {
    const target = await super.getOne(req);
    // store for soft-deleting as target id will be removed afterd entityManager.remove
    const targetId = target?.['id'];

    const queryRunner = this.repo.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction('SERIALIZABLE');

    try {
      // Check can remove first for has relation entities?
      const deletedEntity = await queryRunner.manager.remove(
        this.repo.metadata.tableName,
        target,
      );
      if (deletedEntity) {
        await queryRunner.rollbackTransaction();
        await queryRunner.startTransaction('SERIALIZABLE');
        await queryRunner.manager
          .getRepository(Product)
          .softDelete({ category: { id: targetId } });
        await queryRunner.manager.softDelete(
          this.repo.metadata.tableName,
          targetId,
        );
      }
      // commit transaction now:
      await queryRunner.commitTransaction();
    } catch (err) {
      // since we have errors let's rollback changes we made
      await queryRunner.rollbackTransaction();
      // ER_ROW_IS_REFERENCED_2
      // if (err?.errno === 1451) {
      //   const parsedError = err?.sqlMessage?.match(
      //     /.*\(\`.*\`.(\`.*\`), CONSTRAINT \`/i,
      //   );
      //   throw new ReferencedEntryDeletionException(parsedError?.[1]);
      // }

      throw err;
    } finally {
      // you need to release query runner which is manually created:
      await queryRunner.release();
    }
  }
}
