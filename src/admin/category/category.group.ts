import { genUUID } from 'src/core/crypto/crypto.provider';

export interface ICategoryGroup {
  id?: string;
  name: string;
}

export const CategoryGroup = {
  PRODUCT: {
    id: genUUID('PRODUCT', 'Catergory'),
    name: 'PRODUCT',
  },
  SERVICE: {
    id: genU<PERSON><PERSON>('SERVICE', 'Catergory'),
    name: 'SERVICE',
  },
  MEMBERSHIP: {
    id: genUUI<PERSON>('MEMBERSHIP', 'Catergory'),
    name: 'MEMBERSHIP',
  },
  COUPON: {
    id: genUUID('COUPON', 'Catergory'),
    name: 'COUPON',
  },
  FOOD: {
    id: genUUID('FOOD', 'Catergory'),
    name: 'FOOD',
  },
  BEVERAGE: {
    id: genUUID('BEVERA<PERSON>', 'Catergory'),
    name: 'BEVE<PERSON><PERSON>',
  },
};
