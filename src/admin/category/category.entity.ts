import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ToOne,
  OneToMany,
  <PERSON>Children,
  TreeParent,
  <PERSON>in<PERSON><PERSON><PERSON>n,
  OneToOne,
} from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty } from '@nestjs/swagger';
import { Branch } from '../branch/branch.entity';
import { IsString } from 'class-validator';
import { Product } from '../product/product.entity';
import { Setting } from '../setting/setting.entity';
import { Media } from '../media/media.entity';

@Entity('category')
export class Category extends DocEntity {
  @Column()
  @ApiProperty()
  @IsString()
  name: string;

  @ManyToOne(() => Branch, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Branch })
  branch: Branch;

  @TreeChildren()
  @ApiProperty({
    type: () => [Category],
    required: false,
    readOnly: true,
    example: null,
    properties: {
      hide: { default: true },
    },
  })
  children: Category[];

  @TreeParent({ onDelete: 'SET NULL' })
  @ApiProperty({
    type: () => Category,
    example: null,
    properties: {
      hide: { default: true },
    },
  })
  parent: Category;

  @ApiProperty({ type: () => Product, isArray: true })
  @OneToMany(() => Product, (product) => product.category, {
    onDelete: 'SET NULL',
  })
  items: Product[];

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Setting })
  status: Setting;

  @OneToOne(() => Media, (media) => media.category)
  @JoinColumn()
  avatar: Media;

  @Column({ type: 'integer', default: 0 })
  @ApiProperty()
  roomQuantity: number;
}
