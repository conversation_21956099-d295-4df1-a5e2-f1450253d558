import {
  CrudController,
  CrudRequest,
  Override,
  ParsedRequest,
} from 'src/core/crud/crud';

import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';

import { CreateCategoryDto } from './dto/createCategory.dto';
import { UpdateCategoryDto } from './dto/updateCategory.dto';

import { Category } from './category.entity';
import { CategoryService } from './category.service';
import { isNull } from 'src/core/crud/util';
import { CategoryGroup } from './category.group';
import { Public } from '../auth/decorators/public.decerator';
import { Query, Req } from '@nestjs/common';

@BaseCrud(
  {
    model: {
      type: Category,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    dto: {
      create: CreateCategoryDto,
      update: UpdateCategoryDto,
    },
    query: {
      join: {
        status: { eager: true, allow: ['id', 'name'] },
        avatar: { eager: true, allow: ['id', 'url'] },
      },
      sort: [
        {
          field: 'created',
          order: 'ASC',
        },
      ],
      // sort: [{ field: 'create', order: 'DESC' }],
      // filter: {
      //   $and: [
      //     { name: { $in: Object.values(CategoryGroup).map((c) => c.name) } },
      //   ],
      // },
    },
  },
  {},
)
export class CategoryController extends BaseCrudController<Category> {
  constructor(public service: CategoryService) {
    super(service);
  }
  get base(): CrudController<Category> {
    return this;
  }

  @Override('getManyBase')
  @Public()
  getMany(
    @ParsedRequest() crudRequest: CrudRequest,
    @Req() req: Request,
    @Query('keySearch') keySearch: string,
  ) {
    return this.service.getManyCategory(
      crudRequest,
      req.headers?.['branchid']?.split(',') || [],
      keySearch,
    );
  }

  // @Override('deleteOneBase')
  // deleteOne(@ParsedRequest() req: CrudRequest) {
  //   return this.service.deleteOneCategory(req);
  // }
}
