import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import { Branch } from './branch.entity';

@Injectable()
export class BranchService extends BaseCrudService<Branch> {
  constructor(@InjectRepository(Branch) repo: Repository<Branch>) {
    super(repo);
  }
}
