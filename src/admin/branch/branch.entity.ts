import { <PERSON>tity, Column, OneToMany, Index, ManyToOne } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { User } from '../user/user.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsEmail, IsOptional, IsString } from 'class-validator';
import { Setting } from '../setting/setting.entity';
import { Employee } from '../employee/employee.entity';
import { Currency } from '../currency/currency.entity';
@Entity('branch')
export class Branch extends DocEntity {
  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  name?: string;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  code?: string;

  @Column({ type: 'json', nullable: true })
  @ApiPropertyOptional({ type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  phones?: string[];

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  order?: number;

  @ManyToOne(() => Setting, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Setting })
  status: Setting;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  address?: string;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  startTime?: string;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  endTime?: string;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  @Index({ unique: true })
  @IsEmail()
  @IsOptional()
  email?: string;

  @OneToMany(() => User, (user) => user.branch, {
    onDelete: 'CASCADE',
  })
  user: User[];

  @OneToMany(() => Employee, (employee) => employee.branch, {
    onDelete: 'CASCADE',
  })
  employees: Employee[];

  @ManyToOne(() => Currency, (currency) => currency.branches, {
    onDelete: 'SET NULL',
  })
  currency: Currency;
}
