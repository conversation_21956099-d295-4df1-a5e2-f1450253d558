import {
  CallH<PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { snakeCase } from 'lodash';

import { Observable } from 'rxjs';
import { CrudRequest } from '../../../core/crud/crud';
import { PARSED_CRUD_REQUEST_KEY } from '../../../core/crud/crud/constants';
import { DataSource, getConnection, getManager } from 'typeorm';

@Injectable()
export class FilterHeaderBranchInterceptor implements NestInterceptor {
  constructor(private readonly dataSource: DataSource) {}
  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const req = context.switchToHttp().getRequest();
    const headers = req.headers;
    const method = req.method;

    const crudRequest: CrudRequest = req?.[PARSED_CRUD_REQUEST_KEY];
    const branchIds = headers?.branchid?.split(',') || [];

    if (method !== 'GET' || branchIds.length === 0) {
      return next.handle().pipe();
    }

    const { resourceName, resourceRepo } =
      this.parseResourceInfo(context) || {};

    if (['coupon', 'credit'].includes(resourceName)) {
      return next.handle().pipe();
    }

    try {
      this.validateResourceScope(resourceRepo).belongsToManyBrands(
        async (ok, field) => {
          ok && this.crudReq(crudRequest).in(field, branchIds).push();
          // if (ok && user.brands?.length) {
          //   this.crudReq(crudRequest).in(field, map(user.brands, 'id')).push();
          // }
        },
      );
    } catch (e) {
      console.log('e: ', e);
    }

    try {
      this.validateResourceScope(resourceRepo).belongsToBrand((ok, field) => {
        ok && this.crudReq(crudRequest).in(field, branchIds).push();

        //   user.brands?.length &&
        //   this.crudReq(crudRequest).in(field, map(user.brands, 'id')).push();
      });
    } catch (e) {
      // this.logger.error(`${resourceName}_${user?.id}_${e}`);
    }

    // if (method === 'GET' && branchIds.length > 0) {
    //   this.crudReq(crudRequest).in('branches.id', branchIds).push();
    // }

    return next.handle().pipe();
  }

  private parseResourceInfo(context: ExecutionContext) {
    // Resource
    const resource = Reflect.getMetadata('resource', context.getClass());
    let resourceName =
      resource?.options?.customRepository || snakeCase(resource?.name);

    //  If invalid resource name
    if (!resourceName || ['credit', 'report'].includes(resourceName)) {
      return null;
    }
    if (['membership', 'service', 'food', 'beverage'].includes(resourceName)) {
      resourceName = 'product';
    }
    // Resource Repo
    // const resourceRepo = getManager().getRepository(resourceName);
    const resourceRepo = this.dataSource.getRepository(resourceName);

    return { resource, resourceName, resourceRepo };
  }

  private validateResourceScope(resourceRepo) {
    return {
      belongsToBrand: (cb) => {
        const isBrandExisted = !!resourceRepo.metadata.columns.find(
          (col) => col.propertyName == 'branchId',
        );

        const hasBrandRelation = !!resourceRepo.metadata.relations.find(
          (p) => p.propertyName === 'branch',
        );

        return cb(
          isBrandExisted || hasBrandRelation,
          !!hasBrandRelation ? 'branch.id' : 'branchId',
        );
      },

      belongsToManyBrands: (cb) => {
        const isManyBrandsExisted = !!resourceRepo?.metadata.relations.find(
          (c) =>
            c.propertyName === 'branches' && c.relationType === 'many-to-many',
        );
        return cb(isManyBrandsExisted, 'branches.id');
      },
    };
  }

  private crudReq(crudRequest) {
    let cond;
    return {
      in: function (field, values) {
        cond = {
          [field]: {
            $in: values,
          },
        };
        return this;
      },
      eq: function (field, value) {
        cond = {
          [field]: {
            $eq: value,
          },
        };
        return this;
      },
      push: () => crudRequest.parsed.search.$and.push(cond),
    };
  }
}
