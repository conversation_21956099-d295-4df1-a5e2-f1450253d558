import { CrudController } from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { Branch } from './branch.entity';
import { BranchService } from './branch.service';
import { UpdateBranchDto } from './dto/updateBranch.dto';

@BaseCrud(
  {
    model: {
      type: Branch,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    dto: {
      update: UpdateBranchDto,
    },
    query: {
      join: {
        status: { eager: true, allow: ['id', 'name'] },
        currency: { eager: true, allow: ['id', 'name', 'symbol', 'code'] },
      },
    },
  },
  {
    // grantPerm: userPerm,
    // group: ResourceGroup.SYSTEM,
  },
)
export class BranchController extends BaseCrudController<Branch> {
  constructor(public service: BranchService) {
    super(service);
  }
  get base(): CrudController<Branch> {
    return this;
  }
}
