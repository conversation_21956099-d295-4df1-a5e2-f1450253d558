import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import { EmployeeSetting } from './employee-setting.entity';

@Injectable()
export class EmployeeSettingService extends BaseCrudService<EmployeeSetting> {
  constructor(
    @InjectRepository(EmployeeSetting) repo: Repository<EmployeeSetting>,
  ) {
    super(repo);
  }
}
