import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { WorkingHourModule } from '../working-hour/working-hour.module';
import { EmployeeSetting } from './employee-setting.entity';
import { EmployeeSettingController } from './employee-setting.controller';
import { EmployeeSettingService } from './employee-setting.service';

@Module({
  imports: [TypeOrmModule.forFeature([EmployeeSetting]), WorkingHourModule],
  controllers: [EmployeeSettingController],
  providers: [EmployeeSettingService],
  exports: [EmployeeSettingService],
})
export class EmployeeSettingModule {}
