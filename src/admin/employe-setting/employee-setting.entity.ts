import { Entity, Column, ManyToOne } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty } from '@nestjs/swagger';
import { Setting } from '../setting/setting.entity';
@Entity()
export class EmployeeSetting extends DocEntity {
  @Column({
    type: 'decimal',
    default: 0,
  })
  @ApiProperty()
  product: number;

  @Column({
    type: 'decimal',
    default: 0,
  })
  @ApiProperty()
  service: number;

  @Column({
    type: 'decimal',
    default: 0,
  })
  @ApiProperty()
  membership: number;

  @Column({
    type: 'decimal',
    default: 0,
  })
  @ApiProperty()
  salary: number;

  @ManyToOne(() => Setting, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Setting })
  salaryUnit: Setting;

  @Column({
    type: 'decimal',
    default: 0,
  })
  @ApiProperty()
  ot: number;

  @ManyToOne(() => Setting, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Setting })
  otUnit: Setting;
}
