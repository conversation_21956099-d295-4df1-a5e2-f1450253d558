import { CrudController } from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { EmployeeSetting } from './employee-setting.entity';
import { CreateEmployeeSettingDto } from './dto/createEmployee.dto';
import { UpdateEmployeeSettingDto } from './dto/updateEmployee.dto';
import { WorkingHourService } from '../working-hour/working-hour.service';
import { EmployeeSettingService } from './employee-setting.service';

@BaseCrud(
  {
    model: {
      type: EmployeeSetting,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    dto: {
      create: CreateEmployeeSettingDto,
      update: UpdateEmployeeSettingDto,
    },
    query: {
      //
    },
  },
  {
    // grantPerm: userPerm,
    // group: ResourceGroup.SYSTEM,
  },
)
export class EmployeeSettingController extends BaseCrudController<EmployeeSetting> {
  constructor(
    public service: EmployeeSettingService,
    public workingHourService: WorkingHourService,
  ) {
    super(service);
  }
  get base(): CrudController<EmployeeSetting> {
    return this;
  }
}
