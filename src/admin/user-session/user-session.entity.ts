import {
  <PERSON>tity,
  Column,
  ManyToOne,
} from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { Role } from '../role/role.entity';
import { User } from '../user/user.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity('user_session')
export class UserSession extends DocEntity {
  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  login?: Date;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  logout?: Date;

  @Column({
    length: 256,
  })
  @ApiProperty()
  tokenString: string;

  @ManyToOne(() => Role, { cascade: true })
  role: Role;

  @ManyToOne(() => User, { cascade: true })
  user: User;
}
