import { CrudController } from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { Currency } from './currency.entity';
import { CurrencyService } from './currency.service';

@BaseCrud(
  {
    model: {
      type: Currency,
    },
    routes: {
      only: ['getManyBase', 'getOneBase'],
    },
  },
  {},
)
export class CurrencyController extends BaseCrudController<Currency> {
  constructor(public service: CurrencyService) {
    super(service);
  }
  get base(): CrudController<Currency> {
    return this;
  }
}
