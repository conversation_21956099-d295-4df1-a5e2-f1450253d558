import { Entity, Column, Index, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, Matches } from 'class-validator';
import { DocEntity } from 'src/core/base/doc.entity';
import { Branch } from '../branch/branch.entity';

@Entity()
export class Currency extends DocEntity {
  @Column()
  @ApiProperty()
  @Index({ unique: true })
  @IsNotEmpty()
  name: string;

  @Column()
  @ApiProperty()
  @IsNotEmpty()
  symbol: string;

  @Column()
  @ApiProperty()
  @Index({ unique: true })
  @IsNotEmpty()
  @Matches(new RegExp('^[A-Z]{3}$'), {
    message: 'wrong format currency code',
  })
  code: string;

  @Column({
    default: false,
  })
  @ApiProperty({ default: false, required: false })
  @IsBoolean()
  @IsOptional()
  prefix: boolean;

  @Column({
    default: true,
  })
  @ApiProperty({ default: true, required: false })
  @IsBoolean()
  @IsOptional()
  enabled: boolean;

  @Column({
    default: false,
  })
  syncedFixedRate: boolean;

  @OneToMany((_type) => Branch, (branch) => branch.currency, {
    onDelete: 'CASCADE',
  })
  branches: Branch[];
}
