import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import { Currency } from './currency.entity';

@Injectable()
export class CurrencyService extends BaseCrudService<Currency> {
  constructor(@InjectRepository(Currency) repo: Repository<Currency>) {
    super(repo);
  }
}
