import { OmitType } from '@nestjs/swagger';
import { IsDateString, IsEmail, IsOptional } from 'class-validator';
import { Employee } from '../employee.entity';

export class UpdateEmployeeDto extends OmitType(Employee, [] as const) {
  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsDateString()
  birthDay?: Date;

  @IsOptional()
  @IsDateString()
  startDate?: Date;

  @IsOptional()
  @IsDateString()
  endDate?: Date;
}
