import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, Matches } from 'class-validator';

export class GetEmployeeByServiceAndWorkingHourQueryDto {
  @IsOptional()
  @IsString()
  serviceId?: string;

  @IsString()
  branchId: string;

  @ApiProperty()
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'Invalid date format. Please use YYYY-MM-DD format.',
  })
  startDate: string;

  @ApiProperty()
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'Invalid date format. Please use YYYY-MM-DD format.',
  })
  endDate: string;
}
