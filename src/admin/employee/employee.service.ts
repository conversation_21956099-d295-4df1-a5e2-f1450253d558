import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Brackets,
  Equal,
  In,
  IsNull,
  LessThan,
  LessThanOrEqual,
  MoreThanOrEqual,
  Repository,
} from 'typeorm';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import { Employee } from './employee.entity';
import { CrudRequest } from 'src/core/crud/crud';
import { Customer } from '../customer/customer.entity';
import { ChangeOrderDto } from './dto/employee.dto';
import { clearDateTime, timerToString } from 'src/core/base/utils';
import * as moment from 'moment';
import { WorkingHourService } from '../working-hour/working-hour.service';
import { WorkingHour } from '../working-hour/working-hour.entity';
import { VirtualWorkingHour } from '../working-hour/virtual-working-hour.entity';
import { WHTimework } from '../working-hour/wh-time-work.entity';

@Injectable()
export class EmployeeService extends BaseCrudService<Employee> {
  constructor(
    @InjectRepository(Employee) repo: Repository<Employee>,

    @InjectRepository(WorkingHour)
    private readonly workingHourRepo: Repository<WorkingHour>,
    @InjectRepository(VirtualWorkingHour)
    private readonly virtualWorkingHourRepo: Repository<VirtualWorkingHour>,
  ) {
    super(repo);
  }

  async getEmployees(query: any, curdReq: CrudRequest) {
    const showInactiveTherapists = query.showInactiveTherapists == 'true';

    if (!showInactiveTherapists) {
      curdReq.parsed.search.$and.push({
        'status.name': { $eq: 'Active' },
      });
    }

    return await super.getMany(curdReq);
  }

  async filterEmployee(name: string, branchIds: string[]) {
    const queryBuilder = this.repo.createQueryBuilder('employee');
    queryBuilder.leftJoin('employee.branch', 'branch');
    if (name !== undefined) {
      queryBuilder.andWhere(
        '(employee.fullName ILIKE :name OR employee.displayName ILIKE :name)',
        { name: `%${name}%` },
      );
    }
    if (branchIds.length > 0) {
      queryBuilder.andWhere('branch.id IN (:...branchIds)', { branchIds });
    }
    queryBuilder.addSelect([
      'branch.id',
      'branch.name',
      'branch.email',
      'branch.phones',
      'branch.address',
    ]);
    queryBuilder.orderBy('employee."displayName"', 'ASC');
    const employeeAll = await queryBuilder.getMany();
    return employeeAll;
  }

  async getEmployeeForFilter(crudReq: CrudRequest, customerId: string) {
    const builder = await super.getManyBuilder(crudReq);
    let employeeLikeds = [];

    //get list liked by customerId
    if (customerId) {
      const customer = await this.repo.manager.findOne(Customer, {
        where: { id: customerId },
        relations: ['preferreds'],
      });
      employeeLikeds = (customer.preferreds || []).map((i) => i.id);

      if (employeeLikeds.length > 0) {
        builder
          .orderBy(
            'CASE WHEN "Employee"."id" IN (:...ids) THEN 0 ELSE 1 END, "Employee"."id"',
            'ASC',
          )
          .setParameters({ ids: customer.preferreds.map((i) => i.id) });
      }
    }

    const result = await super.getMany(crudReq);

    if (Array.isArray(result)) {
      return result.map((employee) => {
        return {
          ...employee,
          isLiked: employeeLikeds.includes(employee.id),
        };
      });
    }

    return result;
  }

  async changeOrder(id: string, body: ChangeOrderDto) {
    const { newPosition, beforeId } = body;

    const employee = await this.repo.manager.findOne(Employee, {
      where: {
        id,
      },
    });

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    const employeeBefore = await this.repo.manager.findOne(Employee, {
      where: {
        id: beforeId,
      },
      relations: ['branch'],
    });

    if (employeeBefore) {
      await this.repo.update(
        {
          order: employeeBefore.order,
          branch: {
            id: employeeBefore.branch.id,
          },
          orderTimestamp: LessThan(employeeBefore.orderTimestamp),
        },
        {
          order: newPosition,
        },
      );
      await this.repo.update(
        {
          order: employeeBefore.order,
          branch: {
            id: employeeBefore.branch.id,
          },
          orderTimestamp: MoreThanOrEqual(employeeBefore.orderTimestamp),
        },
        {
          order: newPosition > 1 ? newPosition - 1 : newPosition,
        },
      );
    }

    return await this.repo.manager.save(Employee, {
      id,
      order: newPosition,
      orderTimestamp: new Date(),
    });
  }

  async genEmployeeCode() {
    const count = await this.repo.count();
    let code = `EP${String(count).padStart(5, '0')}`;
    let employeeExists = null;
    do {
      employeeExists = await this.repo.manager.findOne(Employee, {
        where: {
          code,
        },
      });
      code = `EP${String(count + 1).padStart(5, '0')}`;
    } while (employeeExists);

    return code;
  }

  async getEmployeesByWorkingHours({
    branchId,
    serviceId,
    startDate,
    endDate,
    appointmentTime,
    duration = 60, // Default duration 60 minutes if not specified
  }: {
    branchId: string;
    serviceId?: string;
    startDate: string;
    endDate: string;
    appointmentTime?: string; // Specific time for appointment
    duration?: number; // Duration in minutes
  }) {
    const clearedStartDate = moment(startDate).startOf('day').toDate();
    const clearedEndDate = moment(endDate).endOf('day').toDate();

    const rangeDates = Array.from({
      length: moment(clearedEndDate).diff(clearedStartDate, 'days') + 1,
    }).map((_, i) => moment(clearedStartDate).add(i, 'days').utc().toDate());

    // Enhanced query to include active status
    const byBranch = branchId ? { branch: { id: branchId } } : {};
    const byService = serviceId ? { services: { id: serviceId } } : {};
    const employees = await this.repo.find({
      where: {
        ...byBranch,
        ...byService,
        status: { name: 'Active' }, // Only get active employees
      },
      relations: ['branch', 'services', 'status'],
    });

    if (!employees.length) {
      return [];
    }

    const byEmployees = { id: In(employees.map((emp) => emp.id)) };

    const [workingHours, vtWorkingHours] = await Promise.all([
      this.workingHourRepo.find({
        where: {
          date: In(rangeDates),
          employee: byEmployees,
        },
        relations: [
          'employee',
          'branch',
          'shiftTimes',
          'shiftTimes.startTime',
          'shiftTimes.endTime',
        ],
      }),
      this.virtualWorkingHourRepo.find({
        where: rangeDates.flatMap((date) => [
          {
            startDate: LessThanOrEqual(date),
            endDate: MoreThanOrEqual(date),
            employee: byEmployees,
          },
          {
            startDate: LessThanOrEqual(date),
            endDate: IsNull(),
            employee: byEmployees,
          },
        ]),
        relations: [
          'employee',
          'branch',
          'shiftTimes',
          'shiftTimes.startTime',
          'shiftTimes.endTime',
        ],
      }),
    ]);

    const workingHoursHash: Record<string, any> = workingHours.reduce(
      (hs, wh) => {
        hs[wh.employee.id] ??= {};
        hs[wh.employee.id][wh.date.toISOString()] = wh.shiftTimes;
        return hs;
      },
      {},
    );

    const vtWorkingHoursHash: Record<string, any> = vtWorkingHours.reduce(
      (hs, wh) => {
        hs[wh.employee.id] ??= {};
        hs[wh.employee.id][wh.dayOfWeek] ??= [];
        hs[wh.employee.id][wh.dayOfWeek].push(wh);
        return hs;
      },
      {},
    );

    function getWorkingHour(employeeId: string, date: Date) {
      return workingHoursHash[employeeId]?.[date.toISOString()]?.map(
        (shift: WHTimework) => ({
          startTime: timerToString(shift.startTime),
          endTime: timerToString(shift.endTime),
        }),
      );
    }

    function getVtWorkingHour(employeeId: string, date: Date) {
      const dayOfWeek = date.getDay();
      const vts: VirtualWorkingHour[] =
        vtWorkingHoursHash[employeeId]?.[dayOfWeek] || [];

      const targetTime = date.getTime();
      return vts
        .find((vt) => {
          const rangeCheck = [vt.startDate.getTime(), vt.endDate?.getTime()];
          return (
            rangeCheck[0] <= targetTime &&
            (!rangeCheck[1] || targetTime <= rangeCheck[1])
          );
        })
        ?.shiftTimes.map((shift: WHTimework) => ({
          startTime: timerToString(shift.startTime),
          endTime: timerToString(shift.endTime),
        }));
    }

    // Helper function to check if a time falls within working hours
    function isWithinWorkingHours(
      employeeId: string,
      date: Date,
      time: string,
    ) {
      const shifts =
        getWorkingHour(employeeId, date) ??
        getVtWorkingHour(employeeId, date) ??
        [];
      if (!shifts.length) return false;

      const [hours, minutes] = time.split(':').map(Number);
      const appointmentStart = moment(date).set({ hours, minutes });
      const appointmentEnd = moment(appointmentStart).add(duration, 'minutes');

      return shifts.some((shift) => {
        const [startHours, startMinutes] = shift.startTime
          .split(':')
          .map(Number);
        const [endHours, endMinutes] = shift.endTime.split(':').map(Number);
        const shiftStart = moment(date).set({
          hours: startHours,
          minutes: startMinutes,
        });
        const shiftEnd = moment(date).set({
          hours: endHours,
          minutes: endMinutes,
        });

        return (
          appointmentStart.isSameOrAfter(shiftStart) &&
          appointmentEnd.isSameOrBefore(shiftEnd)
        );
      });
    }

    const data = employees.map((employee) => ({
      id: employee.id,
      name: employee.displayName,
      branch: employee.branch?.name,
      services: employee.services,
      workingHours: rangeDates.map((date) => ({
        date,
        shiftTimes:
          getWorkingHour(employee.id, date) ??
          getVtWorkingHour(employee.id, date) ??
          [],
      })),
    }));

    // Filter employees based on appointment time if specified
    let filteredData = data;
    if (appointmentTime) {
      const appointmentDate = moment(startDate).toDate(); // Use start date for appointment
      filteredData = data.filter((employee) =>
        isWithinWorkingHours(employee.id, appointmentDate, appointmentTime),
      );
    }

    const groupedByService = filteredData.reduce((acc, employee) => {
      employee.services.forEach((service) => {
        const { id: serviceId, name: serviceName } = service;
        if (!acc[serviceId]) {
          acc[serviceId] = {
            serviceId,
            serviceName,
            employees: [],
          };
        }
        acc[serviceId].employees.push({
          id: employee.id,
          name: employee.name,
          branch: employee.branch,
        });
      });
      return acc;
    }, {});

    const result = Object.values(groupedByService);

    return result;
  }
}
