import * as moment from 'moment-timezone';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, Repository } from 'typeorm';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { UUID } from 'crypto';
import { PayslipSettingService } from 'src/admin/settings/payslip/payslip-setting.service';
import { WorkingHourService } from 'src/admin/working-hour/working-hour.service';
import { Appointment } from 'src/admin/appointment/appointment.entity';
import { CommissionService } from '../commission/commission.service';
import { Employee } from '../employee.entity';
import { AppointmentStatus } from '../../../core/enums/entity';

@Injectable()
export class PayslipService extends BaseCrudService<Employee> {
  constructor(
    @InjectRepository(Employee) repo: Repository<Employee>,
    private readonly commissionService: CommissionService,
    private readonly payslipSettingService: PayslipSettingService,
    private readonly workingHourService: WorkingHourService,
    @InjectRepository(Appointment)
    private appointmentRepo: Repository<Appointment>,
  ) {
    super(repo);
  }

  async getPayslip(
    name: string,
    employeeId: string,
    branchIds: UUID[],
    filter: [],
  ) {
    let monthYear = moment().format('YYYY-MM');
    let timeZone = null;
    if (filter) {
      for (const f of filter as string[]) {
        const parts = f.split('||');
        if (parts.length === 3 && parts[0] === 'monthYear') {
          monthYear = parts[2];
          break;
        }
      }

      const momentObj = moment(monthYear);
      timeZone = momentObj.format('Z');
      monthYear = momentObj.format('YYYY-MM');
    }
    let commissionList = [];
    commissionList = await this.commissionService.getCommission(
      monthYear,
      name,
      branchIds,
    );
    if (employeeId !== undefined) {
      commissionList = commissionList.filter((item) => item.id === employeeId);
    }

    const payslipSetting =
      await this.payslipSettingService.getAllPayslipSetting(
        branchIds,
        undefined,
      );

    const currentDate = timeZone
      ? moment.tz(monthYear, timeZone)
      : moment.tz(monthYear);
    const startDate = currentDate.startOf('month').toDate();
    const endDate = currentDate.endOf('month').toDate();

    const workingHourList = await this.workingHourService.getList({
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      branchIds,
    });

    const appointmentList = await this.appointmentRepo.find({
      where: { startTime: Between(startDate, endDate) },
      relations: ['orders.items.employees', 'orders.items.product'],
    });

    const payslip = await Promise.all(
      commissionList.map(async (emp) => {
        let totalWorkingHours = 0;
        let totalOTHours = 0;
        if (workingHourList.length > 0) {
          for (const workingHour of workingHourList) {
            let objectWorkingHour = [];
            // working hour total
            if (workingHour.employee.id == emp.id) {
              objectWorkingHour = workingHour.dates;
              for (const date of workingHour.dates) {
                if (date.shiftTimes.length > 0) {
                  totalWorkingHours += await this.calculateTotalWorkHours(
                    date.shiftTimes,
                  );
                }
              }
            }
            // OT hour total
            if (appointmentList.length > 0) {
              for (const appointment of appointmentList) {
                if (
                  appointment.checkOut ||
                  appointment.status?.toLowerCase() ===
                    AppointmentStatus.BOOKING
                ) {
                  for (const order of appointment.orders) {
                    for (const item of order.items) {
                      for (const employee of item.employees) {
                        if (
                          employee?.id === emp.id &&
                          item.product.type === 'service'
                        ) {
                          if (objectWorkingHour.length > 0) {
                            const appointmentStartTime = moment(item.startTime)
                              .clone()
                              .utcOffset(timeZone);
                            const appointmentEndTime = moment(item.endTime)
                              .clone()
                              .utcOffset(timeZone);

                            for (const objectDatetime of objectWorkingHour) {
                              objectDatetime.date = moment(objectDatetime.date)
                                .clone()
                                .utcOffset(timeZone);
                              if (objectDatetime.shiftTimes.length > 0) {
                                const isSameDate = await this.isSameDate(
                                  objectDatetime.date,
                                  appointmentStartTime,
                                );
                                if (isSameDate) {
                                  totalOTHours += await this.calculateOT(
                                    await this.convertShiftTimesToDates(
                                      objectDatetime,
                                      timeZone,
                                    ),
                                    {
                                      dateStartTime: appointmentStartTime,
                                      dateEndTime: appointmentEndTime,
                                    },
                                  );
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
        const payslipSettingInfor = payslipSetting.find(
          (item) => item.employee && item.employee.id === emp.id,
        );
        delete payslipSettingInfor.employee;
        const basicSalary = payslipSettingInfor.basicSalary * totalWorkingHours;
        const oTPaySalary =
          payslipSettingInfor.oTPay * parseFloat(totalOTHours.toFixed(1));
        const totalSalary =
          emp.totalCommission +
          oTPaySalary +
          basicSalary +
          payslipSettingInfor.allowance;
        const employeeDeduction = emp?.employeeCPF
          ? (totalSalary * emp.employeeCPF) / 100
          : 0;
        const employerDeduction = emp?.employerCPF
          ? (totalSalary * emp.employerCPF) / 100
          : 0;
        return {
          ...payslipSettingInfor,
          ...emp,
          totalWorkingHours: totalWorkingHours,
          totalOTHours: parseFloat(totalOTHours.toFixed(1)),
          basicSalary: basicSalary,
          oTPaySalary: oTPaySalary,
          totalSalary: totalSalary,
          employeeDeduction: employeeDeduction,
          employerDeduction: employerDeduction,
          netPay: Math.round(totalSalary - employeeDeduction),
        };
      }),
    );

    return payslip;
  }

  async calculateTotalWorkHours(shiftTimes) {
    let totalMinutes = 0;
    shiftTimes.forEach((shift) => {
      const startTimeParts = shift.startTime.split(':');
      const endTimeParts = shift.endTime.split(':');
      const startMinutes =
        parseInt(startTimeParts[0]) * 60 + parseInt(startTimeParts[1]);
      const endMinutes =
        parseInt(endTimeParts[0]) * 60 + parseInt(endTimeParts[1]);
      totalMinutes += endMinutes - startMinutes;
    });
    const totalWorkingHours = totalMinutes / 60;
    return totalWorkingHours;
  }

  async convertShiftTimesToDates(data, timeZone) {
    const dateObject = new Date(data.date);
    const result = [];

    for (const shift of data.shiftTimes) {
      const startTimeArray = shift.startTime.split(':');
      const dateStartTime = new Date(dateObject);
      dateStartTime.setHours(parseInt(startTimeArray[0], 10));
      dateStartTime.setMinutes(parseInt(startTimeArray[1], 10));

      const endTimeArray = shift.endTime.split(':');
      const dateEndTime = new Date(dateObject);
      dateEndTime.setHours(parseInt(endTimeArray[0], 10));
      dateEndTime.setMinutes(parseInt(endTimeArray[1], 10));

      result.push({
        dateStartTime: moment(dateStartTime).clone().utcOffset(timeZone),
        dateEndTime: moment(dateEndTime).clone().utcOffset(timeZone),
      });
    }

    return result;
  }

  async calculateOT(mainWorkTimes, serviceTime) {
    let totalOT = 0;
    const itemTotal = mainWorkTimes.length;
    let countIsOT = 0;
    for (const mainWorkTime of mainWorkTimes) {
      const mainStart = new Date(mainWorkTime.dateStartTime).getTime();
      const mainEnd = new Date(mainWorkTime.dateEndTime).getTime();

      const serviceStart = new Date(serviceTime.dateStartTime).getTime();
      const serviceEnd = new Date(serviceTime.dateEndTime).getTime();

      let overlapMilliseconds = 0;
      if (serviceStart >= mainStart && serviceStart <= mainEnd) {
        overlapMilliseconds = serviceEnd - mainEnd;
      } else if (serviceStart < mainStart && serviceEnd > mainStart) {
        overlapMilliseconds = mainStart - serviceStart;
      } else {
        countIsOT++;
        if (countIsOT === itemTotal) {
          overlapMilliseconds = serviceEnd - serviceStart;
        }
      }

      const overlapHours = Math.max(overlapMilliseconds / (1000 * 60 * 60), 0);
      totalOT += overlapHours;
    }

    return totalOT;
  }

  async isSameDate(date1, date2) {
    const parsedDate1 = new Date(date1);
    const parsedDate2 = new Date(date2);
    return (
      parsedDate1.getFullYear() === parsedDate2.getFullYear() &&
      parsedDate1.getMonth() === parsedDate2.getMonth() &&
      parsedDate1.getDate() === parsedDate2.getDate()
    );
  }
}
