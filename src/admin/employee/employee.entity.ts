import {
  <PERSON>tity,
  Column,
  OneToMany,
  Index,
  ManyToOne,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Branch } from '../branch/branch.entity';
import { DayOff } from '../day-off/day-off.entity';
import { Setting } from '../setting/setting.entity';
import { Product } from '../product/product.entity';
import { VirtualWorkingHour } from '../working-hour/virtual-working-hour.entity';

@Entity('employee')
@Index('unique_displayname_index', ['branch', 'displayName', 'email'], {
  unique: true,
  where: 'deleted IS NULL',
})
export class Employee extends DocEntity {
  @Column({ unique: true })
  code: string;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  fullName?: string;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  displayName?: string;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiPropertyOptional({ type: () => Setting })
  maritalStatus?: Setting;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  birthDay?: Date;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiPropertyOptional({ type: () => Setting })
  gender?: Setting;

  @Column({
    nullable: true,
  })
  @Index({ unique: true })
  @ApiPropertyOptional()
  phone?: string;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  email?: string;

  @Column({
    type: 'text',
    nullable: true,
  })
  @ApiPropertyOptional()
  address?: string;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  startDate?: Date;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  endDate?: Date;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiPropertyOptional({ type: () => Setting })
  salaryType?: Setting;

  @Column({
    type: 'real',
    nullable: true,
  })
  @ApiPropertyOptional()
  employeeCPF?: number;

  @Column({
    type: 'real',
    nullable: true,
  })
  @ApiPropertyOptional()
  employerCPF?: number;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  nric?: string;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  calendarColor?: string;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Setting })
  status: Setting;

  @ManyToOne(() => Branch, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Branch })
  branch: Branch;

  @OneToMany(() => DayOff, (dayoff) => dayoff.employee)
  @ApiProperty({ type: () => DayOff })
  dayoffs: DayOff[];

  @ManyToMany(() => Product)
  @ApiProperty({ type: () => Product, isArray: true })
  @JoinTable()
  services: Product[];

  @OneToMany(() => VirtualWorkingHour, (virtualWH) => virtualWH.employee)
  @ApiProperty({ type: () => VirtualWorkingHour })
  workingHours: VirtualWorkingHour[];

  // @ManyToMany(() => Customer)
  // @ApiProperty({ type: () => Customer, isArray: true })
  // customerLiked: Customer[];

  @Column({ type: 'bigint', nullable: true })
  order: number;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  orderTimestamp?: Date;
}
