import { BaseCrud } from 'src/admin/auth/decorators/base-crud.decorator';
import { EmployeeBreakTime, EmployeeBreakTimeType } from './break-time.entity';
import { CreateEmployeeBreaktimeDto } from './dto/create-break-time.dto';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { EmployeeBreakTimeService } from './break-time.service';
import { CrudController } from 'src/core/crud/crud';
import {
  Body,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
} from '@nestjs/common';
import { QueryEmployeeBreaktimeDto } from './dto/query-break-time.dto';

@BaseCrud(
  {
    model: {
      type: EmployeeBreakTimeType,
    },
    routes: {
      exclude: [
        'createManyBase',
        'replaceOneBase',
        'createOneBase',
        'deleteOneBase',
        'getManyBase',
        'getOneBase',
        'recoverOneBase',
        'updateOneBase',
      ],
    },
    dto: {
      create: CreateEmployeeBreaktimeDto,
      update: CreateEmployeeBreaktimeDto,
    },
    query: {
      join: {
        employees: { eager: true },
      },
    },
  },
  { prefix: 'appointment' },
)
export class EmployeeBreakTimeController extends BaseCrudController<EmployeeBreakTime> {
  constructor(public service: EmployeeBreakTimeService) {
    super(service);
  }

  get base(): CrudController<EmployeeBreakTime> {
    return this;
  }

  @Post('breaktime')
  async createBreaktime(
    @Req() req: Request,
    @Body() createEmployeeBreaktimeDto: CreateEmployeeBreaktimeDto,
  ) {
    return await this.service.createBreaktime(req, createEmployeeBreaktimeDto);
  }

  @Delete('breaktime/:id')
  async deleteBreaktime(@Param('id') id: string) {
    return await this.service.deleteBreaktime(id);
  }

  @Patch('breaktime/:id')
  async updateBreaktime(
    @Param('id') id: string,
    @Req() req: Request,
    @Body() createEmployeeBreaktimeDto: CreateEmployeeBreaktimeDto,
  ) {
    return await this.service.updateBreaktime(
      id,
      req,
      createEmployeeBreaktimeDto,
    );
  }

  @Get('breaktime/get-all')
  async getBreaktimes(
    @Query() queryEmployeeBreaktimeDto: QueryEmployeeBreaktimeDto,
  ) {
    console.log('asd');
    return await this.service.getBreaktimes(queryEmployeeBreaktimeDto);
  }
}
