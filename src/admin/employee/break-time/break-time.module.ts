import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EmployeeBreakTime } from './break-time.entity';
import { EmployeeBreakTimeController } from './break-time.controller';
import { EmployeeBreakTimeService } from './break-time.service';

@Module({
  imports: [TypeOrmModule.forFeature([EmployeeBreakTime])],
  controllers: [EmployeeBreakTimeController],
  providers: [EmployeeBreakTimeService],
  exports: [EmployeeBreakTimeService],
})
export class EmployeeBreakTimeModule {}
