import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { EmployeeBreakTime } from './break-time.entity';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateEmployeeBreaktimeDto } from './dto/create-break-time.dto';
import { QueryEmployeeBreaktimeDto } from './dto/query-break-time.dto';

@Injectable()
export class EmployeeBreakTimeService extends BaseCrudService<EmployeeBreakTime> {
  constructor(
    @InjectRepository(EmployeeBreakTime) repo: Repository<EmployeeBreakTime>,
  ) {
    super(repo);
  }

  async createBreaktime(
    req: Request,
    createEmployeeBreaktimeDto: CreateEmployeeBreaktimeDto,
  ) {
    try {
      const branchIds = req.headers?.['branchid']?.split(',') || [];
      return await this.repo.save({
        ...createEmployeeBreaktimeDto,
        startTime: new Date(createEmployeeBreaktimeDto.startTime),
        endTime: new Date(createEmployeeBreaktimeDto.endTime),
        branchId: branchIds[0],
      });
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  async updateBreaktime(
    id: string,
    req: Request,
    createEmployeeBreaktimeDto: CreateEmployeeBreaktimeDto,
  ) {
    const found = await this.repo.findOneBy({ id });

    if (!found) {
      throw new NotFoundException('Not found');
    }

    try {
      const branchIds = req.headers?.['branchid']?.split(',') || [];
      await this.repo.update(
        { id },
        {
          ...createEmployeeBreaktimeDto,
          startTime: new Date(createEmployeeBreaktimeDto.startTime),
          endTime: new Date(createEmployeeBreaktimeDto.endTime),
          branchId: branchIds[0],
          employeeId:
            createEmployeeBreaktimeDto.employeeId.length > 0
              ? createEmployeeBreaktimeDto.employeeId
              : null,
        },
      );

      return await this.repo.findOne({
        where: { id },
        relations: ['employee', 'branch'],
      });
    } catch (error) {
      console.log('error', error);
      throw new BadRequestException(error);
    }
  }

  async deleteBreaktime(id: string) {
    const found = await this.repo.findOneBy({ id });

    if (!found) {
      throw new NotFoundException('Not found');
    }

    return await this.repo.softDelete(id);
  }

  async getBreaktimes(queryEmployeeBreaktimeDto: QueryEmployeeBreaktimeDto) {
    try {
      const queryBuilder = this.repo
        .createQueryBuilder('breaktime')
        .leftJoinAndSelect('breaktime.employee', 'employee')
        .leftJoinAndSelect('breaktime.branch', 'branch')
        .where('branch.id = :branchId', {
          branchId: queryEmployeeBreaktimeDto.branchId,
        });

      if (
        queryEmployeeBreaktimeDto.startTime &&
        queryEmployeeBreaktimeDto.endTime
      ) {
        queryBuilder.andWhere(
          'breaktime.startTime BETWEEN :startTime AND :endTime',
          {
            startTime: queryEmployeeBreaktimeDto.startTime,
            endTime: queryEmployeeBreaktimeDto.endTime,
          },
        );
      } else if (queryEmployeeBreaktimeDto.startTime) {
        queryBuilder.andWhere('breaktime.startTime < :startTime', {
          startTime: queryEmployeeBreaktimeDto.startTime,
        });
      } else if (queryEmployeeBreaktimeDto.endTime) {
        queryBuilder.andWhere('breaktime.startTime > :endTime', {
          endTime: queryEmployeeBreaktimeDto.endTime,
        });
      }

      const breaktimes = await queryBuilder.getMany();

      return breaktimes;
    } catch (error) {
      console.error('Error fetching breaktimes:', error.message, error.stack);
      throw new BadRequestException('Failed to fetch breaktimes');
    }
  }
}
