import { ApiPropertyOptional } from '@nestjs/swagger';
import { DocEntity } from 'src/core/base/doc.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { Employee } from '../employee.entity';
import { Branch } from 'src/admin/branch/branch.entity';

export enum EmployeeBreakTimeType {
  BREAKTIME = 'breaktime',
  BLOCKTIME = 'blocktime',
}

@Entity('employee_break_time')
export class EmployeeBreakTime extends DocEntity {
  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  startTime?: Date;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  endTime?: Date;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  type?: string;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  employeeId?: string;

  @ManyToOne(() => Employee)
  @JoinColumn()
  employee?: Employee;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  branchId?: string;

  @ManyToOne(() => Branch)
  @JoinColumn()
  branch?: Branch;
}
