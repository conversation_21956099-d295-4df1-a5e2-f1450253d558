import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, ILike, Repository } from 'typeorm';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import { Employee } from '../employee.entity';
import { EmployeeService } from '../employee.service';
import { CommissionSettingService } from 'src/admin/settings/commission/commission-setting.service';
import { CommissionLogs } from 'src/admin/employee/commission/commission-logs.entity';
import * as moment from 'moment';
@Injectable()
export class CommissionService extends BaseCrudService<Employee> {
  constructor(
    @InjectRepository(Employee) repo: Repository<Employee>,
    // @Inject(forwardRef(() => EmployeeService))
    private readonly employeeService: EmployeeService,
    private readonly commissionSettingService: CommissionSettingService,
    @InjectRepository(CommissionLogs)
    private commissionLogsRepo: Repository<CommissionLogs>,
  ) {
    super(repo);
  }

  async getCommission(monthYear: string, name: string, branchIds: string[]) {
    const employees = await this.employeeService.filterEmployee(
      name,
      branchIds,
    );
    const whereClause: Record<string, any> = { deleted: null };
    if (monthYear !== undefined) {
      whereClause.created = Between(
        moment(monthYear).startOf('month').toDate(),
        moment(monthYear).endOf('month').toDate(),
      );
    } else {
      whereClause.created = Between(
        moment().startOf('month').toDate(),
        moment().endOf('month').toDate(),
      );
    }

    const commissionSetting =
      await this.commissionSettingService.getAllCommissionSetting(
        branchIds,
        undefined,
        undefined,
      );

    const commission = await Promise.all(
      employees.map(async (emp) => {
        whereClause.employee = { id: emp?.id };
        const commissionLogs = await this.commissionLogsRepo.find({
          where: whereClause,
          relations: ['product', 'employee'],
        });
        let serviceCommissionTotal = 0;
        let productCommissionTotal = 0;
        let membershipCommissionTotal = 0;
        if (commissionLogs.length > 0) {
          for (const commission of commissionLogs) {
            const commissionSettingInfor = commissionSetting.find(
              (item) =>
                item.product && item.product.id === commission.product.id,
            );
            if (commission.type === 'service') {
              serviceCommissionTotal +=
                (commission.quantity *
                  (commission.price * commissionSettingInfor.employee)) /
                100;
            }
            if (commission.type === 'product') {
              productCommissionTotal +=
                (commission.quantity *
                  (commission.price * commissionSettingInfor.employee)) /
                100;
            }
            if (commission.type === 'membership') {
              membershipCommissionTotal +=
                (commission.quantity *
                  (commission.price * commissionSettingInfor.employee)) /
                100;
            }
          }
        }
        return {
          ...emp,
          product: productCommissionTotal,
          service: serviceCommissionTotal,
          membership: membershipCommissionTotal,
          totalCommission:
            productCommissionTotal +
            serviceCommissionTotal +
            membershipCommissionTotal,
        };
      }),
    );

    return commission;
  }
}
