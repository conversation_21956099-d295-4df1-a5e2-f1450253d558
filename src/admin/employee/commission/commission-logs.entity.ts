import { Entity, Column, ManyToOne } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty } from '@nestjs/swagger';
import { Employee } from '../employee.entity';
import { Product } from '../../product/product.entity';
import { Invoice } from '../../invoice/invoice.entity';
import { ProductType } from 'src/core/enums/entity';
import { User } from 'src/admin/user/user.entity';
@Entity('commission_logs')
export class CommissionLogs extends DocEntity {
  @ManyToOne(() => Product, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Product })
  product: Product;

  @Column({ type: 'integer', default: 1 })
  @ApiProperty()
  quantity: number;

  @Column({ type: 'real', default: 0 })
  @ApiProperty()
  price: number;

  @Column('enum', {
    enum: ProductType,
    default: ProductType.PRODUCT,
  })
  @ApiProperty()
  type: ProductType;

  @ManyToOne(() => Employee, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Employee })
  employee: Employee;

  @ManyToOne(() => Invoice, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Invoice })
  invoice: Invoice;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @ApiProperty({
    type: () => User,
  })
  user: User;
}
