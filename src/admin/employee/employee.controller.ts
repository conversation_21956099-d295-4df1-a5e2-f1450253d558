import {
  CrudController,
  CrudRequest,
  Override,
  ParsedRequest,
} from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { Employee } from './employee.entity';
import { EmployeeService } from './employee.service';
import { CreateEmployeeDto } from './dto/createEmployee.dto';
import { UpdateEmployeeDto } from './dto/updateEmployee.dto';
import {
  Body,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { WorkingHourService } from '../working-hour/working-hour.service';
import { CreateWorkHourDto } from '../working-hour/dto/createWorkingHour.dto';
import { DeleteWorkingHourQueryDto } from '../working-hour/dto/deleteWorkingHour.dto';
import { ApiQuery } from '@nestjs/swagger';
import { CommissionService } from './commission/commission.service';
import { PayslipService } from './payslip/payslip.service';
import { ChangeOrderDto } from './dto/employee.dto';
import { UUID } from 'crypto';
import { GetEmployeeByServiceAndWorkingHourQueryDto } from './dto/get-by-service.dto';

@BaseCrud(
  {
    model: {
      type: Employee,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    dto: {
      create: CreateEmployeeDto,
      update: UpdateEmployeeDto,
    },
    query: {
      join: {
        branch: { eager: true, allow: ['id', 'name'] },
        status: { eager: true, allow: ['id', 'name'] },
        saralyType: { eager: true, allow: ['id', 'name'] },
        gender: { eager: true, allow: ['id', 'name'] },
        maritalStatus: { eager: true, allow: ['id', 'name'] },
        salaryType: { eager: true, allow: ['id', 'name'] },
        services: { eager: true, allow: ['id', 'name'] },
      },
      filter: {
        $or: [
          { fullName: { $cont: '' } },
          { displayName: { $contL: '' } },
          { phone: { $contL: '' } },
          { email: { $contL: '' } },
        ],
      },
      sort: [
        {
          field: 'order',
          order: 'ASC',
        },
        {
          field: 'orderTimestamp',
          order: 'DESC',
        },
      ],
    },
  },
  {
    // grantPerm: userPerm,
    // group: ResourceGroup.SYSTEM,
  },
)
export class EmployeeController extends BaseCrudController<Employee> {
  constructor(
    public service: EmployeeService,
    public workingHourService: WorkingHourService,
    private readonly commissionService: CommissionService,
    private readonly payslipService: PayslipService,
  ) {
    super(service);
  }

  get base(): CrudController<Employee> {
    return this;
  }

  @Override('getManyBase')
  async getEmployees(
    @Query() query: any,
    @ParsedRequest() curdReq: CrudRequest,
  ) {
    return this.service.getEmployees(query, curdReq);
  }

  @Get('/filter')
  async getEmployeeForFilter(
    @ParsedRequest() req: CrudRequest,
    @Query() { customerId }: { customerId: string },
  ) {
    return this.service.getEmployeeForFilter(req, customerId);
  }

  @Override('createOneBase')
  async createOneOverride(
    @Body() body: CreateEmployeeDto,
    @ParsedRequest() crudRequest: CrudRequest,
  ) {
    body.orderTimestamp = new Date();
    if (!body.code) {
      body.code = await this.service.genEmployeeCode();
    }

    return this.service.createOne(crudRequest, body);
  }

  @Post('/:id/working-hour')
  async createWorkingHour(
    @Body() data: CreateWorkHourDto,
    @Param('id') employeeId: string,
  ): Promise<boolean> {
    await this.workingHourService.setWorkingHourV1(employeeId, data);
    return true;
  }

  @Delete('/:id/working-hour')
  @ApiQuery({ name: 'date', required: true })
  @ApiQuery({ name: 'repeat', required: false, allowEmptyValue: true })
  async deleteWorkingHour(
    @Query() query: DeleteWorkingHourQueryDto,
    @Param('id') employeeId: UUID,
  ): Promise<boolean> {
    //get list working-hour
    // const listWorkingHour = await this.workingHourService.getList({
    //   startDate: query.date,
    //   endDate: query.date,
    //   employeeId,
    // });

    // const timeArray = listWorkingHour[0].dates[0].shiftTimes;

    // const foundObject = timeArray.filter(
    //   (obj) =>
    //     !moment(obj.startTime, 'HH:mm').isSame(
    //       moment(query.startTime, 'HH:mm'),
    //     ) ||
    //     !moment(obj.endTime, 'HH:mm').isSame(moment(query.endTime, 'HH:mm')),
    // );

    //set working-hour
    // await this.workingHourService.setWorkingHour(employeeId, {
    //   date: clearDateTime(query.date),
    //   shiftTimes: foundObject.map((time) => {
    //     const [hourStart, minuteStart] = time.startTime.split(':');
    //     const [hourEnd, minuteEnd] = time.endTime.split(':');
    //     return {
    //       startTime: {
    //         hour: +hourStart,
    //         minute: +minuteStart,
    //       },
    //       endTime: {
    //         hour: +hourEnd,
    //         minute: +minuteEnd,
    //       },
    //     };
    //   }) as any,
    //   repeat: query.repeat,
    // });

    await this.workingHourService.deleteWorkingHour(employeeId, query);
    return true;
  }

  @Get('/commision')
  commision(
    @Req() req: Request,
    @Query()
    query: { monthYear: string; name: string },
  ) {
    const branchIds = req.headers?.['branchid']?.split(',') || [];
    return this.commissionService.getCommission(
      query.monthYear,
      query.name,
      branchIds,
    );
  }

  @Get('/payslip')
  payslip(
    @Req() req: Request,
    @Query()
    query: {
      name: string;
      employeeId: string;
      filter: [];
    },
  ) {
    const branchIds = req.headers?.['branchid']?.split(',') || [];
    return this.payslipService.getPayslip(
      query.name,
      query.employeeId,
      branchIds,
      query.filter,
    );
  }

  @Patch('/change-order/:id')
  async changeOrder(@Param('id') id: string, @Body() body: ChangeOrderDto) {
    return this.service.changeOrder(id, body);
  }

  @Get('get-by-service')
  async getEmployeeByServiceAndWorkingHour(
    @Query() query: GetEmployeeByServiceAndWorkingHourQueryDto,
  ) {
    return await this.service.getEmployeesByWorkingHours({
      branchId: query.branchId,
      serviceId: query.serviceId,
      startDate: query.startDate,
      endDate: query.endDate,
    });
  }
}
