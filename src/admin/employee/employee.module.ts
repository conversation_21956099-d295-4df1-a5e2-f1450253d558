import { TypeOrmModule } from '@nestjs/typeorm';
import { Module, forwardRef } from '@nestjs/common';
import { EmployeeController } from './employee.controller';
import { Employee } from './employee.entity';
import { EmployeeService } from './employee.service';
import { WorkingHourModule } from '../working-hour/working-hour.module';
import { CommissionService } from './commission/commission.service';
import { PayslipService } from './payslip/payslip.service';
import { CommissionSettingModule } from '../settings/commission/commission-setting.module';
import { CommissionLogs } from './commission/commission-logs.entity';
import { PayslipSettingModule } from '../settings/payslip/payslip-setting.module';
import { Appointment } from '../appointment/appointment.entity';
import { WorkingHour } from '../working-hour/working-hour.entity';
import { VirtualWorkingHour } from '../working-hour/virtual-working-hour.entity';
import { EmployeeBreakTimeModule } from './break-time/break-time.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Employee,
      CommissionLogs,
      Appointment,
      WorkingHour,
      VirtualWorkingHour,
    ]),
    WorkingHourModule,
    CommissionSettingModule,
    forwardRef(() => PayslipSettingModule),
    EmployeeBreakTimeModule,
  ],
  controllers: [EmployeeController],
  providers: [EmployeeService, CommissionService, PayslipService],
  exports: [EmployeeService, CommissionService, PayslipService],
})
export class EmployeeModule {}
