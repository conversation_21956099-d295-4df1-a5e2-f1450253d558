import { Entity, Column, ManyToOne } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { Role } from '../role/role.entity';
import { Resource } from '../resource/resource.entity';

@Entity('permission')
export class Permission extends DocEntity {
  @Column({ nullable: true })
  create: boolean;

  @Column({ nullable: true })
  read: boolean;

  @Column({ nullable: true })
  list: boolean;

  @Column({ nullable: true })
  update: boolean;

  @Column({ nullable: true })
  delete: boolean;

  @Column({ nullable: true })
  print: boolean;

  @Column({ nullable: true })
  download: boolean;

  @Column({ nullable: true })
  active: boolean;

  @Column({ nullable: true })
  changePass: boolean;

  @Column({ nullable: true })
  listCalendar: boolean;

  @Column({ nullable: true })
  addAppointment: boolean;

  @Column({ nullable: true })
  export: boolean;

  @Column({ nullable: true })
  sendMail: boolean;

  @Column({ nullable: true })
  checkOut: boolean;

  @ManyToOne(() => Role, (role) => role.permission)
  role: Role;

  @ManyToOne(() => Resource, (resource) => resource.permission, {
    nullable: false,
  })
  resource: Resource;

  @Column({ nullable: true })
  changePaymentSingleDay: boolean;

  @Column({ nullable: true })
  changePaymentAllDays: boolean;

  @Column({ nullable: true })
  editSingleDay: boolean;

  @Column({ nullable: true })
  editAllDays: boolean;

  @Column({ nullable: true })
  refund: boolean;
}
