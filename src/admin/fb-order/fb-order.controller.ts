import {
  <PERSON>rudController,
  CrudRequest,
  CrudRequestInterceptor,
  ParsedRequest,
} from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { FbOrder } from './fb-order.entity';
import { FbOrderService } from './fb-order.service';
import { CreateFbOrderDto } from './dto/createFbOrder.dto';
import { Get, UseInterceptors } from '@nestjs/common';
@BaseCrud(
  {
    model: {
      type: FbOrder,
    },
    routes: {
      only: ['getManyBase', 'updateOneBase'],
    },
    dto: {
      create: CreateFbOrderDto,
    },
    query: {
      join: {
        status: { eager: true, allow: ['id', 'name'] },
        items: {
          eager: true,
          allow: [
            'id',
            'quantity',
            'product',
            'fbOrder',
            'price',
            'employees',
            'note',
          ],
        },
        'items.product': {
          eager: true,
          alias: 'product',
          allow: [
            'id',
            'type',
            'credit',
            'category',
            'name',
            'price',
            'creditType',
          ],
        },
        'items.fbOrder': {
          eager: true,
          alias: 'fbOrder',
          allow: ['id', 'productType'],
        },
        'items.product.category': {
          eager: true,
          alias: 'category',
          allow: ['id', 'name'],
        },
        'items.employees': {
          eager: true,
          alias: 'employees',
          allow: ['id', 'fullName', 'displayName'],
        },
        appointment: { eager: true, allow: ['id', 'name', 'rfid', 'rfids'] },
        'appointment.rfids': {
          eager: true,
          alias: 'rfids',
          allow: ['id', 'lockerNumber', 'group', 'serialCode'],
        },
        'appointment.rfids.group': {
          eager: true,
          alias: 'group',
          allow: ['id', 'name'],
        },
        branch: { eager: true, allow: ['id', 'name', 'address', 'phones'] },
      },
      filter: {
        'appointment.rfid': { $notnull: true },
      },
    },
  },
  {
    // grantPerm: userPerm,
    // group: ResourceGroup.SYSTEM,
  },
)
export class OrderController extends BaseCrudController<FbOrder> {
  constructor(public service: FbOrderService) {
    super(service);
  }
  get base(): CrudController<FbOrder> {
    return this;
  }

  @UseInterceptors(CrudRequestInterceptor)
  @Get('/count-by-status')
  async countByStatus(@ParsedRequest() crudReq: CrudRequest) {
    return this.service.countByStatus(crudReq);
  }
}
