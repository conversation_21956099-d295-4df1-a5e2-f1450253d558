import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { OrderController } from './fb-order.controller';
import { FbOrder } from './fb-order.entity';
import { FbOrderService } from './fb-order.service';
import { Rfid } from '../rfid/rfid.entity';
import { Appointment } from '../appointment/appointment.entity';

@Module({
  imports: [TypeOrmModule.forFeature([FbOrder, Rfid, Appointment])],
  controllers: [OrderController],
  providers: [FbOrderService],
  exports: [FbOrderService],
})
export class FbOrderModule {}
