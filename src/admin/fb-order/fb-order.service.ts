import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { FbOrder } from './fb-order.entity';
import { CrudRequest } from 'src/core/crud/crud';

@Injectable()
export class FbOrderService extends BaseCrudService<FbOrder> {
  constructor(@InjectRepository(FbOrder) repo: Repository<FbOrder>) {
    super(repo);
  }

  async countByStatus(crudReq: CrudRequest) {
    const masterQuery = this.repo
      .createQueryBuilder('FbOrder')
      .select(['"FbOrder".status AS status', 'COUNT(*) AS total'])
      .leftJoin('FbOrder.appointment', 'appointment')
      .leftJoin('FbOrder.branch', 'branch')
      .groupBy('"FbOrder".status');

    this.setSearchCondition(masterQuery, crudReq.parsed.search);

    return await masterQuery.getRawMany();
  }
}
