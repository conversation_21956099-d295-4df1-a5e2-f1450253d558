import {
  <PERSON>rud<PERSON><PERSON>roller,
  CrudRequest,
  CrudRequestInterceptor,
  Override,
  ParsedRequest,
} from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { Order } from './order.entity';
import { OrderService } from './order.service';
import { OrderDto } from './dto/createOrder.dto';
import {
  Body,
  Get,
  Param,
  Post,
  Query,
  Req,
  UseInterceptors,
} from '@nestjs/common';
import { CreateOrderFromMobileDto } from './dto/createOrderFromMobile.dto';
import { Public } from '../auth/decorators/public.decerator';
import { TransferFbOrder } from './dto/transferFbOrder.dto';
import { filterOrderItemsForResponse } from './order-response.helper';

@BaseCrud(
  {
    model: {
      type: Order,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    dto: {
      create: OrderDto,
    },
    query: {
      join: {
        status: { eager: true, allow: ['id', 'name'] },
        invoice: {
          eager: true,
          allow: ['id'],
        },
        items: {
          eager: true,
          allow: [
            'id',
            'quantity',
            'product',
            'order',
            'price',
            'employees',
            'note',
            'startTime',
            'endTime',
            'couponCode',
          ],
        },
        'items.product': {
          eager: true,
          alias: 'product',
          allow: [
            'id',
            'type',
            'credit',
            'category',
            'name',
            'price',
            'creditType',
          ],
        },
        'items.duration': {
          eager: true,
          alias: 'duration',
        },
        'items.order': {
          eager: true,
          alias: 'order',
          allow: ['id'],
        },
        'items.product.category': {
          eager: true,
          alias: 'category',
          allow: ['id', 'name'],
        },
        'items.product.avatar': {
          eager: true,
          alias: 'productAvatar',
          allow: ['id', 'url'],
        },
        'items.employees': {
          eager: true,
          alias: 'employees',
          allow: ['id', 'fullName', 'displayName'],
        },
        appointment: { eager: true, allow: ['id', 'name', 'rfid', 'rfids'] },
        'appointment.rfids': {
          eager: true,
          alias: 'rfids',
          allow: ['id', 'lockerNumber', 'group', 'serialCode'],
        },
        'appointment.rfids.group': {
          eager: true,
          alias: 'group',
          allow: ['id', 'name'],
        },
        'appointment.customer': {
          eager: true,
          alias: 'customer',
        },
        'appointment.customer.gender': {
          eager: true,
          alias: 'gender',
        },
        'appointment.customer.avatar': {
          eager: true,
        },
        'appointment.customer.credits': {
          eager: true,
          alias: 'credits',
        },
        'appointment.customer.credits.creditSetting': {
          eager: true,
        },
        'appointment.customer.preferreds': {
          eager: true,
        },
        'appointment.customer.nationality': {
          eager: true,
        },
        branch: { eager: true, allow: ['id', 'name', 'address', 'phones'] },
      },
      filter: {
        'appointment.rfid': { $notnull: true },
      },
    },
  },
  {
    // grantPerm: userPerm,
    // group: ResourceGroup.SYSTEM,
  },
)
export class OrderController extends BaseCrudController<Order> {
  constructor(public service: OrderService) {
    super(service);
  }

  get base(): CrudController<Order> {
    return this;
  }

  @Override('getManyBase')
  async getMany(
    @ParsedRequest() crudReq: CrudRequest,
    @Query('keySearch') keySearch: string,
  ) {
    return this.service.getListOrder(crudReq, keySearch);
  }

  @Override('createOneBase')
  async createOne(@Req() req: Request, @Body() dto: any) {
    const result = await this.service.createOneOrder(req, dto);
    if (Array.isArray(result)) {
      return result.map((order) => filterOrderItemsForResponse(order));
    }
    return filterOrderItemsForResponse(result);
  }

  @Override('updateOneBase')
  async updateOne(
    @Req() req: Request,
    @Body() dto: any,
    @Param('id') id: string,
  ) {
    return this.service.updateOneOrder(req, dto, id);
  }

  @Public()
  @Post('/from-mobile')
  async orderFromMobile(
    @Body() dto: CreateOrderFromMobileDto,
    @Req() req: Request,
  ): Promise<any> {
    return this.service.orderFromMobile(req, dto);
  }

  @UseInterceptors(CrudRequestInterceptor)
  @Post('transfer')
  async transferCustomerOrder(
    @Req() req: Request,
    @Body() dto: TransferFbOrder,
  ) {
    return this.service.transferCustomerOrder(req, dto);
  }

  @UseInterceptors(CrudRequestInterceptor)
  @Get('/count-by-status')
  async countByStatus(@ParsedRequest() crudReq: CrudRequest) {
    return this.service.countByStatus(crudReq);
  }
}
