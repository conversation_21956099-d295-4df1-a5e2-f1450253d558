import { Entity, Column, OneToMany, ManyToOne } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { OrderDetail } from '../order-detail/order-detail.entity';
import { Appointment } from '../appointment/appointment.entity';
import { Invoice } from '../invoice/invoice.entity';
import { Employee } from '../employee/employee.entity';
import { Branch } from '../branch/branch.entity';
import { InvoiceStatus, OrderType } from 'src/core/enums/entity';
import { Customer } from '../customer/customer.entity';
@Entity()
export class Order extends DocEntity {
  @Column({ type: 'bigint', nullable: false })
  code: number;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional({ type: () => String })
  note?: string;

  @ApiProperty({ type: () => OrderDetail, isArray: true })
  @OneToMany(() => OrderDetail, (orderDetail) => orderDetail.order)
  items: OrderDetail[];

  @ManyToOne(() => Appointment, {
    onDelete: 'SET NULL',
  })
  @ApiProperty({
    type: () => Appointment,
  })
  appointment: Appointment;

  @ApiProperty({ type: () => Invoice })
  @ManyToOne(() => Invoice)
  invoice: Invoice;

  @Column({ type: 'integer', default: 0 })
  @ApiPropertyOptional()
  discount?: number;

  @Column({ type: 'boolean', default: false })
  isDraft: boolean;

  @Column({ type: 'boolean', default: false })
  isPrinted: boolean;

  @Column({
    default: InvoiceStatus.UNPAID,
    type: 'enum',
    enum: Object.values(InvoiceStatus),
  })
  @ApiProperty()
  status: InvoiceStatus;

  @ManyToOne(() => Employee, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Employee })
  employee: Employee;

  @Column({ type: 'real', nullable: true, default: 0 })
  @ApiPropertyOptional()
  tax?: number;

  @Column({ type: 'real', nullable: true, default: 0 })
  @ApiPropertyOptional()
  total?: number;

  @Column({ type: 'real', nullable: true, default: 0 })
  @ApiPropertyOptional()
  subTotal?: number;

  @Column({ type: 'real', nullable: true, default: 0 })
  @ApiPropertyOptional()
  totalBeforeTax?: number;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional({ type: () => String })
  discountProductId?: string;

  @Column({ type: 'real', nullable: true, default: 0 })
  @ApiPropertyOptional()
  discountMoney?: number;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional({ type: () => String })
  couponCode?: string;

  @Column({
    nullable: true,
    type: 'jsonb',
  })
  @ApiProperty()
  payload: object;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional({ type: () => String })
  couponName?: string;

  @ManyToOne(() => Branch, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Branch })
  branch: Branch;

  @Column('enum', {
    enum: OrderType,
    default: OrderType.OTHERS,
  })
  @ApiProperty()
  orderType: OrderType;

  @Column({ type: 'boolean', default: false })
  isComplete: boolean;

  @ManyToOne(() => Customer, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Customer })
  transferBy: Customer;

  @Column({ type: 'boolean', default: false })
  isEdited: boolean;
}
