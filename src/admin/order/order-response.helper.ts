import { cloneDeep } from 'lodash';

export function filterOrderItemsForResponse(order: any) {
  const filteredOrder = cloneDeep(order);

  if (
    !Array.isArray(filteredOrder.items) &&
    Array.isArray(filteredOrder.payload?.items)
  ) {
    filteredOrder.items = [...filteredOrder.payload.items];
  }

  const calcTotal = (items: any[]) =>
    items.reduce(
      (sum, item) => sum + (item.price || 0) * (item.quantity || 1),
      0,
    );

  const couponItems = (filteredOrder.items || []).filter(
    (item: any) => item.product?.type === 'coupon' && item.couponCode,
  );
  const couponCodes = couponItems.map((item: any) => item.couponCode);

  const isAddonOfCoupon = (item: any) =>
    item.product?.type !== 'coupon' &&
    item.couponCode &&
    couponCodes.includes(item.couponCode);

  filteredOrder.items = (filteredOrder.items || []).filter(
    (item: any) => !isAddonOfCoupon(item),
  );
  filteredOrder.subTotal = calcTotal(filteredOrder.items);
  filteredOrder.total = filteredOrder.subTotal;
  filteredOrder.totalBeforeTax = filteredOrder.subTotal;

  if (filteredOrder.payload && Array.isArray(filteredOrder.payload.items)) {
    filteredOrder.payload.items = filteredOrder.payload.items.filter(
      (item: any) => !isAddonOfCoupon(item),
    );
    filteredOrder.payload.subTotal = calcTotal(filteredOrder.payload.items);
    filteredOrder.payload.total = filteredOrder.payload.subTotal;
    filteredOrder.payload.totalBeforeTax = filteredOrder.payload.subTotal;
  }

  return filteredOrder;
}
