import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { OrderController } from './order.controller';
import { Order } from './order.entity';
import { OrderService } from './order.service';
import { Rfid } from '../rfid/rfid.entity';
import { Appointment } from '../appointment/appointment.entity';
import { AuditTrailModule } from '../audit-trail/audit-trail.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Order, Rfid, Appointment]),
    AuditTrailModule,
  ],
  controllers: [OrderController],
  providers: [OrderService],
  exports: [OrderService],
})
export class OrderModule {}
