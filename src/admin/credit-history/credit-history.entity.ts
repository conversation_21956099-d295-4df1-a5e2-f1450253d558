import { <PERSON>tity, Column, ManyTo<PERSON>ne, OneToMany } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Credit } from '../credit/credit.entity';
import { User } from '../user/user.entity';
import { Invoice } from '../invoice/invoice.entity';
import { Product } from '../product/product.entity';
import { Customer } from '../customer/customer.entity';

@Entity('credit_history')
export class CreditHistory extends DocEntity {
  @Column({
    nullable: false,
    type: 'jsonb',
  })
  @ApiProperty()
  detail: object;

  @Column({ type: 'real', nullable: false })
  @ApiProperty()
  paid: number;

  @ManyToOne(() => Credit, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Credit })
  credit: Credit;

  @Column({ type: 'boolean', default: false })
  isMembershipPkg: boolean;

  @Column({ type: 'boolean', default: false })
  isRefund: boolean;

  @Column({ type: 'boolean', default: false })
  isRefunded: boolean;

  @Column({ type: 'boolean', default: false })
  isPurgeExpired: boolean;

  @Column({ type: 'real', nullable: true })
  @ApiProperty()
  opening: number;

  @Column({ type: 'real', nullable: true })
  @ApiProperty()
  closing: number;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiProperty()
  expiryDate: Date;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiProperty()
  lastConsumed: Date;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiProperty()
  purgedDate: Date;

  @Column({ type: 'real', nullable: true })
  @ApiPropertyOptional()
  usable?: number;

  @Column({ type: 'real', nullable: true })
  @ApiPropertyOptional()
  purged?: number;

  @Column({ type: 'real', nullable: true })
  @ApiPropertyOptional()
  refund?: number;

  @Column({ type: 'real', nullable: true })
  @ApiPropertyOptional()
  value?: number;

  @Column({ type: 'real', nullable: true })
  @ApiPropertyOptional()
  refundValue?: number;

  @Column({ type: 'real', nullable: true })
  @ApiPropertyOptional()
  refundBeforeTax?: number;

  @Column({ type: 'real', nullable: true })
  @ApiPropertyOptional()
  tax?: number;

  @ManyToOne(() => CreditHistory, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => CreditHistory })
  membership: CreditHistory;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => User })
  refundedBy: User;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  refundDate: Date;

  @ManyToOne(() => Invoice, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Invoice })
  invoice: Invoice;

  @ManyToOne(() => Product, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Product })
  product: Product;

  @ManyToOne(() => Customer, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Customer })
  customer: Customer;

  @Column({ type: 'boolean', default: false })
  isPassportHistory: boolean;

  @Column({ type: 'real', nullable: true })
  @ApiProperty()
  creditBefore: number;

  @Column({
    nullable: true,
    type: 'jsonb',
  })
  @ApiProperty()
  refundDto: object;
}
