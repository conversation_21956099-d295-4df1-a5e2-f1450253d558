import { <PERSON>tity, Column, ManyToOne } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty } from '@nestjs/swagger';
import { CreditHistory } from './credit-history.entity';
import { Invoice } from '../invoice/invoice.entity';

@Entity('membership_history')
export class MembershipHistory extends DocEntity {
  @ManyToOne(() => CreditHistory, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => CreditHistory })
  membership: CreditHistory;

  @Column({ type: 'real', nullable: false })
  @ApiProperty()
  paid: number;

  @ManyToOne(() => Invoice, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Invoice })
  invoice: Invoice;
}
