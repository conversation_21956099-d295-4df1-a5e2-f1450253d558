import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { CreditHistoryController } from './credit-history.controller';
import { CreditHistory } from './credit-history.entity';
import { CreditHistoryService } from './credit-history.service';
import { MembershipHistory } from './membership-history.entity';
import { CreditModule } from '../credit/credit.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([CreditHistory, MembershipHistory]),
    CreditModule,
  ],
  controllers: [CreditHistoryController],
  providers: [CreditHistoryService],
  exports: [CreditHistoryService],
})
export class CreditHistoryModule {}
