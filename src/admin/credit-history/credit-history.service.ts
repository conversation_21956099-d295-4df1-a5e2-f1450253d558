import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import { CreditHistory } from './credit-history.entity';
import { CreditService } from '../credit/credit.service';

@Injectable()
export class CreditHistoryService extends BaseCrudService<CreditHistory> {
  constructor(
    @InjectRepository(CreditHistory) repo: Repository<CreditHistory>,

    private readonly creditService: CreditService,
  ) {
    super(repo);
  }

  async getCreditRefundPreview(creditId: string, req: any) {
    return await this.creditService.previewCreditMembershipRefund(
      creditId,
      req,
    );
  }
}
