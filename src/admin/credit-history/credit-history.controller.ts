import { CrudController, Override } from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { CreditHistory } from './credit-history.entity';
import { CreditHistoryService } from './credit-history.service';
import { CreditHistoryDto } from './dto/createCreditHistory.dto';
import { Get, Param, Req } from '@nestjs/common';

@BaseCrud(
  {
    model: {
      type: CreditHistory,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    dto: {
      create: CreditHistoryDto,
    },
    query: {
      join: {
        credit: { eager: true, allow: ['id', 'total'] },
        'credit.customer': {
          eager: true,
          allow: ['id', 'firstName', 'lastName'],
        },
      },
    },
  },
  {
    // grantPerm: userPerm,
    // group: ResourceGroup.SYSTEM,
  },
)
export class CreditHistoryController extends BaseCrudController<CreditHistory> {
  constructor(public service: CreditHistoryService) {
    super(service);
  }
  get base(): CrudController<CreditHistory> {
    return this;
  }

  @Override('getOneBase')
  @Get('/:id')
  async getCreditRefundPreview(
    @Req() req: Request,
    @Param() params: { id: string },
  ) {
    return this.service.getCreditRefundPreview(params.id, req);
  }
}
