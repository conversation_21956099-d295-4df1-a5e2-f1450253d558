import {
  CrudController,
  CrudRequest,
  Override,
  ParsedRequest,
} from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { Setting } from './setting.entity';
import { SettingService } from './setting.service';
import { CreateSettingDto } from './dto/createSetting.dto';
import { UpdateSettingDto } from './dto/updateSetting.dto';
import { Query } from '@nestjs/common';
import { RequestQueryParser } from 'src/core/crud/crud-request';

@BaseCrud(
  {
    model: {
      type: Setting,
    },
    routes: {
      only: ['getManyBase'],
    },
    dto: {
      create: CreateSettingDto,
      update: UpdateSettingDto,
    },
  },
  {},
)
export class SettingController extends BaseCrudController<Setting> {
  constructor(public service: SettingService) {
    super(service);
  }
  get base(): CrudController<Setting> {
    return this;
  }

  @Override('getManyBase')
  async getMany(@ParsedRequest() req: CrudRequest) {
    return this.service.getAllSetting(req);
  }
}
