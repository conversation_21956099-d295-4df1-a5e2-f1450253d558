import { ApiProperty, ApiPropertyOptional, OmitType } from '@nestjs/swagger';
import { Setting } from '../setting.entity';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class UpdateSettingDto extends OmitType(Setting, [] as const) {
  @ApiProperty()
  @IsOptional()
  @IsString()
  type: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  name: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  order?: number;
}
