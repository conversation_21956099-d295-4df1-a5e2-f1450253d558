import { ApiProperty, ApiPropertyOptional, OmitType } from '@nestjs/swagger';
import { Setting } from '../setting.entity';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateSettingDto extends OmitType(Setting, [] as const) {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  type: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  order?: number;
}
