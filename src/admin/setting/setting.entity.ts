import { Entity, Column } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
@Entity('setting')
export class Setting extends DocEntity {
  @Column({
    nullable: false,
  })
  @ApiProperty()
  type: string;

  @Column({
    nullable: false,
  })
  @ApiProperty()
  name: string;

  @Column({
    nullable: true,
  })
  @ApiProperty()
  order?: number;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  value?: number;
}
