import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import { Setting } from './setting.entity';
import { CrudRequest } from 'src/core/crud/crud';

@Injectable()
export class SettingService extends BaseCrudService<Setting> {
  constructor(@InjectRepository(Setting) repo: Repository<Setting>) {
    super(repo);
  }

  async getAllSetting(req: CrudRequest) {
    const masterQuery = this.repo
      .createQueryBuilder('setting')
      .select('setting.type')
      .addSelect('setting.name')
      .addSelect('setting.id')
      .where('setting.type IS NOT NULL')
      .orderBy('setting.order', 'ASC');

    if (req.parsed.filter) {
      for (const f of req.parsed.filter) {
        if (f.field === 'type') {
          masterQuery.andWhere('setting.type IN (:...type)', { type: f.value });
        }
      }
    }

    const settings = await masterQuery.getMany();
    // Group the settings by type
    return settings.reduce((groups, setting) => {
      const { type, name, id } = setting;
      if (!groups[type]) {
        groups[type] = [];
      }
      groups[type].push({ id, name });
      return groups;
    }, {});
  }
}
