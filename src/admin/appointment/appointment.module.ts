import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { AppointmentController } from './appointment.controller';
import { Appointment } from './appointment.entity';
import { AppointmentService } from './appointment.service';
import { OrderDetail } from '../order-detail/order-detail.entity';
import { Employee } from '../employee/employee.entity';
import { Customer } from '../customer/customer.entity';
import { Product } from '../product/product.entity';
import { Order } from '../order/order.entity';
import { WorkingHourModule } from '../working-hour/working-hour.module';
import { EmployeeModule } from '../employee/employee.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Appointment,
      Order,
      OrderDetail,
      Employee,
      Customer,
      Product,
    ]),
    WorkingHourModule,
    EmployeeModule,
  ],
  controllers: [AppointmentController],
  providers: [AppointmentService],
  exports: [AppointmentService],
})
export class AppointmentModule {}
