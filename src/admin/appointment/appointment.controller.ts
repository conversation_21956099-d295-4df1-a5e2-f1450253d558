import {
  <PERSON>rud<PERSON><PERSON>roller,
  CrudRequest,
  CrudRequestInterceptor,
  Override,
  ParsedBody,
  ParsedRequest,
} from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { Appointment } from './appointment.entity';
import { AppointmentService } from './appointment.service';
import {
  BadRequestException,
  Body,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseInterceptors,
} from '@nestjs/common';
import { ChangeOrderDto, CheckDuplicateDto } from './dto/appointment.dto';
import { AvailabilityQueryDto } from './dto/availabilityEmployess.dto';

@BaseCrud(
  {
    model: {
      type: Appointment,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    query: {
      join: {
        status: { eager: true, allow: ['id', 'name'] },
        orders: { eager: true, allow: ['id', 'note', 'items', 'orderType'] },
        'orders.items': {
          eager: true,
          alias: 'items',
          allow: [
            'id',
            'note',
            'product',
            'startTime',
            'endTime',
            'quantity',
            'price',
            'duration',
            'discount',
            'status',
          ],
        },
        'orders.items.product': {
          eager: true,
          alias: 'product',
          allow: [
            'id',
            'type',
            'credit',
            'category',
            'name',
            'price',
            'products',
          ],
        },
        'orders.items.product.products': {
          eager: true,
          alias: 'products',
          allow: ['id', 'type', 'name'],
        },
        'orders.items.employees': {
          eager: true,
          alias: 'employee',
          allow: ['id', 'displayName', 'calendarColor'],
        },
        'orders.items.duration': {
          eager: true,
          alias: 'duration',
          allow: ['id', 'name'],
        },
        customer: {
          eager: true,
          alias: 'customer',
          allow: ['id', 'firstName', 'lastName', 'phone'],
        },
      },
      filter: {
        $or: [
          {
            'customer.lastName': { $contInStr: '' },
          },
          { 'customer.phone': { $contL: '' } },
        ],
        'orders.items.duration.name': { $ne: 'Empty' },
      },
      // sort: [
      //   {
      //     field: 'order',
      //     order: 'ASC',
      //   },
      //   {
      //     field: 'orderTimestamp',
      //     order: 'DESC',
      //   },
      // ],
    },
  },
  {},
)
export class AppointmentController extends BaseCrudController<Appointment> {
  constructor(public service: AppointmentService) {
    super(service);
  }

  get base(): CrudController<Appointment> {
    return this;
  }

  @Override('createOneBase')
  async createOne(
    @ParsedBody() dto: any,
    @ParsedRequest() crudRequest: CrudRequest,
  ): Promise<Appointment> {
    return this.service.createOneAppointment(crudRequest, dto);
  }

  @Override('updateOneBase')
  async updateOne(
    @ParsedBody() dto: any,
    @ParsedRequest() crudRequest: CrudRequest,
    @Param('id') id: string,
  ): Promise<Appointment> {
    return this.service.updateOneAppointment(crudRequest, dto, id);
  }

  @Override('deleteOneBase')
  async deleteOne(
    @ParsedRequest() crudRequest: CrudRequest,
    @Param('id') id: string,
  ): Promise<void | Appointment> {
    //check not in check-in progress
    // const appointment = await this.service.getOne(crudRequest);
    // if (appointment.rfid) {
    //   throw new BadRequestException('Can not delete appointment has check-in!');
    // }
    return this.service.deleteOneAppointment(id);
  }

  @UseInterceptors(CrudRequestInterceptor)
  @Post('check-duplicate')
  checkDuplicate(
    @ParsedRequest() req: CrudRequest,
    @Body() dto: CheckDuplicateDto,
  ) {
    return this.service.checkDuplicate(dto);
  }

  @Get('/availability')
  @UseInterceptors(CrudRequestInterceptor)
  async availabilityEmployess(
    @ParsedRequest() crudReq: CrudRequest,
    @Req() req: Request,
    @Query() query: AvailabilityQueryDto,
  ): Promise<any> {
    try {
      return await this.service.availabilityEmployess(
        crudReq,
        query,
        req.headers?.['branchid']?.split(',') || [],
      );
    } catch (error) {
      throw new BadRequestException(
        error.message || 'Error processing availability request',
      );
    }
  }

  @UseInterceptors(CrudRequestInterceptor)
  @Get('/count-by-status')
  async countByStatus(@ParsedRequest() crudReq: CrudRequest) {
    return this.service.countByStatus(crudReq);
  }

  @Patch('/change-order/:id')
  async changeOrder(@Param('id') id: string, @Body() body: ChangeOrderDto) {
    return this.service.changeOrder(id, body);
  }
}
