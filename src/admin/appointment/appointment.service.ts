import * as moment from 'moment-timezone';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, In, LessThan, MoreThanOrEqual, Repository } from 'typeorm';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import { Appointment } from './appointment.entity';
import { CrudRequest } from 'src/core/crud/crud';
import { Order } from '../order/order.entity';
import { OrderDetail } from '../order-detail/order-detail.entity';
import { Employee } from '../employee/employee.entity';
import { Customer } from '../customer/customer.entity';
import { AppointmentStatus, ProductType } from '../../core/enums/entity';
import { ChangeOrderDto, CheckDuplicateDto } from './dto/appointment.dto';
import { Product } from '../product/product.entity';
import { AvailabilityEmployess } from './dto/availabilityEmployess.dto';
import { Category } from '../category/category.entity';
import { WorkingHourService } from '../working-hour/working-hour.service';
import { UUID } from 'crypto';
import { getTimeZoneNameFromOffset } from 'src/core/common/common.utils';
import { EmployeeService } from '../employee/employee.service';

interface ServiceEmployees {
  serviceId: string;
  serviceName: string;
  employees: Array<{
    id: string;
    name: string;
    branch: string;
  }>;
}

@Injectable()
export class AppointmentService extends BaseCrudService<Appointment> {
  constructor(
    @InjectRepository(Appointment) repo: Repository<Appointment>,
    @InjectRepository(OrderDetail)
    private orderDetailRepo: Repository<OrderDetail>,
    @InjectRepository(Employee)
    private employeeRepo: Repository<Employee>,
    @InjectRepository(Customer)
    private customerRepo: Repository<Customer>,
    @InjectRepository(Product)
    private productRepo: Repository<Product>,
    @InjectRepository(Order)
    private orderRepo: Repository<Order>,
    private readonly workingHourService: WorkingHourService,
    private readonly employeeService: EmployeeService,
  ) {
    super(repo);
  }

  async createOneAppointment(
    crudRequest: CrudRequest,
    dto: any,
  ): Promise<Appointment> {
    let appointment = null;
    for (const order of dto.orders) {
      await this.validateEmployeeCanWorkOnService(order);
    }
    let orderInsert = null;
    await this.repo.manager.transaction(async (trans) => {
      appointment = await trans.save(Appointment, {
        ...dto,
        orderTimestamp: new Date(),
      });

      for (const order of dto.orders) {
        const branchId = null;
        let totalBeforeTax = 0;
        for (const item of order.items) {
          const productInfo = await this.repo.manager.findOne(Product, {
            where: { id: item?.product?.id },
          });

          if (productInfo) {
            totalBeforeTax += productInfo?.price * (item?.quantity ?? 1);
          }
        }
        const subTotal = parseFloat(totalBeforeTax.toFixed(2));
        const total =
          order?.tax && +order?.tax >= 0
            ? totalBeforeTax + (totalBeforeTax * order?.tax) / 100
            : totalBeforeTax;

        orderInsert = await trans.save(Order, {
          ...order,
          totalBeforeTax: parseFloat(totalBeforeTax.toFixed(2)),
          subTotal,
          total: parseFloat(total.toFixed(2)),
          appointment,
          branch: dto.branch,
        });

        await Promise.all(
          order.items.map(async (item) => {
            if (item.employees.length === 0) {
              item.status = AppointmentStatus.BOOKING;
            } else {
              item.status = AppointmentStatus.REQUEST;
            }
            return trans.save(OrderDetail, {
              ...item,
              order: orderInsert,
            });
          }),
        );
      }
    });
    const orderRecord = await this.orderRepo.findOne({
      where: { id: orderInsert?.id },
      relations: ['items', 'items.product'],
    });
    await this.orderRepo.update(
      { id: orderInsert?.id },
      { payload: orderRecord },
    );

    return appointment;
  }

  async updateOneAppointment(
    crudRequest: CrudRequest,
    dto: any,
    id: string,
  ): Promise<Appointment> {
    let appointment = await this.repo.findOne({ where: { id } });

    for (const order of dto?.orders || []) {
      await this.validateEmployeeCanWorkOnService(order);
    }
    let orderInsert = null;
    const checkOut = appointment?.checkOut;
    await this.repo.manager.transaction(async (trans) => {
      appointment = await trans.save(Appointment, {
        ...dto,
        id,
      });
      if (dto?.orders && !checkOut) {
        await trans.softDelete(Order, {
          appointment,
        });
        for (const order of dto.orders) {
          let totalBeforeTax = 0;
          for (const item of order.items) {
            const productInfo = await this.repo.manager.findOne(Product, {
              where: { id: item?.product?.id },
            });

            if (productInfo) {
              totalBeforeTax += productInfo?.price * (item?.quantity ?? 1);
            }
          }
          const subTotal = parseFloat(totalBeforeTax.toFixed(2));
          const total = parseFloat(totalBeforeTax.toFixed(2));

          orderInsert = await trans.save(Order, {
            ...order,
            totalBeforeTax: parseFloat(totalBeforeTax.toFixed(2)),
            subTotal,
            total,
            appointment: { id },
          });

          await Promise.all(
            order.items.map(async (item) => {
              return trans.save(OrderDetail, {
                ...item,
                order: orderInsert,
              });
            }),
          );
        }
      }
    });
    if (orderInsert) {
      const orderRecord = await this.orderRepo.findOne({
        where: { id: orderInsert?.id },
        relations: ['items', 'items.product'],
      });
      await this.orderRepo.update(
        { id: orderInsert?.id },
        { payload: orderRecord },
      );
    }
    return appointment;
  }

  async validateEmployeeCanWorkOnService(order: Order) {
    for (const item of order.items) {
      const result = await Promise.all(
        item.employees.map((employee) => {
          return this.getRecordsWithOverlappingHours(
            employee.id,
            item.startTime,
            item.endTime,
            item.id,
          );
        }),
      );
      delete order.id;
      for (const item of result) {
        if (item.totalTask >= 3) {
          const employee = await this.employeeRepo.findOne({
            where: { id: item.employeeId },
          });
          throw new BadRequestException(
            `This time frame of ${
              employee.displayName || employee.fullName || item.employeeId
            } is full. Please choose another time frame!`,
          );
        }
      }
    }
  }

  async getRecordsWithOverlappingHours(
    employeeId: string,
    start: Date,
    end: Date,
    orderId?: string,
    orderDetailId?: string,
  ) {
    const query = this.orderDetailRepo
      .createQueryBuilder('detail')
      .innerJoinAndSelect('detail.employees', 'employee')
      .where(
        new Brackets((qb) => {
          qb.where('employee.id = :employeeId', { employeeId })
            .andWhere('detail."startTime" >= :start', {
              start,
            })
            .andWhere('detail."startTime" < :end', {
              end,
            });
          if (orderId) {
            qb.andWhere(`detail."orderId" != :orderId`, {
              orderId,
            });
          }
          // if (orderDetailId) {
          //   qb.andWhere(`detail."id" != :orderDetailId`, {
          //     orderDetailId,
          //   });
          // }
        }),
      )
      .orWhere(
        new Brackets((qb) => {
          qb.where('employee.id = :employeeId', { employeeId })
            .andWhere('detail."endTime" > :start', {
              start,
            })
            .andWhere('detail."endTime" <= :end', {
              end,
            });
          if (orderId) {
            qb.andWhere('detail."orderId" != :orderId', {
              orderId,
            });
          }
          // if (orderDetailId) {
          //   qb.andWhere(`detail."id" != :orderDetailId`, {
          //     orderDetailId,
          //   });
          // }
        }),
      )
      .orWhere(
        new Brackets((qb) => {
          qb.where('employee.id = :employeeId', { employeeId })
            .andWhere('detail."startTime" <= :start', {
              start,
            })
            .andWhere('detail."endTime" >= :end', {
              end,
            });
          if (orderId) {
            qb.andWhere(`detail."orderId" != :orderId`, {
              orderId,
            });
          }
          // if (orderDetailId) {
          //   qb.andWhere(`detail."id" != :orderDetailId`, {
          //     orderDetailId,
          //   });
          // }
        }),
      );

    // .where('employee.id = :employeeId', { employeeId })
    // .andWhere('orderDetail.startTime IS NOT NULL')
    // .andWhere('orderDetail.endTime IS NOT NULL')
    // .andWhere('orderDetail.startTime >= :startWork', {
    //   startWork,
    // })
    // .andWhere('orderDetail.endTime <= :endWork', { endWork })
    // .andWhere(
    //   '(orderDetail.startTime < :endWork OR orderDetail.endTime > :startWork)',
    //   {
    //     endWork,
    //     startWork,
    //   },
    // );
    const overlappingRecords = await query.getCount();
    return {
      employeeId,
      totalTask: overlappingRecords,
    };
  }

  async deleteOneAppointment(appointmentId: string) {
    const appointment = await this.repo.findOne({
      where: { id: appointmentId },
    });

    if (appointment) {
      await this.repo.manager.transaction(async (trans) => {
        //delete all order, order-detail of appointment

        const orders = await trans.find(Order, {
          where: { appointment: { id: appointmentId } },
        });
        await trans.softDelete(Order, { appointment: { id: appointmentId } });
        await trans.softDelete(OrderDetail, {
          order: { id: In(orders.map((o) => o.id)) },
        });

        //delete appointment

        await trans.softDelete(Appointment, { id: appointmentId });
      });
    } else {
      await this.orderDetailRepo.softDelete(appointmentId);
    }
  }

  async checkDuplicate({
    startTime,
    endTime,
    customerId,
    appointmentId,
  }: CheckDuplicateDto) {
    const customerExist = await this.customerRepo.manager.findOne(Customer, {
      where: { id: customerId },
    });

    if (!customerExist) {
      throw new NotFoundException('Customer not found');
    }

    let appointment = null;
    let isDuplicate = false;
    if (appointmentId) {
      const appointmentExist = await this.repo.manager.findOne(Appointment, {
        where: {
          id: appointmentId,
        },
      });

      if (!appointmentExist) {
        throw new NotFoundException('Appointment not found');
      }
    }
    const utcStartTime = moment().utcOffset('+00').startOf('day');
    const utcEndTime = moment().utcOffset('+00').endOf('day').milliseconds(999);

    const queryAppointment = await this.repo
      .createQueryBuilder('appointment')
      .leftJoinAndSelect('appointment.customer', 'customer')
      .where('customer.id = :customerId', { customerId })
      .andWhere('appointment.startTime BETWEEN :startTime AND :endTime', {
        startTime: utcStartTime,
        endTime: utcEndTime,
      });

    if (appointmentId) {
      queryAppointment.andWhere('appointment.id NOT IN (:...excludedIds)', {
        excludedIds: [appointmentId],
      });
    }

    queryAppointment.orderBy('appointment.startTime', 'ASC');
    appointment = await queryAppointment.getOne();

    if (appointment) {
      isDuplicate = true;
    }

    return {
      isDuplicate,
    };
  }

  async availabilityEmployess(
    crudReq: CrudRequest,
    query: any,
    branchIds?: UUID[],
  ) {
    try {
      const data: AvailabilityEmployess = {
        startTime: moment().startOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS'),
        endTime: moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS'),
        serviceIds: [],
      };

      let clientZone = moment().format('Z z');

      if (query?.filter) {
        for (const item of query.filter) {
          try {
            const [field, operator, value] = item.split('||');
            if (field === 'startTime') {
              data.startTime = moment(value).format('YYYY-MM-DDTHH:mm:ss.SSS');
              clientZone = value.slice(-6);
            }
            if (field === 'endTime') {
              data.endTime = moment(value).format('YYYY-MM-DDTHH:mm:ss.SSS');
              clientZone = value.slice(-6);
            }
            if (field === 'serviceIds') {
              // Handle the comma-separated UUIDs string directly
              if (typeof value === 'string') {
                data.serviceIds = value
                  .split(',')
                  .map((id) => id.trim())
                  .filter((id) => id !== '');
              } else if (Array.isArray(value)) {
                data.serviceIds = value;
              }
            }
          } catch (error) {
            console.error('Error parsing filter item:', item, error);
          }
        }
      }

      // Ensure serviceIds is always an array
      if (!Array.isArray(data.serviceIds)) {
        data.serviceIds = [];
      }

      const clientZoneName = getTimeZoneNameFromOffset(clientZone);
      const utcStartTime = moment(data.startTime)
        .utcOffset('+00')
        .format('YYYY-MM-DDTHH:mm:ss.SSS');
      const utcEndTime = moment(data.endTime)
        .utcOffset('+00')
        .format('YYYY-MM-DDTHH:mm:ss.SSS');

      // Validate that the dates are valid
      if (!moment(utcStartTime).isValid() || !moment(utcEndTime).isValid()) {
        throw new BadRequestException('Invalid date format in request');
      }

      // Get appointment time in HH:mm format for employee filtering
      const appointmentTime = moment(data.startTime).format('HH:mm');
      const duration = moment(data.endTime).diff(
        moment(data.startTime),
        'minutes',
      );

      if (duration < 0) {
        throw new BadRequestException('End time must be after start time');
      }

      // Get available employees for each service
      const availableEmployees = [];
      for (const serviceId of data.serviceIds) {
        try {
          const response =
            await this.employeeService.getEmployeesByWorkingHours({
              branchId: branchIds?.[0], // Using first branch ID if multiple provided
              serviceId,
              startDate: utcStartTime,
              endDate: utcEndTime,
              appointmentTime,
              duration,
            });

          const employeesByService = response as ServiceEmployees[];
          employeesByService.forEach((service) => {
            service.employees.forEach((emp) => {
              if (!availableEmployees.find((e) => e.id === emp.id)) {
                availableEmployees.push(emp);
              }
            });
          });
        } catch (error) {
          console.error(
            `Error getting employees for service ${serviceId}:`,
            error,
          );
        }
      }

      // Get room availability
      const categoriesList = await this.getCategoriesByServiceId(
        data.serviceIds,
      );

      // Check existing appointments for room availability
      const existingAppointments = await this.orderDetailRepo
        .createQueryBuilder('order_detail')
        .leftJoinAndSelect('order_detail.product', 'product')
        .leftJoinAndSelect('order_detail.employees', 'employees')
        .leftJoinAndSelect('product.category', 'category')
        .where(
          new Brackets((qb) => {
            qb.where(
              new Brackets((qb) => {
                qb.where('order_detail.startTime >= :startTime', {
                  startTime: utcStartTime,
                }).andWhere('order_detail.startTime < :endTime', {
                  endTime: utcEndTime,
                });
              }),
            )
              .orWhere(
                new Brackets((qb) => {
                  qb.where('order_detail.endTime > :startTime', {
                    startTime: utcStartTime,
                  }).andWhere('order_detail.endTime <= :endTime', {
                    endTime: utcEndTime,
                  });
                }),
              )
              .orWhere(
                new Brackets((qb) => {
                  qb.where('order_detail.startTime <= :startTime', {
                    startTime: utcStartTime,
                  }).andWhere('order_detail.endTime >= :endTime', {
                    endTime: utcEndTime,
                  });
                }),
              );
          }),
        )
        .getMany();

      // Update room availability based on existing appointments
      categoriesList.forEach((category) => {
        existingAppointments.forEach((appointment) => {
          if (category.id === appointment?.product?.category?.id) {
            category.roomQuantity -= category.roomQuantity > 0 ? 1 : 0;
          }
        });
      });

      return {
        availableRooms: categoriesList,
        availableEmployees,
      };
    } catch (error) {
      console.error('Error in availabilityEmployess:', error);
      throw error;
    }
  }

  async getCategoriesByServiceId(serviceIds: string[]) {
    const productOfService = await this.productRepo.find({
      where:
        serviceIds.length > 0
          ? { id: In(serviceIds) }
          : { type: ProductType.SERVICE },
      relations: ['category'],
    });

    const categories = {};
    for (const service of productOfService) {
      if (service.category) {
        categories[service.category.id] = service.category;
      }
    }

    return Object.values(categories) as Category[];
  }

  async countByStatus(crudReq: CrudRequest) {
    const masterQuery = this.repo
      .createQueryBuilder('Appointment')
      .select([
        '"Appointment".status AS status',
        'COUNT(*) AS total',
        '"Appointment".id as id',
      ])
      .leftJoin('Appointment.branch', 'branch')
      .leftJoin('Appointment.customer', 'customer')
      .leftJoin('Appointment.orders', 'orders')
      .leftJoin('orders.items', 'items')
      .leftJoin('items.duration', 'duration')
      .groupBy('"Appointment".id')
      .addGroupBy('"Appointment".status');

    this.setSearchCondition(masterQuery, crudReq.parsed.search);
    const data = await masterQuery.getRawMany();
    const groupedByStatus = data.reduce((acc, item) => {
      const { status, total } = item;
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});
    return groupedByStatus;
  }

  async changeOrder(id: string, body: ChangeOrderDto) {
    const { newPosition, beforeId } = body;

    const appointment = await this.repo.manager.findOne(Appointment, {
      where: {
        id,
      },
    });

    if (!appointment) {
      throw new NotFoundException('Appointment not found');
    }

    const appointmentBefore = await this.repo.manager.findOne(Appointment, {
      where: {
        id: beforeId,
      },
      relations: ['branch'],
    });

    if (appointmentBefore) {
      await this.repo.update(
        {
          order: appointmentBefore.order,
          branch: {
            id: appointmentBefore.branch.id,
          },
          orderTimestamp: LessThan(appointmentBefore.orderTimestamp),
        },
        {
          order: newPosition,
        },
      );
      await this.repo.update(
        {
          order: appointmentBefore.order,
          branch: {
            id: appointmentBefore.branch.id,
          },
          orderTimestamp: MoreThanOrEqual(appointmentBefore.orderTimestamp),
        },
        {
          order: newPosition > 1 ? newPosition - 1 : newPosition,
        },
      );
    }

    return await this.repo.manager.save(Appointment, {
      id,
      order: newPosition,
      orderTimestamp: new Date(),
    });
  }

  // Test method to validate parsing logic with sample parameters
  async testParameterParsing(sampleQuery: string) {
    // Parse the URL-encoded query string
    const params = new URLSearchParams(sampleQuery);
    const query = { filter: [] };

    // Extract filter parameters
    for (const [key, value] of params.entries()) {
      if (key.startsWith('filter')) {
        query.filter.push(value);
      }
    }

    // Call the availability method with the parsed query
    const data: AvailabilityEmployess = {
      startTime: moment().startOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS'),
      endTime: moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS'),
      serviceIds: [],
    };

    if (query?.filter) {
      for (const item of query.filter) {
        try {
          const [field, operator, value] = item.split('||');

          if (field === 'serviceIds') {
            // Handle the comma-separated UUIDs string directly
            if (typeof value === 'string') {
              data.serviceIds = value
                .split(',')
                .map((id) => id.trim())
                .filter((id) => id !== '');
            }
          }
        } catch (error) {
          console.error('Error parsing test filter item:', item, error);
        }
      }
    }

    return {
      originalQuery: sampleQuery,
      parsedQuery: query,
      parsedData: data,
    };
  }
}
