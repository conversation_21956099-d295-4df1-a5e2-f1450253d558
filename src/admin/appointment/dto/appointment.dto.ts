import {
  IsDateS<PERSON>,
  IsNotEmpty,
  IsNumber,
  IsO<PERSON>al,
  IsString,
  IsUUI<PERSON>,
} from 'class-validator';

export class CheckDuplicateDto {
  @IsNotEmpty()
  @IsDateString()
  startTime: Date;

  @IsNotEmpty()
  @IsDateString()
  endTime: Date;

  @IsNotEmpty()
  @IsString()
  customerId: string;

  @IsOptional()
  @IsString()
  appointmentId?: string;
}

export class ChangeOrderDto {
  @IsNotEmpty()
  @IsNumber()
  newPosition: number;

  @IsOptional()
  @IsUUID()
  beforeId?: string;
}
