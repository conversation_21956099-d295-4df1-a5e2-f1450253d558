import {
  ArrayNotEmpty,
  IsArray,
  IsDateString,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateIf,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class AvailabilityEmployess {
  @IsNotEmpty()
  @IsDateString()
  startTime: string;

  @IsNotEmpty()
  @IsDateString()
  endTime: string;

  @IsNotEmpty()
  @IsArray()
  @ArrayNotEmpty()
  serviceIds: string[];
}

export class AvailabilityQueryDto {
  @IsOptional()
  @IsArray()
  @Transform(({ value }) => {
    // Ensure filter is always processed as an array
    if (typeof value === 'string') {
      return [value];
    }
    return value;
  })
  filter?: string[];
}
