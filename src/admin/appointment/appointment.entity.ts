import { Entity, Column, ManyToOne, OneToMany } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Customer } from '../customer/customer.entity';
import { Order } from '../order/order.entity';
import { Invoice } from '../invoice/invoice.entity';
import { Branch } from '../branch/branch.entity';
import { AppointmentStatus, AppointmentType } from 'src/core/enums/entity';
import { Rfid } from '../rfid/rfid.entity';

@Entity()
export class Appointment extends DocEntity {
  @Column({
    nullable: false,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  startTime: Date;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  endTime: Date;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  checkIn?: Date;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  checkOut?: Date;

  @Column({
    default: AppointmentStatus.BOOKING,
    type: 'enum',
    enum: Object.values(AppointmentStatus),
  })
  @ApiProperty()
  status: AppointmentStatus;

  @ManyToOne(() => Customer, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Customer })
  customer: Customer;

  @ApiProperty({ type: () => Order, isArray: true })
  @OneToMany(() => Order, (order) => order.appointment, {
    onDelete: 'SET NULL',
  })
  orders: Order[];

  @ApiProperty({ type: () => Invoice, isArray: true })
  @OneToMany(() => Invoice, (invoice) => invoice.appointment)
  invoices: Invoice[];

  @Column({ nullable: true })
  @ApiPropertyOptional({ type: () => String })
  note?: string;

  @Column({ nullable: true })
  @ApiPropertyOptional({ type: () => String })
  rfid?: string;

  @ManyToOne(() => Rfid, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Rfid })
  rfids: Rfid;

  @ManyToOne(() => Branch, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Branch })
  branch: Branch;

  @Column({ type: 'bigint', nullable: true })
  order: number;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  orderTimestamp?: Date;

  @Column({
    default: AppointmentType.APPOINTMENT,
    type: 'enum',
    enum: Object.values(AppointmentType),
  })
  @ApiProperty()
  type: AppointmentType;
}
