import { Entity, Column, ManyToOne } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Employee } from '../employee/employee.entity';
@Entity('day_off')
export class DayOff extends DocEntity {
  @Column({
    nullable: false,
    type: 'timestamptz',
  })
  @ApiProperty()
  startDate: Date;

  @Column({
    nullable: false,
    type: 'timestamptz',
  })
  @ApiProperty()
  endDate: Date;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  description?: string;

  @ManyToOne(() => Employee, (employee) => employee.dayoffs, {
    onDelete: 'CASCADE',
  })
  @ApiProperty({ type: () => Employee })
  employee: Employee;
}
