import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, Repository } from 'typeorm';
import * as moment from 'moment';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { DayOff } from './day-off.entity';
import { CrudRequest } from 'src/core/crud/crud';
import { DayOffDto } from './dto/day-off.dto';

@Injectable()
export class DayOffService extends BaseCrudService<DayOff> {
  constructor(@InjectRepository(DayOff) repo: Repository<DayOff>) {
    super(repo);
  }

  async getList(
    crudReq: CrudRequest,
    keySearch?: string,
    branchIds?: string[],
  ) {
    const data: any = {
      startTime: moment().startOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS'),
      endTime: moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS'),
    };

    let startDateFlag = false;
    let endDateFlag = false;
    if (crudReq.parsed?.filter) {
      for (const f of crudReq.parsed.filter) {
        if (f.field === 'startDate') {
          startDateFlag = true;
          data.startTime = moment(f.value)
            .startOf('day')
            .format('YYYY-MM-DDTHH:mm:ss.SSS');
        }
        if (f.field === 'endDate') {
          endDateFlag = true;
          data.endTime = moment(f.value)
            .endOf('day')
            .format('YYYY-MM-DDTHH:mm:ss.SSS');
        }
      }
    }

    let dayOffList = [];

    const utcStartTime = data.startTime;
    const utcEndTime = data.endTime;

    const queryDayOff = this.repo
      .createQueryBuilder('day_off')
      .leftJoinAndSelect('day_off.employee', 'employee')
      .leftJoinAndSelect('employee.branch', 'branch');
    if (!startDateFlag && endDateFlag) {
      queryDayOff.where('day_off."endDate" <= :endTime', {
        endTime: utcEndTime,
      });
    } else if (startDateFlag && !endDateFlag) {
      queryDayOff.where('day_off."startDate" >= :startTime', {
        startTime: utcStartTime,
      });
    } else {
      queryDayOff.where(
        new Brackets((subQuery) =>
          subQuery
            .where('(day_off.startDate BETWEEN :startTime AND :endTime)', {
              startTime: utcStartTime,
              endTime: utcEndTime,
            })
            .orWhere('(day_off.endDate BETWEEN :startTime AND :endTime)', {
              startTime: utcStartTime,
              endTime: utcEndTime,
            }),
        ),
      );
    }
    if (branchIds.length > 0) {
      queryDayOff.andWhere(
        new Brackets((subQuery) =>
          subQuery.where('branch.id IN (:...branchIds)', {
            branchIds,
          }),
        ),
      );
    }

    if (keySearch) {
      queryDayOff.andWhere(
        new Brackets((subQuery) =>
          subQuery.where('(employee.displayName ILIKE :keySearch)', {
            keySearch: `%${keySearch}%`,
          }),
        ),
      );
    }

    queryDayOff.orderBy('day_off."startDate"', 'DESC');
    queryDayOff.addOrderBy('day_off."created"', 'DESC');
    dayOffList = await queryDayOff.getMany();
    return dayOffList;
  }

  async createOneDayOff(crudRequest: CrudRequest, dto: DayOffDto) {
    const querDayOff = await this.repo
      .createQueryBuilder('day_off')
      .leftJoinAndSelect('day_off.employee', 'employee')
      .where(
        '((DATE(day_off.startDate) = DATE(:startDate) OR DATE(day_off.startDate) = DATE(:endDate)) OR (DATE(day_off.endDate) = DATE(:startDate)) OR (DATE(day_off.endDate) = DATE(:endDate)) OR (:startDate BETWEEN day_off.startDate AND day_off.endDate) OR (:endDate BETWEEN day_off.startDate AND day_off.endDate))',
        {
          startDate: dto.startDate,
          endDate: dto.endDate,
        },
      )
      .andWhere('employee.id = :employeeId', { employeeId: dto.employee.id })
      .getOne();

    if (querDayOff) {
      throw new BadRequestException('The day-off is duplicated.');
    }

    const createdDayOff = await super.createOne(crudRequest, dto);
    return createdDayOff;
  }

  async updateOneDayOff(crudRequest: CrudRequest, dto: DayOffDto) {
    const querDayOff = await this.repo
      .createQueryBuilder('day_off')
      .leftJoinAndSelect('day_off.employee', 'employee')
      .where(
        '((DATE(day_off.startDate) = DATE(:startDate) OR DATE(day_off.startDate) = DATE(:endDate)) OR (DATE(day_off.endDate) = DATE(:startDate)) OR (DATE(day_off.endDate) = DATE(:endDate)) OR (:startDate BETWEEN day_off.startDate AND day_off.endDate) OR (:endDate BETWEEN day_off.startDate AND day_off.endDate))',
        {
          startDate: dto.startDate,
          endDate: dto.endDate,
        },
      )
      .andWhere('employee.id = :employeeId', { employeeId: dto.employee.id })
      .getOne();

    if (
      querDayOff &&
      crudRequest?.parsed?.paramsFilter[0]?.value !== querDayOff?.id
    ) {
      throw new BadRequestException('The day-off is duplicated.');
    }
    return super.updateOne(crudRequest, dto);
  }
}
