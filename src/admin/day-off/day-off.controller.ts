import {
  Crud<PERSON>ontroller,
  CrudRequest,
  Override,
  ParsedBody,
  ParsedRequest,
} from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { DayOff } from './day-off.entity';
import { DayOffService } from './day-off.service';
import { DayOffDto } from './dto/day-off.dto';
import { Query, Req } from '@nestjs/common';

@BaseCrud(
  {
    model: {
      type: DayOff,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    dto: {
      create: DayOffDto,
      update: DayOffDto,
    },
    query: {
      join: {
        employee: { eager: true, allow: ['id', 'fullName', 'displayName'] },
        'employee.branch': { eager: true, allow: ['id', 'name'] },
      },
    },
  },
  {
    // grantPerm: userPerm,
    // group: ResourceGroup.SYSTEM,
  },
)
export class DayOffController extends BaseCrudController<DayOff> {
  constructor(public service: DayOffService) {
    super(service);
  }
  get base(): CrudController<DayOff> {
    return this;
  }

  @Override('getManyBase')
  async getMany(
    @Req() req: Request,
    @ParsedRequest() crudReq: CrudRequest,
    @Query('keySearch') keySearch: string,
  ) {
    const branchIds = req.headers?.['branchid']?.split(',') || [];
    return this.service.getList(crudReq, keySearch, branchIds);
  }

  @Override('createOneBase')
  createOne(
    @ParsedBody() dto: DayOffDto,
    @ParsedRequest() crudRequest: CrudRequest,
  ) {
    return this.service.createOneDayOff(crudRequest, dto);
  }

  @Override('updateOneBase')
  updateOne(
    @ParsedBody() dto: DayOffDto,
    @ParsedRequest() crudRequest: CrudRequest,
  ) {
    return this.service.updateOneDayOff(crudRequest, dto);
  }
}
