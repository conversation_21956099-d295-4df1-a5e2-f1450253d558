import { ApiProperty, PickType } from '@nestjs/swagger';
import { DayOff } from '../day-off.entity';
import { IsDateString, IsNotEmpty } from 'class-validator';

export class DayOffDto extends PickType(DayOff, ['id']) {
  @ApiProperty()
  @IsNotEmpty()
  @IsDateString()
  startDate: Date;

  @ApiProperty()
  @IsNotEmpty()
  @IsDateString()
  endDate: Date;

  @ApiProperty()
  @IsNotEmpty()
  employee: any;
}
