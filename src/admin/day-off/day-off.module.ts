import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { DayOffController } from './day-off.controller';
import { DayOff } from './day-off.entity';
import { DayOffService } from './day-off.service';

@Module({
  imports: [TypeOrmModule.forFeature([DayOff])],
  controllers: [DayOffController],
  providers: [DayOffService],
  exports: [DayOffService],
})
export class DayOffModule {}
