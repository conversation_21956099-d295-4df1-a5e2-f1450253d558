import {
  BadRequestException,
  Injectable,
  forwardRef,
  Inject,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Auth, AuthType } from './auth.entity';
import { Repository, getManager } from 'typeorm';
import { SigninDTO } from './dto/signin.dto';
import { JwtService } from '@nestjs/jwt';
import { jwtConstants } from './constants';
import * as _ from 'lodash';
import * as bcrypt from 'bcryptjs';
import { ResetPasswordDto } from './dto/reset-password.dto';

import { MailerService } from '../mailer/mailer.service';
import * as handlebars from 'handlebars';
import { resetPasswordTemplate } from '../mailer/templates';
import { User } from '../user/user.entity';
import { TokenService } from '../token/token.service';
import { Role } from '../role/role.entity';
import { UserSession } from '../user-session/user-session.entity';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { Token, TypeToken } from '../token/token.entity';
import { UserService } from '../user/user.service';
import { genUUID, md5 } from 'src/core/crypto/crypto.provider';
import { UnauthorizedException } from 'src/core/exception/core/unauthorized.exception';
import { CommonHttpStatus } from 'src/core/common/common-http.status';
import { checkIfValidUUID } from 'src/core/common/common.utils';
import { InvalidCredentialsException } from 'src/core/exception/core/invalidCredentials.exception';
import { waMessage } from 'src/core/exception/exception.messages.contants';
import { RecordStatus } from 'src/core/enums/entity';
import { EmailResetPasswordDto } from './dto/email-reset-password.dto';
import { VerifyTokenPassword } from './dto/verify-token-password';
import { ResourceService } from '../resource/resource.service';
import { ResourceType } from '../resource/resource.entity';

export class AuthSignedTokenPayload {
  id: string;
  role: Role;

  constructor(id: string, role: Role) {
    this.id = id;
    this.role = role;
  }

  getPayload() {
    return {
      id: this.id,
      ...(this.role ? { role: this.role } : {}),
    };
  }
}

@Injectable()
export class AuthService extends BaseCrudService<Auth> {
  constructor(
    @InjectRepository(Auth) repo: Repository<Auth>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private readonly jwtService: JwtService,
    private readonly tokenService: TokenService,
    @Inject(forwardRef(() => UserService))
    private readonly userService: UserService,
    private readonly mailerService: MailerService,
    private readonly resourceService: ResourceService,
  ) {
    super(repo);
  }

  async signin(req, res, dto: SigninDTO) {
    try {
      // check user exist

      const user = await this.userRepository.findOne({
        where: { email: dto['username'] },
        relations: ['role'],
      });

      // throw new BadRequestException('error');
      if (!user) {
        return new InvalidCredentialsException().sendWithRes(res);
      }

      let tokens = await this.generateToken(req, res, user);

      // handle user login time logs
      const loginSession = await this.repo.manager.findOne(UserSession, {
        where: {
          logout: null,
          user: {
            id: user.id,
          },
        },
      });
      if (loginSession) {
        // update logout time of old session
        await this.repo.manager.save(UserSession, {
          id: loginSession.id,
          logout: new Date(),
        });
      }
      await this.repo.manager.save(UserSession, {
        user,
        role: user.role,
        login: new Date(),
        tokenString: tokens.tokenString,
      });
      // ---End---

      tokens = _.omit(tokens, ['tokenString']);
      res.send(tokens);
    } catch (error) {
      throw new BadRequestException(error?.response || error);
    }
  }

  async me(req) {
    const me = await this.userService.findOne({
      where: { id: req.user['id'] },
      relations: [
        'role',
        'role.permission',
        'role.permission.resource',
        'branch',
      ],
      select: {
        id: true,
        fullname: true,
        displayName: true,
        phone: true,
        username: true,
        email: true,
        avatar: {
          id: true,
          url: true,
        },
        role: {
          id: true,
          code: true,
          name: true,
        },
        branch: {
          id: true,
          name: true,
        },
      },
    });

    //get resource
    const resources = await this.resourceService.getManyResource(req, true);
    //map perm with default-resource
    let accessResource = [];
    if (me.role.name.toUpperCase() !== 'SUPERADMIN') {
      const myPerms = me.role.permission;
      const myPermittedResources = {};
      myPerms
        .filter(
          (p) =>
            p.resource.type === ResourceType.DEFAULT ||
            p.delete ||
            p.read ||
            p.update ||
            p.create ||
            p.list ||
            p.print ||
            p.download ||
            p.active ||
            p.changePass ||
            p.listCalendar ||
            p.addAppointment ||
            p.export ||
            p.sendMail ||
            p.checkOut ||
            p.changePaymentSingleDay ||
            p.changePaymentAllDays ||
            p.editSingleDay ||
            p.editAllDays ||
            p.refund,
        )
        .forEach((p) => {
          myPermittedResources[p.resource.id] = {
            delete: p.delete,
            read: p.read,
            update: p.update,
            create: p.create,
            list: p.list,
            print: p.print,
            download: p.download,
            active: p.active,
            changePass: p.changePass,
            listCalendar: p.listCalendar,
            addAppointment: p.addAppointment,
            export: p.export,
            sendMail: p.sendMail,
            checkOut: p.checkOut,
            changePaymentSingleDay: p.changePaymentSingleDay,
            changePaymentAllDays: p.changePaymentAllDays,
            editSingleDay: p.editSingleDay,
            editAllDays: p.editAllDays,
            refund: p.refund,
          };
        });

      resources.forEach((r) => {
        const child = r.children
          .filter((child) => {
            return Object.keys(myPermittedResources).includes(child.id);
          })
          .map((child) => {
            return {
              ...child,
              ...myPermittedResources[child.id],
            };
          });
        if (
          r.type === ResourceType.DEFAULT ||
          child.length > 0 ||
          myPermittedResources[r.id]?.delete ||
          myPermittedResources[r.id]?.read ||
          myPermittedResources[r.id]?.update ||
          myPermittedResources[r.id]?.create ||
          myPermittedResources[r.id]?.list ||
          myPermittedResources[r.id]?.print ||
          myPermittedResources[r.id]?.download ||
          myPermittedResources[r.id]?.active ||
          myPermittedResources[r.id]?.changePass ||
          myPermittedResources[r.id]?.listCalendar ||
          myPermittedResources[r.id]?.addAppointment ||
          myPermittedResources[r.id]?.export ||
          myPermittedResources[r.id]?.sendMail ||
          myPermittedResources[r.id]?.checkOut ||
          myPermittedResources[r.id]?.changePaymentSingleDay ||
          myPermittedResources[r.id]?.changePaymentAllDays ||
          myPermittedResources[r.id]?.editSingleDay ||
          myPermittedResources[r.id]?.editAllDays ||
          myPermittedResources[r.id]?.refund
        ) {
          accessResource.push({
            ...r,
            ...myPermittedResources[r.id],
            children: child,
          });
        }
      });
      // resource.filter();
    } else {
      accessResource = resources;
    }

    delete me.role.permission;
    const resData = {
      ...me,
      resource: accessResource,
    };

    return resData;
  }

  async validateUser(username: string, password: string): Promise<any> {
    const user = await this.amIActive(username, true);
    let validUser = false;

    if (user?.auths?.[0]) {
      const authorized = bcrypt.compareSync(password, user.auths[0].password);
      if (authorized) {
        validUser = true;
      }
    }

    if (validUser) {
      const { auths, ...result } = user;
      return result;
    }

    return null;
  }

  async checkStatusLogin(userId: string) {
    const token = await this.tokenService.findOne({
      where: { user: { id: userId } },
    });

    return token;
  }

  async grantTokenWithRefreshToken(req, res) {
    const refreshToken = req.user?.refreshToken;
    const tokens = await this.refreshToken(req, res, refreshToken);
    res.send(tokens);
  }

  private async refreshToken(req, res, refreshToken: string) {
    try {
      const verifiedToken = await this.validateToken(refreshToken);
      const user = await this.amIActive(verifiedToken.id, false);
      if (user) {
        return this.generateToken(req, res, user);
      }
    } catch (err) {
      res.status(err?.status || CommonHttpStatus.BAD_REQUEST);
    }
  }

  async signout(req, res) {
    const userId = req.user.id;
    const tokenSession = await this.repo.manager.findOne(Token, {
      where: {
        user: {
          id: userId,
        },
      },
    });

    if (tokenSession) {
      const logoutSession = await this.repo.manager.findOne(UserSession, {
        where: {
          tokenString: tokenSession.tokenString,
          user: {
            id: userId,
          },
        },
      });

      if (logoutSession) {
        // update logout time of session
        await this.repo.manager.save(UserSession, {
          id: logoutSession.id,
          logout: new Date(),
        });
      }
    }

    // remove token from db
    await this.tokenService.revokeToken(userId);

    res.status(CommonHttpStatus.OK).send(true);
  }

  private async validateToken(token: string) {
    if (!token) {
      throw new UnauthorizedException();
    }

    const verifiedToken = this.jwtService.verify(token);
    if (verifiedToken?.refreshToken) {
      // Check if token in database
      const tokenRecord: Token = await this.isTokenInDB(token);
      if (!tokenRecord?.id) {
        throw new UnauthorizedException();
      }
    }

    return verifiedToken;
  }

  private async isTokenInDB(token: string): Promise<Token> {
    const verifiedToken = this.jwtService.verify(token);
    const tokenRecord = await this.tokenService.findOne({
      where: [
        { tokenString: md5(token) },
        {
          type: verifiedToken['refreshToken']
            ? 'REFRESH_TOKEN'
            : 'ACCESS_TOKEN',
        },
      ],
    });

    if (!tokenRecord) {
      throw new BadRequestException();
    }

    return tokenRecord;
  }

  private async amIActive(
    userNameOrId: string,
    withAuth: boolean,
  ): Promise<any> {
    const user = checkIfValidUUID(userNameOrId)
      ? await this.userService.findById(userNameOrId, [
          ...(withAuth ? ['auths'] : []),
          'role',
        ])
      : await this.userService.findByEmail(userNameOrId, [
          ...(withAuth ? ['auths'] : []),
          'role',
        ]);

    return user;
  }

  // private checkIfValidUUID(str) {
  //   // Regular expression to check if string is a valid UUID
  //   const regexExp =
  //     /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/gi;

  //   return regexExp.test(str);
  // }

  private async generateToken(req, res, user: User) {
    const accessToken = this.jwtService.sign(
      new AuthSignedTokenPayload(user.id, user?.role).getPayload(),
      {
        expiresIn: jwtConstants.expire.access,
      },
    );

    const refreshToken = this.jwtService.sign(
      {
        refreshToken: true,
        id: user.id,
        role: user?.role,
      },
      { expiresIn: jwtConstants.expire.refresh * 1000 },
    );
    const tokenString = md5(refreshToken);
    await this.tokenService.storeToken(
      req,
      tokenString,
      user.id,
      TypeToken.REFRESH_TOKEN,
      new Date(Date.now() + jwtConstants.expire.refresh * 1000),
    );

    return {
      accessToken,
      refreshToken,
      tokenString,
    };
  }

  async setPassword(userId: string, password: string): Promise<boolean> {
    // check if user got Auth
    const userAuth = await this.repo.findOne({
      where: {
        user: {
          id: userId,
        },
        type: AuthType.SYSTEM,
      },
    });

    const salt = bcrypt.genSaltSync(10);
    const hashedPassword = bcrypt.hashSync(password, salt);
    if (userAuth) {
      // update password
      const { affected } = await this.repo.update(
        {
          id: userAuth.id,
        },
        { password: hashedPassword },
      );

      return affected > 0;
    } else {
      const result = await this.repo.insert({
        password: hashedPassword,
        type: AuthType.SYSTEM,
        user: {
          id: userId,
        },
      });

      return true;
    }
  }

  async delete(userId: string) {
    return this.repo.delete({ user: { id: userId } });
  }

  async forceResetPassword(
    userId: string,
    dto: ResetPasswordDto,
    req: Request,
  ) {
    // valid userId is uuid
    if (!checkIfValidUUID(userId)) {
      throw new BadRequestException({
        message: `validation failed. userId must be uuid`,
      });
    }
    // check not me
    if (userId === req['user']['id']) {
      throw new BadRequestException(
        waMessage.exception.cannotUpdateYourself.message,
      );
    }

    // Check user exists
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['role'],
    });
    if (!user) {
      throw new BadRequestException(waMessage.exception.userNotFound.message);
    }

    // Check role level
    // if (req['user']['role']['level'] <= user.role.level) {
    //   throw new BadRequestException(waMessage.exception.invalidRole.message);
    // }

    // Check if token exist in db
    const tokenRecord = await this.repo.findOne({
      relations: ['user', 'user.auths'],
      where: {
        user: { id: userId },
      },
    });

    // remove token from db
    await this.tokenService.revokeToken(userId);

    // let's update password
    const salt = bcrypt.genSaltSync(10);
    const hashedPassword = bcrypt.hashSync(dto.password, salt);
    const queryRunner = this.repo.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction('SERIALIZABLE');
    try {
      const sysAuth = tokenRecord?.user?.auths?.filter(
        (a: Auth) => a.type === AuthType.SYSTEM,
      )?.[0];

      if (sysAuth) {
        await queryRunner.manager.save(Auth, {
          id: sysAuth?.id,
          password: hashedPassword,
        });
      } else {
        await queryRunner.manager.save(Auth, {
          user: {
            id: userId,
          },
          type: AuthType.SYSTEM,
          password: hashedPassword,
        });
      }

      // REMOVE old token
      if (tokenRecord?.id) {
        await queryRunner.manager.softDelete(Token, tokenRecord.id);
      }
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
    } finally {
      await queryRunner.release();
    }
    return { success: true };
    // send mail when reset pass success
    // const contentTemplate = handlebars.compile(resetPasswordTemplate);

    // const html = contentTemplate({
    //   username: user.email,
    //   password: dto.password,
    //   publicKey: process.env.ESSENTIAL_URL,
    // });

    // await this.mailerService.sendMail({
    //   to: user.email,
    //   html,
    //   subject: 'Reset Password',
    //   text: 'Reset Password',
    // });
  }

  async forgotPassword(dto: EmailResetPasswordDto, res, req: Request) {
    const user = await this.userService.findByEmail(dto.email, ['tokens']);
    if (!user) {
      throw new BadRequestException(waMessage.exception.emailNotFound.message);
    }
    if (!user?.tokens[0]?.tokenString) {
      await this.generateToken(req, res, user);
    }

    const tokenRecord = await this.userService.getTokenByUserId(user.id);
    const contentTemplate = handlebars.compile(resetPasswordTemplate);
    const html = contentTemplate({
      link: process.env.FE_URL + '/auth/createNewPassword?token=' + tokenRecord,
    });

    this.mailerService.sendMail({
      to: dto.email,
      html,
      subject: 'Reset Password',
      text: 'Reset Password',
    });
    res.send({
      statusCode: 200,
      message: 'Email sent successfully',
    });
  }

  async forgotPasswordVerify(dto: VerifyTokenPassword, res) {
    const data = await this.tokenService.getUserIdByToken(dto.token);
    if (!data?.user?.id) {
      throw new BadRequestException(waMessage.exception.tokenNotFound.message);
    }
    const updatePassword = await this.setPassword(data.user.id, dto.password);
    if (updatePassword) {
      await this.tokenService.revokeToken(data.user.id);
    }
    res.send({
      statusCode: 200,
      message: 'Password reset successfully',
    });
  }
}
