import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { AuthService, AuthSignedTokenPayload } from '../auth.service';
import { UnauthorizedException } from 'src/core/exception/core/unauthorized.exception';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(private readonly authService: AuthService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: 'JWT_SECRET_KEY',
    });
  }

  async validate(payload: AuthSignedTokenPayload) {
    //check token exists
    const statusLogin = await this.authService.checkStatusLogin(payload.id);
    if (!statusLogin) {
      throw new UnauthorizedException();
    }
    return payload;
  }
}
