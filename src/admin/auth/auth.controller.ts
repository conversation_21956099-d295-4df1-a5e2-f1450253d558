import { Auth } from './auth.entity';
import { AuthService } from './auth.service';
import { BaseCrud } from './decorators/base-crud.decorator';
import {
  Body,
  Get,
  Param,
  Post,
  Put,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { SigninDTO } from './dto/signin.dto';
import { LocalAuthGuard } from './guards/local-auth.guard';

import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { JwtRefreshGuard } from './guards/jwt-refresh.guard';
// import { ResourceGroup } from '../resource/resource.group';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { UUID } from 'crypto';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { CrudController } from 'src/core/crud/crud';
import { EmailResetPasswordDto } from './dto/email-reset-password.dto';
import { VerifyTokenPassword } from './dto/verify-token-password';

@BaseCrud(
  {
    model: { type: Auth },
    routes: {
      exclude: [
        'getManyBase',
        'getOneBase',
        'createOneBase',
        'createManyBase',
        'updateOneBase',
        'deleteOneBase',
      ],
    },
  },
  {
    // group: ResourceGroup.SYSTEM,
    isPublic: true,
  },
)
export class AuthController extends BaseCrudController<Auth> {
  constructor(public service: AuthService) {
    super(service);
  }

  get base(): CrudController<Auth> {
    return this;
  }

  @UseGuards(LocalAuthGuard)
  @Post('signin')
  token(@Req() req, @Res() res, @Body() signinDTO: SigninDTO) {
    return this.service.signin(req, res, signinDTO);
  }

  @UseGuards(JwtRefreshGuard)
  @Get('token/refresh')
  refreshToken(@Req() req, @Res() res) {
    this.service.grantTokenWithRefreshToken(req, res);
  }

  @UseGuards(JwtAuthGuard)
  @Get('signout')
  signout(@Req() req, @Res() res) {
    return this.service.signout(req, res);
  }

  @UseGuards(JwtAuthGuard)
  @Get('me')
  me(@Req() req) {
    return this.service.me(req);
  }

  // Reset password for another user
  @UseGuards(JwtAuthGuard)
  @Post('/password/reset/:id')
  forceResetPassword(
    @Param('id') userId: string,
    @Body() resetPasswordDto: ResetPasswordDto,
    @Req() req,
  ) {
    return this.service.forceResetPassword(userId, resetPasswordDto, req);
  }

  // test mailer
  @Post('/password/forgot')
  forgotPasswordSendMail(
    @Body() emailResetPasswword: EmailResetPasswordDto,
    @Res() res,
    @Req() req,
  ) {
    return this.service.forgotPassword(emailResetPasswword, res, req);
  }

  @Post('/password/forgot/verify')
  forgotPasswordVerify(
    @Body() verifyTokenPassword: VerifyTokenPassword,
    @Res() res,
  ) {
    return this.service.forgotPasswordVerify(verifyTokenPassword, res);
  }
}
