import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { getAction, getFeature } from 'src/core/crud/crud';

import { AuthService } from '../auth.service';

export const CrudMethods = [
  'getManyBase',
  'getOneBase',
  'createOneBase',
  'createManyBase',
  'updateOneBase',
  'submitOneBase',
  'deleteOneBase',
];

@Injectable()
export class ACLCrudGuard implements CanActivate {
  constructor(private readonly authService: AuthService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest();
    const res = context.switchToHttp().getResponse();
    const handler = context.getHandler();
    const feature = getFeature(context.getClass()).toLowerCase();

    const resource = Reflect.getMetadata('resource', context.getClass());

    const resourceName = resource?.options?.customRepository || resource?.name;

    let user: any;
    try {
      // user = await this.authService.verify(req, res);
    } catch (error) {
      throw error;
    }

    if (!user) {
      throw new UnauthorizedException();
    }

    // if (user?.superman) {
    //   return true;
    // }

    if (!user.role) {
      this.cleanTokens(res);
      throw new UnauthorizedException();
    }

    if (
      !CrudMethods.includes(handler.name) &&
      !CrudMethods.includes(`${handler.name}Base`)
    ) {
      return true;
    }

    const action = getAction(handler);
    let actionName: any;
    if (
      ['submitOne', 'submitOneBase', 'approveOne', 'approveOneBase'].includes(
        handler.name,
      )
    ) {
      actionName = handler.name.match(/([a-z]+)($|One.*)/)[1];
    }

    if (['cancelOne', 'cancelOneBase'].includes(handler.name)) {
      actionName = 'approve';
    }

    if (!action && !actionName) {
      return true;
    }

    actionName = actionName || action.split('-')[0].toLowerCase();

    return true;
  }

  private cleanTokens(res) {
    res.clearCookie('accessToken');
    res.clearCookie('refreshToken');
  }
}
