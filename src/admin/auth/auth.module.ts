import { forwardRef, Global, Module, NestModule } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthController } from './auth.controller';
import { Auth } from './auth.entity';
import { AuthService } from './auth.service';
import { getJWTConfig } from './constants';
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { JwtRefreshStrategy } from './strategies/jwt-refresh.strategy';
// import { MailerService } from '../mailer/mailer.service';
import { User } from '../user/user.entity';
import { UserModule } from '../user/user.module';
import { TokenModule } from '../token/token.module';
import { TokenService } from '../token/token.service';
import { Token } from '../token/token.entity';
import { MailerModule } from '../mailer/mailer.module';
import { ResourceModule } from '../resource/resource.module';

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([Auth, Token, User]),
    JwtModule.register({
      secret: 'JWT_SECRET_KEY',
      signOptions: { expiresIn: '60m' },
    }),
    PassportModule,
    TokenModule,
    forwardRef(() => UserModule),
    MailerModule,
    ResourceModule,
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    TokenService,
    LocalStrategy,
    JwtStrategy,
    JwtRefreshStrategy,
  ],
  exports: [AuthService],
})
export class AuthModule implements NestModule {
  configure() {}
}
