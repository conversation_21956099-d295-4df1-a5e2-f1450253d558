import { JwtModuleOptions } from '@nestjs/jwt';
import { ConfigService } from 'src/core/config/config.service';

export const jwtConstants = {
  expire: {
    access: 24 * 60 * 60 * 30, // 24 hours
    refresh: 24 * 60 * 60, // 24 hours
    recover: 30 * 60, // 30 mins
    game: 24 * 60 * 60, // 24 hours
  },
};

export const getJWTConfig = (configService: ConfigService) => {
  return {
    // secret: Buffer.from(configService.get<string>('auth.privateKey'), 'base64').toString('ascii'),
    publicKey: Buffer.from(
      configService.get<string>('auth.publicKey'),
      'base64',
    ),
    privateKey: Buffer.from(
      configService.get<string>('auth.privateKey'),
      'base64',
    ),
    signOptions: {
      algorithm: 'RS256',
      expiresIn: jwtConstants.expire.access,
    },
    verifyOptions: {
      algorithms: ['RS256'],
    },
  } as JwtModuleOptions;
};
