import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, ManyToOne } from 'typeorm';
import { User } from '../user/user.entity';
import { DocEntity } from 'src/core/base/doc.entity';
export enum AuthType {
  SYSTEM = 'SYSTEM',
  VENDOR = 'VENDOR',
}

@Entity()
export class Auth extends DocEntity {
  @ManyToOne(() => User, (user) => user.auths, { nullable: false })
  @ApiProperty({
    type: () => User,
    example: null,
    properties: {
      alwaysOn: {
        default: 'user.id',
      },
    },
  })
  user: User;

  @Column({ nullable: true })
  @ApiProperty({
    required: false,
  })
  password: string;

  @Column({ nullable: false, default: AuthType.SYSTEM })
  @ApiProperty({
    required: false,
  })
  type: AuthType;

  @Column({ nullable: true })
  @ApiProperty({
    required: false,
  })
  confirmationToken: string;
}
