import {
  CrudController,
  CrudRequest,
  Override,
  ParsedBody,
  ParsedRequest,
} from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { Invoice } from './invoice.entity';
import { InvoiceService } from './invoice.service';
import { CreateInvoiceDto } from './dto/createInvoice.dto';
import { Body, Param, Patch, Put, Query, Req } from '@nestjs/common';
import { BackDateDto, InvoiceQueryDto } from './dto/invoice.dto';

@BaseCrud(
  {
    model: {
      type: Invoice,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    dto: {
      create: CreateInvoiceDto,
    },
    query: {
      filter: {
        $or: [
          { code: { $cont: '' } },
          { 'customer.firstName': { $contL: '' } },
          { 'customer.lastName': { $contL: '' } },
          { 'customer.phone': { $contL: '' } },
        ],
      },
      join: {
        invoicePayments: {
          eager: true,
          allow: ['id', 'paid', 'billCode', 'roundNumber'],
        },
        'invoicePayments.paymentMethod': {
          eager: true,
          allow: ['id', 'name', 'code', 'isOptionBillCode'],
        },
        invoiceCoupon: {
          eager: true,
          allow: [
            'id',
            'discountValue',
            'couponName',
            'couponCode',
            'couponType',
            'percent',
            'order',
          ],
        },
        orders: {
          eager: true,
          allow: [
            'id',
            'code',
            'note',
            'items',
            'total',
            'tax',
            'totalBeforeTax',
            'payload',
            'couponCode',
            'discountProductId',
            'discountMoney',
            'couponName',
            'subTotal',
            'orderType',
            'transferBy',
            'isEdited',
          ],
        },
        'orders.transferBy': {
          eager: true,
          alias: 'transferBy',
          allow: ['id', 'firstName', 'lastName'],
        },
        'orders.items': {
          eager: true,
          alias: 'items',
          allow: [
            'id',
            'note',
            'product',
            'quantity',
            'discount',
            'price',
            'startTime',
            'endTime',
            'duration',
            'employees',
            'couponCode',
          ],
        },
        'orders.items.employees': {
          eager: true,
          alias: 'employees',
          allow: ['id', 'fullName', 'displayName'],
        },
        'orders.items.product': {
          eager: true,
          alias: 'product',
          allow: [
            'id',
            'type',
            'credit',
            'category',
            'name',
            'price',
            'creditType',
            'periodUnit',
            'period',
          ],
        },
        'orders.items.product.category': {
          eager: true,
          alias: 'category',
          allow: ['id', 'name'],
        },
        customer: {
          eager: true,
          allow: ['id', 'firstName', 'lastName', 'phone', 'nric', 'credits'],
        },
        'customer.credits': {
          eager: true,
          alias: 'credits',
          allow: [
            'id',
            'creditBalance',
            'creditSetting',
            'issueDate',
            'expiryDate',
          ],
        },
        'customer.credits.creditSetting': {
          eager: true,
          alias: 'creditSetting',
          allow: ['id', 'name', 'creditType'],
        },
        branch: { eager: true, allow: ['id', 'name', 'address', 'phones'] },
        status: { eager: true, allow: ['id', 'name'] },
        appointment: { eager: true, allow: ['id'] },
        parentInvoice: { eager: true, allow: ['id', 'code', 'status'] },
        childInvoices: { eager: true, allow: ['id', 'code', 'status'] },
        referral: {
          eager: true,
          allow: ['id', 'fullname', 'displayName', 'username'],
        },
      },
      sort: [
        {
          field: 'date',
          order: 'DESC',
        },
      ],
    },
  },
  {
    // grantPerm: userPerm,
    // group: ResourceGroup.SYSTEM,
  },
)
export class InvoiceController extends BaseCrudController<Invoice> {
  constructor(public service: InvoiceService) {
    super(service);
  }

  get base(): CrudController<Invoice> {
    return this;
  }

  @Override('createOneBase')
  async createOne(
    @ParsedBody() dto: CreateInvoiceDto,
    @ParsedRequest() crudRequest: CrudRequest,
    @Req() req: Request,
  ): Promise<Invoice> {
    return await this.service.createOneInvoice(crudRequest, dto, req);
  }

  @Override('updateOneBase')
  async updateOne(@Body() dto: any, @Param('id') id: string) {
    return this.service.updateOneInvoice(dto, id);
  }

  @Override('getManyBase')
  async getList(
    @ParsedRequest() crudRequest: CrudRequest,
    @Query() query: InvoiceQueryDto,
  ): Promise<any> {
    return await this.service.getList(crudRequest, query);
  }

  @Override('getOneBase')
  async getOne(@ParsedRequest() crudRequest: CrudRequest): Promise<any> {
    return await this.service.getOneInvoice(crudRequest);
  }

  @Patch('/back-date/:id')
  async backDate(@Param('id') id: string, @Body() body: BackDateDto) {
    return this.service.backDate(id, body);
  }

  @Put('/note/:id')
  async updateNoteInvoice(
    @Param('id') id: string,
    @Body() body: { note: string },
  ): Promise<Invoice> {
    return await this.service.updateNoteInvoice(id, body);
  }
}
