import {
  <PERSON><PERSON>ty,
  Column,
  ManyToOne,
  ManyToMany,
  JoinTable,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Branch } from '../branch/branch.entity';
import { Customer } from '../customer/customer.entity';
import { InvoicePaymentMethod } from './invoice-payment-method.entity';
import { Appointment } from '../appointment/appointment.entity';
import { Order } from '../order/order.entity';
import { InvoiceStatus } from 'src/core/enums/entity';
import { User } from '../user/user.entity';
import { InvoiceCoupon } from './invoice-coupon.entity';
@Entity('invoice')
export class Invoice extends DocEntity {
  @Column({
    nullable: false,
  })
  @ApiProperty()
  code: string;

  @Column({
    nullable: false,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  date: Date;

  @Column({ type: 'real', nullable: true, default: 0 })
  @ApiPropertyOptional()
  total?: number;

  @Column({ type: 'real', nullable: true, default: 0 })
  @ApiPropertyOptional()
  paid?: number;

  @Column({ type: 'real', nullable: true, default: 0 })
  @ApiPropertyOptional()
  unPaid?: number;

  @Column({
    default: InvoiceStatus.UNPAID,
    type: 'enum',
    enum: Object.values(InvoiceStatus),
  })
  @ApiProperty()
  status: InvoiceStatus;

  @ManyToOne(() => Branch, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Branch })
  branch: Branch;

  @ManyToOne(() => Customer, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Customer })
  customer: Customer;

  @ManyToMany(() => InvoicePaymentMethod)
  @JoinTable()
  invoicePayments: InvoicePaymentMethod[];

  @ManyToMany(() => InvoiceCoupon)
  @JoinTable()
  invoiceCoupon: InvoiceCoupon[];

  @ManyToOne(() => Appointment, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Appointment })
  appointment: Appointment;

  @OneToMany(() => Order, (order) => order.invoice)
  @ApiProperty({ type: () => Order })
  orders: Order[];

  @OneToMany(() => Invoice, (invoice) => invoice.parentInvoice)
  @ApiProperty({ type: () => Invoice })
  childInvoices: Invoice[];

  @ManyToOne(() => Invoice, (invoice) => invoice.childInvoices)
  @JoinColumn()
  parentInvoice: Invoice;

  @Column({ type: 'real', nullable: true, default: 0 })
  @ApiPropertyOptional()
  discount?: number;

  @Column({ type: 'real', nullable: true, default: 0 })
  @ApiPropertyOptional()
  tax?: number;

  @Column({ type: 'real', nullable: true, default: 0 })
  @ApiPropertyOptional()
  totalBeforeTax?: number;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => User })
  referral: User;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional({ type: () => String })
  note?: string;

  @Column({ type: 'real', nullable: true, default: 0 })
  @ApiPropertyOptional()
  subTotal?: number;

  @Column({
    nullable: true,
    type: 'jsonb',
  })
  @ApiProperty()
  payload: object;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  voidDate: Date;
}
