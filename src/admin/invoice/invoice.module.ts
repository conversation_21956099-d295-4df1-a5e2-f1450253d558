import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { InvoiceController } from './invoice.controller';
import { Invoice } from './invoice.entity';
import { InvoiceService } from './invoice.service';
import { Credit } from '../credit/credit.entity';
import { CreditSetting } from '../settings/credit/credit-setting.entity';
import { Order } from '../order/order.entity';
import { AuditTrailModule } from '../audit-trail/audit-trail.module';
import { SalesModule } from '../sales/sales.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Invoice, Credit, CreditSetting, Order]),
    AuditTrailModule,
    SalesModule,
  ],
  controllers: [InvoiceController],
  providers: [InvoiceService],
  exports: [InvoiceService],
})
export class InvoiceModule {}
