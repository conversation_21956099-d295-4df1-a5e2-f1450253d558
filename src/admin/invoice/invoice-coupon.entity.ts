import { Entity, Column, ManyToOne, ManyToMany, JoinTable } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Branch } from '../branch/branch.entity';
import { CouponType } from 'src/core/enums/entity';
import { Product } from '../product/product.entity';
import { CouponItem } from '../inventory/coupon-item.entity';
@Entity('invoice_coupon')
export class InvoiceCoupon extends DocEntity {
  @ManyToOne(() => CouponItem)
  @ApiPropertyOptional()
  couponItem?: CouponItem;

  @ManyToOne(() => Branch, { onDelete: 'CASCADE' })
  @ApiPropertyOptional({ type: () => Branch })
  branch?: Branch;

  @Column({ type: 'real', default: 0 })
  @ApiPropertyOptional()
  discountValue?: number;

  @Column({ type: 'real', default: 0 })
  @ApiPropertyOptional()
  percent?: number;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  couponName?: string;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  couponCode?: string;

  @Column('enum', {
    enum: CouponType,
    default: null,
  })
  @ApiProperty()
  couponType?: CouponType;

  @Column({
    nullable: false,
    default: 0,
  })
  @ApiProperty()
  order: number;

  @ManyToOne(() => Product, { onDelete: 'CASCADE' })
  @ApiPropertyOptional({ type: () => Product })
  discountProduct?: Product;
}
