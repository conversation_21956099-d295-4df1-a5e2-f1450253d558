// import { BadRequestException, Injectable } from '@nestjs/common';
// import * as _ from 'lodash';
// import { InjectRepository } from '@nestjs/typeorm';
// import { In, IsNull, MoreThanOrEqual, Not, Repository } from 'typeorm';
// import { Decimal } from 'decimal.js';

// import { BaseCrudService } from 'src/core/base/base-crud.service';
// import { Invoice } from './invoice.entity';
// import { CrudRequest } from 'src/core/crud/crud';
// import { InvoicePaymentMethod } from './invoice-payment-method.entity';
// import { CreditHistory } from '../credit-history/credit-history.entity';
// import { Credit } from '../credit/credit.entity';
// import { CommissionLogs } from '../employee/commission/commission-logs.entity';
// import {
//   AppointmentStatus,
//   CouponType,
//   CreditStatus,
//   CreditType,
//   InvoiceStatus,
//   PaymentMethod,
//   PeriodUnit,
//   ProductType,
//   RecordStatus,
// } from 'src/core/enums/entity';
// import { Appointment } from '../appointment/appointment.entity';
// import { Order } from '../order/order.entity';
// import * as moment from 'moment';
// import { CreditSetting } from '../settings/credit/credit-setting.entity';
// import { CouponItem } from '../inventory/coupon-item.entity';
// import { InvoiceCoupon } from './invoice-coupon.entity';
// import { BackDateDto, InvoiceQueryDto } from './dto/invoice.dto';
// import { getCustomPaginationLimit } from '../../core/common/common.utils';
// import { IssueCoupon } from '../inventory/issue-coupon.entity';
// import handlebars from 'handlebars';
// import { MailerService } from '../mailer/mailer.service';
// import { v4 as uuidv4 } from 'uuid';
// import { SEND_COUPON_CODE } from '../mailer/templates/mjml/constant';
// import { renderMJML } from '../mailer/templates/mjml';
// import { AuditTrailOperation } from '../audit-trail/audit-trail.entity';
// import { AuditTrailService } from '../audit-trail/audit-trail.service';
// import { Branch } from '../branch/branch.entity';

// @Injectable()
// export class InvoiceService extends BaseCrudService<Invoice> {
//   constructor(
//     @InjectRepository(Invoice) repo: Repository<Invoice>,
//     @InjectRepository(Credit) private repoCredit: Repository<Credit>,
//     @InjectRepository(CreditSetting)
//     private creditSettingRepo: Repository<CreditSetting>,
//     @InjectRepository(Order)
//     private orderRepo: Repository<Order>,
//     private readonly mailerService: MailerService,
//     private readonly auditTrailService: AuditTrailService,
//   ) {
//     super(repo);
//   }

//   async buyMembership() {}
// }
