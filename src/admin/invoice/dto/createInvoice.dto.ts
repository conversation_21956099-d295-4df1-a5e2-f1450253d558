import { ApiProperty, PickType } from '@nestjs/swagger';
import { Invoice } from '../invoice.entity';
import {
  IsArray,
  IsDateString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Order } from 'src/admin/order/order.entity';
import { InvoicePaymentMethod } from '../invoice-payment-method.entity';

export class CreateInvoiceDto extends PickType(Invoice, ['id', 'appointment']) {
  @ApiProperty()
  @IsOptional()
  @IsDateString()
  date: Date;

  @ApiProperty()
  @IsOptional()
  @IsString()
  code: string;

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  invoicePayments?: InvoicePaymentMethod[];

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  orders?: Order[];

  @IsOptional()
  parentInvoice?: Invoice;

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  childInvoices?: Invoice[];
}
