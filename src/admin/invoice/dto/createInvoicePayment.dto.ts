import { ApiProperty, PickType } from '@nestjs/swagger';
import { Invoice } from '../invoice.entity';
import {
  ArrayNotEmpty,
  IsArray,
  IsDateString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { InvoicePaymentMethod } from '../invoice-payment-method.entity';

export class CreateInvoicePaymentDto extends PickType(InvoicePaymentMethod, [
  'id',
]) {
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  payments?: Payment[];
}

class Payment {
  @IsNotEmpty()
  @IsNumber()
  paid: number;

  @ValidateNested()
  paymentType: { id: string };
}
