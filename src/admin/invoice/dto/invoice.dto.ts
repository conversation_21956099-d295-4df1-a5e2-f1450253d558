import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsDateString,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Max,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';

export class InvoiceQueryDto {
  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  page: number;

  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  @Max(1000)
  @Min(1)
  limit: number;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keySearch?: string;
}

export class BackDateDto {
  @ApiProperty()
  @IsNotEmpty()
  date: string;
}
