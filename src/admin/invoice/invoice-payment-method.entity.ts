import { Entity, Column, ManyToOne } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Setting } from '../setting/setting.entity';
import { PaymentMethod } from '../payment-method/payment-method.entity';
import { Branch } from '../branch/branch.entity';

@Entity('invoice_payment_method')
export class InvoicePaymentMethod extends DocEntity {
  @Column({ type: 'real', nullable: true })
  @ApiPropertyOptional()
  paid?: number;

  @Column({ type: 'real', nullable: true, default: 0 })
  @ApiPropertyOptional()
  roundNumber?: number;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Setting })
  status: Setting;

  @ManyToOne(() => PaymentMethod)
  paymentMethod: PaymentMethod;

  @ManyToOne(() => Branch, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Branch })
  branch: Branch;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  billCode?: string;
}
