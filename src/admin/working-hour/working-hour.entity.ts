import {
  Column,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  OneToOne,
  Unique,
} from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { VirtualWorkingHour } from './virtual-working-hour.entity';
import { Employee } from '../employee/employee.entity';
import { Branch } from '../branch/branch.entity';
import { WHTimework } from './wh-time-work.entity';

@Entity('working_hour')
@Unique(['date', 'branch', 'employee'])
export class WorkingHour extends DocEntity {
  @ManyToOne(() => Employee, { onDelete: 'CASCADE' })
  // @ApiProperty({ type: () => Employee })
  employee: Employee;

  @OneToMany(() => WHTimework, (st) => st.workingHour, {
    cascade: true,
  })
  @ApiProperty({ type: () => WHTimework, isArray: true })
  shiftTimes: WHTimework[];

  @Column({ type: 'timestamptz' })
  @ApiProperty({
    type: 'string',
    format: 'date',
  })
  @Index()
  date: Date;

  @Column({
    type: 'smallint',
  })
  @ApiProperty()
  dayOfWeek: number; // 0 -> 6 (sunday -> saturday) (Date.instance.getDay())

  @ManyToOne(() => Branch)
  @ApiProperty({ type: () => Branch })
  branch: Branch;

  @OneToOne(() => VirtualWorkingHour, (vr) => vr.workingHour, {
    cascade: true,
  })
  @ApiPropertyOptional({ type: () => VirtualWorkingHour })
  virtualRepeat?: VirtualWorkingHour;
}
