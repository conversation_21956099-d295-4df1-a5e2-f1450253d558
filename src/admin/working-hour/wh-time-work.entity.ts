import { Entity, ManyToOne, OneToOne } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { WorkingHour } from './working-hour.entity';
import { VirtualWorkingHour } from './virtual-working-hour.entity';
import { DocEntity } from 'src/core/base/doc.entity';
import { WHTimer } from './wh-timer.entity';

@Entity('wh_timework')
export class WHTimework extends DocEntity {
  @OneToOne(() => WHTimer, (t) => t.refStart, {
    cascade: true,
  })
  @ApiProperty({ type: () => WHTimer })
  startTime: WHTimer;

  @OneToOne(() => WHTimer, (t) => t.refEnd, { cascade: true })
  @ApiProperty({ type: () => WHTimer })
  endTime: WHTimer;

  @ManyToOne(() => WorkingHour, {
    onDelete: 'CASCADE',
    orphanedRowAction: 'delete',
  })
  @ApiProperty({ type: () => WorkingHour })
  workingHour?: WorkingHour;

  @ManyToOne(() => VirtualWorkingHour, {
    onDelete: 'CASCADE',
    orphanedRowAction: 'delete',
  })
  @ApiProperty({ type: () => VirtualWorkingHour })
  virtualWorkingHour?: VirtualWorkingHour;

  constructor(partial: Partial<WHTimework>) {
    super();
    Object.assign(this, partial);
  }
}
