import {
  Column,
  <PERSON>tity,
  Index,
  JoinC<PERSON>umn,
  ManyToOne,
  OneToMany,
  OneToOne,
  Unique,
} from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Branch } from '../branch/branch.entity';
import { Employee } from '../employee/employee.entity';
import { WorkingHour } from './working-hour.entity';
import { WHTimework } from './wh-time-work.entity';

@Entity('virtual_working_hour')
@Unique(['startDate', 'branch', 'employee'])
export class VirtualWorkingHour extends DocEntity {
  @OneToMany(() => WHTimework, (st) => st.virtualWorkingHour, {
    cascade: true,
  })
  @ApiProperty({ type: () => WHTimework, isArray: true })
  shiftTimes: WHTimework[];

  @ManyToOne(() => Employee, { onDelete: 'CASCADE' })
  employee: Employee;

  @Column({
    type: 'smallint',
  })
  @ApiProperty()
  dayOfWeek: number; // 0 -> 6 (sunday -> saturday) (Date.instance.getDay())

  @Column({ type: 'timestamptz' })
  @ApiProperty({
    type: 'string',
    format: 'date',
  })
  @Index()
  startDate: Date;

  @Column({ type: 'timestamptz', nullable: true })
  @ApiPropertyOptional({
    default: null,
    type: 'string',
    format: 'date',
  })
  endDate?: Date; // if 'endDate' null mean virtual repeat to eternal

  @OneToOne(() => WorkingHour, (wh) => wh.virtualRepeat, {
    onDelete: 'CASCADE',
    orphanedRowAction: 'delete', //delete old virtualRepeat when working hour change virtual
  })
  @ApiProperty({ type: () => WorkingHour })
  @JoinColumn()
  workingHour: WorkingHour;

  @ManyToOne(() => Branch)
  @ApiProperty({ type: () => Branch })
  branch: Branch;

  constructor(partial: Partial<VirtualWorkingHour>) {
    super();
    Object.assign(this, partial);
  }
}
