import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { WorkingHour } from './working-hour.entity';
import { WorkingHourService } from './working-hour.service';
import { WorkingHourController } from './working-hour.controller';
import { Employee } from '../employee/employee.entity';
import { VirtualWorkingHour } from './virtual-working-hour.entity';
import { WHTimer } from './wh-timer.entity';
import { DayOff } from '../day-off/day-off.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      WorkingHour,
      Employee,
      VirtualWorkingHour,
      WHTimer,
      DayOff
    ]),
  ],
  controllers: [WorkingHourController],
  providers: [WorkingHourService],
  exports: [WorkingHourService],
})
export class WorkingHourModule {}
