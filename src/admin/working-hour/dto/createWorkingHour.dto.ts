import { ApiProperty } from '@nestjs/swagger';
import { HOUR, MINUTE } from 'src/core/common/common.types';

class TimerDto {
  @ApiProperty()
  hour: HOUR;

  @ApiProperty()
  minute: MINUTE;
}

class TimeWorkDto {
  @ApiProperty({ type: () => TimerDto })
  startTime: TimerDto;

  @ApiProperty({ type: () => TimerDto })
  endTime: TimerDto;
}

export class CreateWorkHourDto {
  @ApiProperty()
  repeat?: boolean;

  @ApiProperty()
  date: Date;

  @ApiProperty({ type: () => TimeWorkDto, isArray: true })
  shiftTimes: TimeWorkDto[];
}
