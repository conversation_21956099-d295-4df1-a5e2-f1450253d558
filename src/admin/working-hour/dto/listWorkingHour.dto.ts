import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsDateString,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
} from 'class-validator';
import { UUID } from 'crypto';
import { Branch } from 'src/admin/branch/branch.entity';
import { Employee } from 'src/admin/employee/employee.entity';

export class QueryDto {
  @ApiProperty()
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'Invalid date format. Please use YYYY-MM-DD format.',
  })
  startDate: string;

  @ApiProperty()
  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'Invalid date format. Please use YYYY-MM-DD format.',
  })
  endDate: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsUUID()
  branch?: UUID;

  @ApiPropertyOptional()
  @IsString()
  keySearch?: string;
}

class ShiftTime {
  @ApiProperty()
  startTime: string;

  @ApiProperty()
  endTime: string;
}

export class WorkingHourDateResponse {
  @ApiProperty()
  date: Date;

  @ApiProperty({ type: () => ShiftTime, isArray: true })
  shiftTimes: ShiftTime[];
}

export class WorkingHourResponse {
  @ApiProperty({ type: () => Employee })
  employee: Employee;

  @ApiProperty({ type: () => Branch })
  branch: Branch;

  @ApiProperty({ type: () => WorkingHourDateResponse, isArray: true })
  dates: WorkingHourDateResponse[];
}
