import { Column, <PERSON>tity, Join<PERSON><PERSON>umn, OneToOne } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty } from '@nestjs/swagger';
import { HOUR, MINUTE } from 'src/core/common/common.types';
import { WHTimework } from './wh-time-work.entity';

@Entity('wh_timer')
export class WHTimer extends DocEntity {
  @Column({ type: 'smallint' })
  @ApiProperty()
  hour: HOUR;

  @Column({ type: 'smallint' })
  @ApiProperty()
  minute: MINUTE;

  @OneToOne(() => WHTimework, (refStart) => refStart.startTime, {
    onDelete: 'CASCADE',
    orphanedRowAction: 'delete',
  })
  @ApiProperty({ type: () => WHTimework })
  @JoinColumn()
  refStart?: WHTimework;

  @OneToOne(() => WHTimework, (refStart) => refStart.endTime, {
    onDelete: 'CASCADE',
    orphanedRowAction: 'delete',
  })
  @ApiProperty({ type: () => WHTimework })
  @JoinColumn()
  refEnd?: WHTimework;

  constructor(partial: Partial<WHTimer>) {
    super();
    Object.assign(this, partial);
  }
}
