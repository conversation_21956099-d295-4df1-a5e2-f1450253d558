import {
  Call<PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';

import { Observable } from 'rxjs';
import { PARSED_CRUD_REQUEST_KEY } from 'src/core/crud/crud/constants';
import { RoleBuiltIn } from 'src/core/database/type';

@Injectable()
export class UserInterceptor implements NestInterceptor {
  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const req = context.switchToHttp().getRequest();
    const user = req.user;
    const crudRequest = req?.[PARSED_CRUD_REQUEST_KEY];
    // if (user && crudRequest?.parsed) {
    //   const roleId = user.role.id;
    //   crudRequest.parsed.search['$and'].push({
    //     ['role.id']:
    //       roleId === RoleBuiltIn.SUPER_ADMIN
    //         ? RoleBuiltIn.ADMIN
    //         : RoleBuiltIn.STAFF,
    //   });
    // }

    // clean $and
    // if (crudRequest.parsed?.search?.$and) {
    //   crudRequest.parsed.search.$and = crudRequest.parsed.search.$and.filter(
    //     (i: any) => !!i,
    //   );
    // }

    return next.handle().pipe();
  }
}
