import {
  Crud<PERSON><PERSON>roller,
  CrudRequest,
  Override,
  ParsedBody,
  ParsedRequest,
} from 'src/core/crud/crud';
import { User } from './user.entity';
import { userPerm } from './user.perm';
import { UserService } from './user.service';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { CreateUserDto } from './dto/createUser.dto';
import { Param, Req, Request, Controller, Get } from '@nestjs/common';
import { UserInterceptor } from './user.interceptor';
import { UpdateUserDto } from './dto/updateUser.dto';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
// import { ResourceGroup } from 'src/core/resource/resource.group';

@BaseCrud(
  {
    model: {
      type: User,
    },
    routes: {
      exclude: ['createManyBase'],
      getManyBase: {
        interceptors: [UserInterceptor],
      },
      getOneBase: {
        interceptors: [UserInterceptor],
      },
    },
    query: {
      join: {
        role: { eager: true, allow: ['id', 'name'] },
        avatar: { eager: true, allow: ['id', 'url'] },
        gender: { eager: true, allow: ['id', 'name'] },
        branch: { eager: true, allow: ['id', 'name'] },
        status: { eager: true, allow: ['id', 'name'] },
      },
    },
  },
  {
    grantPerm: userPerm,
    // group: ResourceGroup.SYSTEM,
  },
)
export class UserController extends BaseCrudController<User> {
  constructor(public service: UserService) {
    super(service);
  }
  get base(): CrudController<User> {
    return this;
  }

  @Get('select-option')
  async getSelectOption(@Req() req: Request) {
    return this.service.getSelectOption(req);
  }

  @Override('createOneBase')
  createOne(
    @ParsedBody() dto: CreateUserDto,
    @Req() req: Request,
    @ParsedRequest() crudRequest: CrudRequest,
  ): Promise<User> {
    return this.service.createOneUser(req, crudRequest, dto);
  }

  @Override('updateOneBase')
  updateOne(
    @ParsedBody() dto: UpdateUserDto,
    @ParsedRequest() crudRequest: CrudRequest,
    @Req() req: Request,
    @Param('id') id: string,
  ): Promise<User> {
    return this.service.updateUser(req, crudRequest, dto, id);
  }

  @Override('deleteOneBase')
  deleteOne(
    @ParsedRequest() crudRequest: CrudRequest,
    @Req() req,
    @Param('id') id: string,
  ) {
    return this.service.deleteOneUser(crudRequest, req, id);
  }
}
