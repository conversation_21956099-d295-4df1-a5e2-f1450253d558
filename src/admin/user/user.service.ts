import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './user.entity';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { CrudRequest } from 'src/core/crud/crud';
import { CreateUserDto } from './dto/createUser.dto';
import { AuthService } from '../auth/auth.service';
import { DuplicatedEntryException } from 'src/core/exception/core/duplicated-entry.exception';
import { waMessage } from 'src/core/exception/exception.messages.contants';
// import { RoleService } from '../role/role.service';
import { UpdateUserDto } from './dto/updateUser.dto';
import { TokenService } from '../token/token.service';
// import { MailerService } from 'src/core/mailer/mailer.service';
// import { resetPasswordTemplate } from 'src/core/mailer/templates';
// import handlebars from 'handlebars';

@Injectable()
export class UserService extends BaseCrudService<User> {
  constructor(
    @InjectRepository(User) repo: Repository<User>,
    @Inject(forwardRef(() => AuthService))
    private authService: AuthService,
    // @Inject(forwardRef(() => RoleService))
    // private roleService: RoleService,
    @Inject(forwardRef(() => TokenService))
    private tokenService: TokenService, // private readonly mailerService: MailerService,
  ) {
    super(repo);
  }
  async findById(userId: string, relations?: string[]): Promise<User> {
    return this.repo.findOne({
      relations,
      where: { id: userId },
    });
  }

  async getSelectOption(req: any) {
    const headers = req.headers;

    const branchIds = headers?.branchid?.split(',') || [];

    return await this.repo
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.role', 'role')
      .leftJoinAndSelect('user.avatar', 'avatar')
      .leftJoinAndSelect('user.gender', 'gender')
      .leftJoinAndSelect('user.branch', 'branch')
      .leftJoinAndSelect('user.status', 'status')
      .where('branch.id = :branchId', { branchId: branchIds[0] })
      .orWhere('branch.id is null')
      .getMany();
  }

  async findByEmail(userEmail: string, relations?: string[]): Promise<any> {
    const user = await this.repo.findOne({
      relations,
      where: { email: userEmail },
    });
    return user;
  }

  async checkTokenToGetUserId(userId: string): Promise<any> {
    const user = await this.repo.findOne({
      relations: ['tokens'],
      where: {
        id: userId,
      },
      select: {
        tokens: { tokenString: true },
      },
    });
    return user.tokens[0].tokenString;
  }

  async getTokenByUserId(userId: string): Promise<any> {
    const user = await this.repo.findOne({
      relations: ['tokens'],
      where: {
        id: userId,
      },
      select: {
        tokens: { tokenString: true },
      },
    });
    return user.tokens[0].tokenString;
  }

  async createOneUser(
    req: Request,
    crudRequest: CrudRequest,
    dto: CreateUserDto,
  ): Promise<User> {
    const reqUser = req['user'];

    // check email not exsist in DB
    await this.isEmailValidForNewUser(dto.email);

    // check role level can create user has smaller role level
    // await this.validateRole(dto, reqUser, null);

    // create user
    const user = await super.createOne(crudRequest, dto);
    // save password
    await this.authService.setPassword(user.id, dto.password);

    // send mail when reset pass success
    // const contentTemplate = handlebars.compile(resetPasswordTemplate);

    // const html = contentTemplate({
    //   username: user.email,
    //   password: dto.password,
    //   publicKey: process.env.ESSENTIAL_URL,
    // });

    // await this.mailerService.sendMail({
    //   to: user.email,
    //   html,
    //   subject: 'Welcome Essential',
    //   text: 'Welcome Essential',
    // });

    return user;
  }

  async updateUser(
    req: Request,
    crudRequest: CrudRequest,
    dto: UpdateUserDto,
    userId: string,
  ): Promise<User> {
    const reqUser = req['user'];
    const beforeUpdatedUser = await this.repo.findOne({
      where: { id: userId },
      relations: ['role'],
    });

    if (!beforeUpdatedUser) {
      throw new BadRequestException(waMessage.exception.userNotFound.message);
    }

    if (reqUser.id === beforeUpdatedUser.id) {
      throw new BadRequestException(
        waMessage.exception.cannotUpdateYourself.message,
      );
    }

    // check email not exsist in DB
    if (dto?.email) {
      if (dto.email.trim() !== beforeUpdatedUser.email) {
        await this.isEmailValidForNewUser(dto.email);
      }
    }
    // check role level can create user has smaller role level
    // await this.validateRole(dto, reqUser, beforeUpdatedUser);

    return super.updateOne(crudRequest, dto);
  }

  async isEmailValidForNewUser(email: string): Promise<void> {
    const user = await this.repo.findOne({
      select: ['id'],
      where: {
        email: email?.trim(),
      },
    });

    if (!!user) {
      throw new DuplicatedEntryException('email');
    }
  }

  async deleteOneUser(
    crudRequest: CrudRequest,
    req,
    userId: string,
  ): Promise<void | User> {
    const reqUser = req['user'];
    const targetUser = await this.repo.findOne({
      where: {
        id: userId,
      },
      relations: ['role'],
    });

    if (!targetUser) {
      throw new BadRequestException(waMessage.exception.userNotFound.message);
    }

    switch (true) {
      case reqUser['id'] === targetUser.id:
        throw new BadRequestException(waMessage.exception.notDelMyUser.message);
      default:
    }

    await this.tokenService.revokeToken(userId);

    await this.authService.delete(userId);

    return super.deleteOne(crudRequest);
  }
}
