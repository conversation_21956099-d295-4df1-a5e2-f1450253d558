import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  OneToMany,
  ManyToOne,
  Index,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { Role } from '../role/role.entity';
import { Branch } from '../branch/branch.entity';
import { DocEntity } from 'src/core/base/doc.entity';
import { Auth } from '../auth/auth.entity';
import { Token } from '../token/token.entity';
import { Setting } from '../setting/setting.entity';
import { ApiProperty } from '@nestjs/swagger';
import { Media } from '../media/media.entity';
@Entity('user')
export class User extends DocEntity {
  @Column({
    nullable: true,
  })
  fullname: string;

  @Column({
    nullable: true,
  })
  displayName?: string;

  @Column({
    nullable: true,
  })
  uniqueID?: string;

  @Column({
    nullable: true,
  })
  phone?: string;

  @Column({
    type: 'smallint',
    default: 65,
  })
  phoneCode: number;

  @Column({
    nullable: true,
  })
  username?: string;

  @Column({
    nullable: true,
  })
  @Index({ unique: true })
  email?: string;

  @Column({
    nullable: true,
  })
  referralSource?: string;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  birthday?: Date;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Setting })
  gender?: Setting;

  @Column({
    type: 'text',
    nullable: true,
  })
  note?: string;

  @Column({
    type: 'text',
    nullable: true,
  })
  address?: string;

  @Column({
    type: 'text',
    nullable: true,
  })
  description?: string;

  @ApiProperty({ required: false })
  @OneToOne(() => Media, (media) => media.user)
  @JoinColumn()
  avatar: Media;

  @Column({
    nullable: true,
  })
  acceptMarketingNotifi?: boolean;

  @ManyToOne(() => Role, (role) => role.user)
  role: Role;

  @ManyToOne(() => Branch, (branch) => branch.user, { onDelete: 'CASCADE' })
  branch: Branch;

  @OneToMany(() => Auth, (auth) => auth.user, { cascade: ['insert'] })
  auths: Auth[];

  @OneToMany(() => Token, (token) => token.user)
  tokens: Token[];

  @ManyToOne(() => Setting, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Setting })
  status: Setting;
}
