// import { RoleBuiltIn } from 'src/core/database/type/index';
import { IGrantPerm } from 'src/core/base/decorator/grantPerm.decorator';
import { RoleBuiltIn } from 'src/core/database/type';

export const userPerm: IGrantPerm[] = [
  {
    roleId: RoleBuiltIn.SUPER_ADMIN,
    create: true,
    read: true,
    update: true,
    delete: true,
    list: true,
  },
  {
    roleId: RoleBuiltIn.ADMIN,
    create: true,
    read: true,
    update: true,
    delete: true,
    list: true,
  },
  {
    roleId: RoleBuiltIn.STAFF,
    create: false,
    read: true,
    update: false,
    delete: false,
    list: true,
  },
];
