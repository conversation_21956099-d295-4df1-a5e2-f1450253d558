import { CrudController } from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { CreditSetting } from './credit-setting.entity';
import { CreditService } from './credit-setting.service';
import { CreditSettingDto } from './dto/createCreditSetting.dto';
import { BaseCrud } from 'src/admin/auth/decorators/base-crud.decorator';

@BaseCrud(
  {
    model: {
      type: CreditSetting,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    dto: {
      create: CreditSettingDto,
    },
    query: {
      join: {
        role: { eager: true, allow: ['id', 'name'] },
        restrictions: { eager: true, allow: ['id', 'name'] },
        status: { eager: true, allow: ['id', 'name'] },
        consumptionPeriod: { eager: true, allow: ['id', 'name'] },
      },
    },
  },
  {
    // grantPerm: userPerm,
    // group: ResourceGroup.SYSTEM,
  },
)
export class CreditController extends BaseCrudController<CreditSetting> {
  constructor(public service: CreditService) {
    super(service);
  }
  get base(): CrudController<CreditSetting> {
    return this;
  }
}
