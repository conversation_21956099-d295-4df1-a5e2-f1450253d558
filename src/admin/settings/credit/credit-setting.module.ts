import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { CreditController } from './credit-setting.controller';
import { CreditSetting } from './credit-setting.entity';
import { CreditService } from './credit-setting.service';

@Module({
  imports: [TypeOrmModule.forFeature([CreditSetting])],
  controllers: [CreditController],
  providers: [CreditService],
  exports: [CreditService],
})
export class CreditSettingModule {}
