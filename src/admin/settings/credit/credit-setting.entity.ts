import { Entity, Column, ManyTo<PERSON>ne, ManyToMany, JoinTable } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Branch } from 'src/admin/branch/branch.entity';
import { Customer } from 'src/admin/customer/customer.entity';
import { Setting } from 'src/admin/setting/setting.entity';
import { CreditType } from 'src/core/enums/entity';

@Entity('credit_setting')
export class CreditSetting extends DocEntity {
  @Column({
    nullable: false,
  })
  @ApiProperty()
  price: number;

  @Column({
    nullable: false,
  })
  @ApiProperty()
  name: string;

  @Column({
    nullable: false,
  })
  @ApiProperty()
  credit: number;

  @Column({
    nullable: false,
    type: 'timestamptz',
  })
  @ApiProperty()
  applyFromTheDate: Date;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Setting })
  status: Setting;

  @Column({
    nullable: true,
    default: null,
    type: 'enum',
    enum: Object.values(CreditType),
  })
  @ApiProperty()
  creditType?: CreditType;
}
