import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import { CreditSetting } from './credit-setting.entity';

@Injectable()
export class CreditService extends BaseCrudService<CreditSetting> {
  constructor(
    @InjectRepository(CreditSetting) repo: Repository<CreditSetting>,
  ) {
    super(repo);
  }
}
