import { ApiProperty, PickType } from '@nestjs/swagger';
import { CreditSetting } from '../credit-setting.entity';
import { IsDateString, IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class CreditSettingDto extends PickType(CreditSetting, ['id']) {
  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  price: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  credit: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsDateString()
  applyFromTheDate: Date;
}
