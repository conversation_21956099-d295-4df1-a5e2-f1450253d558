import { CrudController } from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from 'src/admin/auth/decorators/base-crud.decorator';
import { CouponService } from './coupon-setting.service';
import { CouponSetting } from './coupon-setting.entity';
import { CouponSettingDto } from './dto/createCouponSetting.dto';

@BaseCrud(
  {
    model: {
      type: CouponSetting,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    dto: {
      create: CouponSettingDto,
    },
    query: {
      sort: [
        {
          field: 'percent',
          order: 'ASC',
        },
      ],
    },
  },
  {
    // grantPerm: userPerm,
    // group: ResourceGroup.SYSTEM,
  },
)
export class CouponController extends BaseCrudController<CouponSetting> {
  constructor(public service: CouponService) {
    super(service);
  }

  get base(): CrudController<CouponSetting> {
    return this;
  }
}
