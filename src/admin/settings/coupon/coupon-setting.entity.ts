import { <PERSON>tity, Column, ManyToOne } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Setting } from 'src/admin/setting/setting.entity';

@Entity('coupon_setting')
export class CouponSetting extends DocEntity {
  @Column({ type: 'real', default: 0 })
  @ApiPropertyOptional()
  percent?: number;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Setting })
  status: Setting;
}
