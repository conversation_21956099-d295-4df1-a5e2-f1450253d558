import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import { CouponSetting } from './coupon-setting.entity';

@Injectable()
export class CouponService extends BaseCrudService<CouponSetting> {
  constructor(
    @InjectRepository(CouponSetting) repo: Repository<CouponSetting>,
  ) {
    super(repo);
  }
}
