import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { CouponController } from './coupon-setting.controller';
import { CouponService } from './coupon-setting.service';
import { CouponSetting } from './coupon-setting.entity';

@Module({
  imports: [TypeOrmModule.forFeature([CouponSetting])],
  controllers: [CouponController],
  providers: [CouponService],
  exports: [CouponService],
})
export class CouponSettingModule {}
