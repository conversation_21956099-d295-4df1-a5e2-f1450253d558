import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import { PayslipSetting } from './payslip-setting.entity';
import { UpsertPayslipSettingDto } from './dto/upsertPayslipSetting.dto';
import { PayslipEmployeeSetting } from './payslip-employee-setting.entity';
import { EmployeeService } from 'src/admin/employee/employee.service';

@Injectable()
export class PayslipSettingService extends BaseCrudService<PayslipSetting> {
  constructor(
    @InjectRepository(PayslipSetting) repo: Repository<PayslipSetting>,
    @InjectRepository(PayslipEmployeeSetting)
    private payslipEmployeeRepo: Repository<PayslipEmployeeSetting>,
    private readonly employeeService: EmployeeService,
  ) {
    super(repo);
  }

  async getAllPayslipSetting(branchIds: string[], name: string) {
    const payslipSetting = await this.repo.findOne({
      where: { deleted: null },
    });
    const employeeAll = await this.employeeService.filterEmployee(
      name,
      branchIds,
    );

    if (employeeAll.length > 0) {
      const payslipEmployeeList = await Promise.all(
        employeeAll.map(async (employee) => {
          const payslipEmployeeCheck = await this.payslipEmployeeRepo.findOne({
            where: { employee: { id: employee.id } },
            relations: ['employee'],
          });

          if (!payslipEmployeeCheck?.id) {
            return {
              basicSalary: payslipSetting?.basicSalary,
              oTPay: payslipSetting?.oTPay,
              allowance: payslipSetting?.allowance,
              employee: {
                id: employee.id,
                fullName: employee.fullName,
                displayName: employee.displayName,
              },
            };
          } else {
            return payslipEmployeeCheck;
          }
        }),
      );
      return payslipEmployeeList;
    } else {
      return [];
    }
  }

  async updateMany(input: UpsertPayslipSettingDto): Promise<any> {
    if (input.data.length > 0) {
      for (const commission of input.data) {
        const employeeId = commission.employeeId;
        delete commission.employeeId;
        const existingRecord = await this.payslipEmployeeRepo.findOne({
          where: {
            employee: {
              id: employeeId,
            },
          },
        });
        if (!existingRecord) {
          await this.payslipEmployeeRepo.insert({
            employee: { id: employeeId },
            ...commission,
          });
        } else {
          await this.payslipEmployeeRepo.update(
            { employee: { id: employeeId } },
            {
              ...existingRecord,
              ...commission,
            },
          );
        }
      }
    }
  }
}
