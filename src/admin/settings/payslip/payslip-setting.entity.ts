import { <PERSON>ti<PERSON>, Column, ManyToOne } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty } from '@nestjs/swagger';
import { Employee } from 'src/admin/employee/employee.entity';

@Entity('payslip_setting')
export class PayslipSetting extends DocEntity {
  @Column({ type: 'integer', default: 20 })
  @ApiProperty()
  basicSalary: number;

  @Column({ type: 'integer', default: 20 })
  @ApiProperty()
  oTPay: number;

  @Column({ type: 'integer', default: 200 })
  @ApiProperty()
  allowance: number;
}
