import { TypeOrmModule } from '@nestjs/typeorm';
import { Module, forwardRef } from '@nestjs/common';
import { PayslipController } from './payslip-setting.controller';
import { PayslipSetting } from './payslip-setting.entity';
import { PayslipSettingService } from './payslip-setting.service';
import { Employee } from 'src/admin/employee/employee.entity';
import { PayslipEmployeeSetting } from './payslip-employee-setting.entity';
import { EmployeeModule } from 'src/admin/employee/employee.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      PayslipSetting,
      Employee,
      PayslipEmployeeSetting,
    ]),
    forwardRef(() => EmployeeModule),
  ],
  controllers: [PayslipController],
  providers: [PayslipSettingService],
  exports: [PayslipSettingService],
})
export class PayslipSettingModule {}
