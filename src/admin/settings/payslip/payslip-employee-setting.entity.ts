import { Entity, Column, ManyToOne } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty } from '@nestjs/swagger';
import { Employee } from 'src/admin/employee/employee.entity';

@Entity('payslip_employee_setting')
export class PayslipEmployeeSetting extends DocEntity {
  @Column({ type: 'integer', default: 20 })
  @ApiProperty()
  basicSalary: number;

  @Column({ type: 'integer', default: 20 })
  @ApiProperty()
  oTPay: number;

  @Column({ type: 'integer', default: 200 })
  @ApiProperty()
  allowance: number;

  @ManyToOne(() => Employee, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Employee })
  employee: Employee;
}
