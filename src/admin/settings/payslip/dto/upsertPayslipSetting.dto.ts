import { ApiProperty, ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { PayslipSetting } from '../payslip-setting.entity';
import { IsNotEmpty } from 'class-validator';
import { UUID } from 'crypto';

export class UpsertPayslipSettingDto extends PickType(PayslipSetting, []) {
  @IsNotEmpty()
  @ApiProperty({ type: () => Array })
  data: PayslipEmployee[];
}
class PayslipEmployee {
  @IsNotEmpty()
  @ApiProperty()
  employeeId: UUID;

  @ApiPropertyOptional()
  basicSalary?: number;

  @ApiPropertyOptional()
  oTPay?: number;

  @ApiPropertyOptional()
  allowance?: number;
}
