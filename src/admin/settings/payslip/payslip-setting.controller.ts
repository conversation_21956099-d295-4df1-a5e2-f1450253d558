import { CrudController, Override } from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { PayslipSetting } from './payslip-setting.entity';
import { PayslipSettingService } from './payslip-setting.service';
import { UpsertPayslipSettingDto } from './dto/upsertPayslipSetting.dto';
import { BaseCrud } from 'src/admin/auth/decorators/base-crud.decorator';
import { Body, Post, Query, Req } from '@nestjs/common';

@BaseCrud(
  {
    model: {
      type: PayslipSetting,
    },
    routes: {
      only: ['getManyBase'],
    },
    query: {},
  },
  {
    // grantPerm: userPerm,
    // group: ResourceGroup.SYSTEM,
  },
)
export class PayslipController extends BaseCrudController<PayslipSetting> {
  constructor(public service: PayslipSettingService) {
    super(service);
  }
  get base(): CrudController<PayslipSetting> {
    return this;
  }

  @Override('getManyBase')
  async getMany(@Query('name') name: string, @Req() req: Request) {
    const branchIds = req.headers?.['branchid']?.split(',') || [];
    return this.service.getAllPayslipSetting(branchIds, name);
  }

  @Post('/updateMany')
  async updateManyCommissionSetting(
    @Body() data: UpsertPayslipSettingDto,
  ): Promise<boolean> {
    await this.service.updateMany(data);
    return true;
  }
}
