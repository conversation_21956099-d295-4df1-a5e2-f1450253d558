import { <PERSON>tity, Column, ManyToOne } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty } from '@nestjs/swagger';
import { Product } from 'src/admin/product/product.entity';

@Entity('commission_product_setting')
export class CommissionProductSetting extends DocEntity {
  @Column({ type: 'integer', default: 15 })
  @ApiProperty()
  employee: number;

  @Column({ type: 'integer', default: 10 })
  @ApiProperty()
  frontdesk: number;

  @Column({ type: 'integer', default: 10 })
  @ApiProperty()
  fBStaff: number;

  @ManyToOne(() => Product, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Product })
  product: Product;
}
