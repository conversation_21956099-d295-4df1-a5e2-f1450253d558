import { <PERSON><PERSON>ty, Column, ManyToOne, ManyToMany, JoinT<PERSON> } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Product } from 'src/admin/product/product.entity';

@Entity('commission_setting')
export class CommissionSetting extends DocEntity {
  @Column({ type: 'integer', default: 15 })
  @ApiProperty()
  employee: number;

  @Column({ type: 'integer', default: 10 })
  @ApiProperty()
  frontdesk: number;

  @Column({ type: 'integer', default: 10 })
  @ApiProperty()
  fBStaff: number;
}
