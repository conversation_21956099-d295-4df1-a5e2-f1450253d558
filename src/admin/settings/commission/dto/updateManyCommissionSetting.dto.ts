import { ApiProperty, ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { CommissionProductSetting } from '../commission-product-setting.entity';
import { UUID } from 'crypto';

export class UpdateManyCommissionSettingDto extends PickType(
  CommissionProductSetting,
  [],
) {
  @IsNotEmpty()
  @ApiProperty({ type: () => Array })
  data: CommissionProduct[];
}
class CommissionProduct {
  @IsNotEmpty()
  @ApiProperty()
  productId: UUID;

  @ApiPropertyOptional()
  employee?: number;

  @ApiPropertyOptional()
  frontdesk?: number;

  @ApiPropertyOptional()
  fBStaff?: number;
}
