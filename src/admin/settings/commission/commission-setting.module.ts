import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { CommissionSettingController } from './commission-setting.controller';
import { CommissionSetting } from './commission-setting.entity';
import { CommissionSettingService } from './commission-setting.service';
import { CommissionProductSetting } from './commission-product-setting.entity';
import { Product } from 'src/admin/product/product.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      CommissionSetting,
      CommissionProductSetting,
      Product,
    ]),
  ],
  controllers: [CommissionSettingController],
  providers: [CommissionSettingService],
  exports: [CommissionSettingService],
})
export class CommissionSettingModule {}
