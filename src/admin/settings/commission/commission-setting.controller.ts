import { CrudController, Override } from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { CommissionSetting } from './commission-setting.entity';
import { CommissionSettingService } from './commission-setting.service';
import { BaseCrud } from 'src/admin/auth/decorators/base-crud.decorator';
import { Body, Post, Query, Req } from '@nestjs/common';
import { UpdateManyCommissionSettingDto } from './dto/updateManyCommissionSetting.dto';

@BaseCrud(
  {
    model: {
      type: CommissionSetting,
    },
    routes: {
      only: ['getManyBase'],
    },
    query: {
      join: {
        product: { eager: true, allow: ['id', 'name'] },
      },
    },
  },
  {
    // grantPerm: userPerm,
    // group: ResourceGroup.SYSTEM,
  },
)
export class CommissionSettingController extends BaseCrudController<CommissionSetting> {
  constructor(public service: CommissionSettingService) {
    super(service);
  }
  get base(): CrudController<CommissionSetting> {
    return this;
  }

  @Override('getManyBase')
  async getMany(
    @Query('type') type: string,
    @Query('name') name: string,
    @Req() req: Request,
  ) {
    const branchIds = req.headers?.['branchid']?.split(',') || [];
    return this.service.getAllCommissionSetting(branchIds, type, name);
  }

  @Post('/updateMany')
  async updateManyCommissionSetting(
    @Body() data: UpdateManyCommissionSettingDto,
  ): Promise<boolean> {
    await this.service.updateMany(data);
    return true;
  }
}
