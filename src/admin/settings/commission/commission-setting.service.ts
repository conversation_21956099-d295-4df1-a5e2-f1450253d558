import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ILike, In, Repository } from 'typeorm';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import { CommissionSetting } from './commission-setting.entity';
import { UpdateManyCommissionSettingDto } from './dto/updateManyCommissionSetting.dto';
import { CommissionProductSetting } from './commission-product-setting.entity';
import { Product } from 'src/admin/product/product.entity';

@Injectable()
export class CommissionSettingService extends BaseCrudService<CommissionSetting> {
  constructor(
    @InjectRepository(CommissionSetting) repo: Repository<CommissionSetting>,
    @InjectRepository(CommissionProductSetting)
    private commissionProductRepo: Repository<CommissionProductSetting>,
    @InjectRepository(Product)
    private productRepo: Repository<Product>,
  ) {
    super(repo);
  }

  async getAllCommissionSetting(
    branchIds: string[],
    type: string,
    name: string,
  ) {
    const commissionSetting = await this.repo.findOne({
      where: { deleted: null },
    });
    const whereClause: Record<string, any> = { deleted: null };

    if (type !== undefined) {
      whereClause.type = type;
    }

    if (name !== undefined) {
      whereClause.name = ILike(`%${name}%`);
    }
    if (branchIds.length > 0) {
      whereClause.branches = {};
      whereClause.branches.id = In(branchIds);
    }

    const productAll = await this.productRepo.find({
      where: whereClause,
    });

    if (productAll.length > 0) {
      const commissionProductList = await Promise.all(
        productAll.map(async (product) => {
          const commissionProductCheck =
            await this.commissionProductRepo.findOne({
              where: { product: { id: product.id } },
              relations: ['product'],
            });

          if (!commissionProductCheck?.id) {
            return {
              employee: commissionSetting?.employee,
              frontdesk: commissionSetting?.frontdesk,
              fBStaff: commissionSetting?.fBStaff,
              product: {
                id: product.id,
                name: product.name,
                type: product.type,
              },
            };
          } else {
            return commissionProductCheck;
          }
        }),
      );
      return commissionProductList;
    } else {
      return [];
    }
  }

  async updateMany(input: UpdateManyCommissionSettingDto): Promise<any> {
    if (input.data.length > 0) {
      for (const commission of input.data) {
        const productId = commission.productId;
        delete commission.productId;
        const existingRecord = await this.commissionProductRepo.findOne({
          where: {
            product: {
              id: productId,
            },
          },
        });
        if (!existingRecord) {
          await this.commissionProductRepo.insert({
            product: { id: productId },
            ...commission,
          });
        } else {
          await this.commissionProductRepo.update(
            { product: { id: productId } },
            {
              ...existingRecord,
              ...commission,
            },
          );
        }
      }
    }
  }

  async getCommissionByProductId(productId: string, commissionListData: any[], isExport = false) {
    const commission = commissionListData.find((c) => c.product.id === productId);
    if (commission) {
      if (isExport) {
        return {
          employee: commission.employee + '%',
          frondesk: commission.frontdesk + '%',
          staff: commission.fBStaff + '%',
        };
      }
      return {
        employee: commission.employee,
        frondesk: commission.frontdesk,
        staff: commission.fBStaff,
      }
    }
    if (isExport) {
      return {
        employee: '2%',
        frondesk: '10%',
        staff: '5%',
      };
    }
    return {
      employee: 2,
      frondesk: 10,
      fBStaff: 5,
    };
  }
}
