import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import * as jwt from 'jsonwebtoken';
import * as moment from 'moment';
import { CrudRequest } from 'src/core/crud/crud';
import { Decimal } from 'decimal.js';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, In, IsNull, Not, Repository } from 'typeorm';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import {
  AppointmentStatus,
  AppointmentType,
  CreditStatus,
  CreditType,
  InvoiceStatus,
  ProductType,
} from 'src/core/enums/entity';
import { Customer } from './customer.entity';
import { Invoice } from '../invoice/invoice.entity';
import { checkInCustomerDto } from './dto/checkInCustomer.dto';
import {
  AppointmentSummaryQueryDto,
  InvoiceSummaryQueryDto,
  MembershipSummaryQueryDto,
} from './dto/summaryCustomer.dto';
import { AppointmentService } from '../appointment/appointment.service';
import { Rfid } from '../rfid/rfid.entity';
import { Appointment } from '../appointment/appointment.entity';
import { Credit } from '../credit/credit.entity';
import {
  getCustomPaginationLimit,
  UTCtimeZone,
} from '../../core/common/common.utils';
import { waMessage } from '../../core/exception/exception.messages.contants';
import { CreditHistory } from '../credit-history/credit-history.entity';
import { DuplicatedEntryException } from '../../core/exception/core/duplicated-entry.exception';
import { OrderDetail } from '../order-detail/order-detail.entity';
import { PaymentMethodType } from '../payment-method/payment-method.entity';

@Injectable()
export class CustomerService extends BaseCrudService<Customer> {
  private readonly secretKey: string = '0RdBHlbG8P1FFh6';

  constructor(
    @InjectRepository(Customer) repo: Repository<Customer>,
    @InjectRepository(Credit) private readonly creditRepo: Repository<Credit>,
    @InjectRepository(Appointment)
    private appointmentRepo: Repository<Appointment>,
    private readonly appointmentService: AppointmentService,
    @InjectRepository(CreditHistory)
    private creditHistoryRepo: Repository<CreditHistory>,
  ) {
    super(repo);
  }

  async getAppointmentInDay(customerId: string): Promise<Appointment[]> {
    return await this.appointmentService
      .find({
        where: {
          customer: { id: customerId },
          startTime: Between(
            moment().startOf('day').toDate(),
            moment().endOf('day').toDate(),
          ),
          checkIn: IsNull(),
        },
        relations: [
          'customer',
          'orders',
          'orders.items',
          'orders.items.product',
          'orders.items.employees',
        ],
        select: {
          id: true,
          startTime: true,
          customer: {
            id: true,
            code: true,
            firstName: true,
            lastName: true,
            phone: true,
          },
          orders: {
            id: true,
            items: {
              id: true,
              product: {
                id: true,
                name: true,
              },
            },
          },
        },
      })
      .then((result) => {
        return result.map((item) => {
          const orders = [];
          for (const o of item.orders) {
            for (const i of o.items) {
              const employees = [];
              if (i.employees.length > 0) {
                for (const emp of i.employees) {
                  employees.push(emp.displayName);
                }
              }
              orders.push({ name: i.product.name, employees });
            }
          }
          return {
            ...item,
            orders,
          };
        });
      });
  }

  async getCustomerSummary(customerId: string): Promise<any> {
    const [appointments, total] = await this.repo.manager.findAndCount(
      Appointment,
      {
        where: {
          customer: {
            id: customerId,
          },
        },
      },
    );

    const completed = await this.repo.manager.count(Appointment, {
      where: {
        customer: {
          id: customerId,
        },
        status: Not(AppointmentStatus.CANCEL),
      },
    });

    let cancelled = 0;
    const noShows = 0;
    for (const appointment of appointments) {
      if (appointment.status === AppointmentStatus.CANCEL) {
        cancelled += 1;
      }
    }

    const newCredit = await this.repo.manager.findOne(Credit, {
      where: {
        customer: { id: customerId },
        creditSetting: {
          creditType: CreditType.NEW,
        },
        status: CreditStatus.VALID,
      },
      relations: ['creditSetting'],
    });

    const oldCredit = await this.repo.manager.findOne(Credit, {
      where: {
        customer: { id: customerId },
        creditSetting: {
          creditType: CreditType.OLD,
        },
        status: CreditStatus.VALID,
      },
      relations: ['creditSetting'],
    });

    const totalCredit =
      newCredit?.creditBalance || 0 + oldCredit?.creditBalance || 0;

    const invoices = await this.repo.manager.find(Invoice, {
      where: {
        customer: { id: customerId },
        invoicePayments: {
          paymentMethod: {
            type: In([
              PaymentMethodType.CARD,
              PaymentMethodType.CASH,
              PaymentMethodType.FOC,
            ]),
          },
        },
      },
    });
    let totalSales = new Decimal(0);
    for (const invoice of invoices) {
      totalSales = totalSales.plus(new Decimal(invoice.paid));
    }

    return {
      totalSales: parseFloat(totalSales.toFixed(2)),
      availableCredit: totalCredit ? parseFloat(totalCredit.toFixed(2)) : 0,
      validity: newCredit?.expiryDate || oldCredit?.expiryDate || null,
      allBookings: total,
      completed,
      cancelled,
      noShows,
    };
  }

  async getAppointmentSummary(
    customerId: string,
    { limit, page }: AppointmentSummaryQueryDto,
    crudReq: CrudRequest,
  ): Promise<any> {
    const query = {
      startDate: moment().startOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS'),
      endDate: moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS'),
    };

    let qStartDate = query.startDate;
    let qEndDate = query.endDate;
    let startDateFlag = false;
    let endDateFlag = false;
    if (crudReq.parsed?.filter) {
      for (const f of crudReq.parsed.filter) {
        if (f.field === 'startDate') {
          startDateFlag = true;
          query.startDate = moment(f.value[0].replace('Z', ''))
            .tz(UTCtimeZone)
            .format('YYYY-MM-DDTHH:mm:ss.SSS');
        }
        if (f.field === 'endDate') {
          endDateFlag = true;
          query.endDate = moment(f.value[0].replace('Z', ''))
            .tz(UTCtimeZone)
            .format('YYYY-MM-DDTHH:mm:ss.SSS');
        }
      }
    }

    if (startDateFlag && endDateFlag) {
      qStartDate = query.startDate;
      qEndDate = query.endDate;
    }

    // case: get data up-coming
    if (!startDateFlag && endDateFlag) {
      qStartDate = null;
      qEndDate = query.endDate;
    }

    // case: get data past
    if (startDateFlag && !endDateFlag) {
      qStartDate = query.startDate;
      qEndDate = null;
    }

    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;

    const [data, total] = await this.appointmentRepo
      .createQueryBuilder('appointment')
      .leftJoinAndSelect('appointment.customer', 'customer')
      .leftJoinAndSelect('appointment.orders', 'orders')
      .leftJoinAndSelect('orders.items', 'items')
      .leftJoinAndSelect('items.product', 'product')
      .leftJoinAndSelect('product.duration', 'duration')
      .leftJoinAndSelect('items.employees', 'employees')
      .where('appointment.customer = :customerId', { customerId })
      .andWhere(
        qStartDate && qEndDate
          ? 'appointment.startTime BETWEEN :qStartDate AND :qEndDate'
          : qEndDate && !qStartDate
          ? 'appointment.startTime > :qEndDate'
          : 'appointment.startTime < :qStartDate',
        {
          qStartDate,
          qEndDate,
        },
      )
      .andWhere('product.type = :productType', {
        productType: ProductType.SERVICE,
      })
      .orderBy('appointment.startTime', 'DESC')
      .select([
        'appointment.id',
        'appointment.startTime',
        'appointment.status',
        'customer.id',
        'customer.code',
        'customer.firstName',
        'customer.lastName',
        'customer.phone',
        'orders.id',
        'orders.total',
        'items.id',
        'items.quantity',
        'product.id',
        'product.name',
        'product.type',
        'duration.id',
        'duration.name',
        'employees.id',
        'employees.displayName',
        'employees.fullName',
      ])
      .take(qLimit)
      .skip(offset)
      .getManyAndCount();

    const outOrders = data.map((ord) => {
      let sumTotal = new Decimal(0);
      const orders = [];
      let invoiceDate = null;
      if (ord.orders && ord.orders.length > 0 && ord.orders[0].invoice) {
        invoiceDate = ord.orders[0].invoice.date;
      }
      for (const o of ord.orders) {
        sumTotal = sumTotal.plus(new Decimal(o.total));
        for (const i of o.items) {
          const employees = [];
          if (i.employees.length > 0) {
            for (const emp of i.employees) {
              employees.push(emp.displayName ? emp.displayName : emp.fullName);
            }
          }
          orders.push({
            ...i,
            employees,
            status: ord.status,
            invoiceDate: invoiceDate,
          });
        }
      }
      return {
        ...ord,
        orders,
        sumTotal,
        invoiceDate,
      };
    });

    return this.createPageInfo<Appointment>(
      outOrders,
      total,
      qLimit || total,
      offset || 0,
    );
  }

  async getInvoiceSummary(
    customerId: string,
    { limit, page }: InvoiceSummaryQueryDto,
  ): Promise<any> {
    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;
    const [data, total] = await this.repo.manager.findAndCount(Invoice, {
      take: qLimit,
      skip: offset,
      where: {
        customer: { id: customerId },
      },
      order: {
        date: 'DESC',
      },
      relations: ['customer', 'orders', 'parentInvoice', 'childInvoices'],
    });
    const credits = await this.repo.manager.find(Credit, {
      where: {
        customer: {
          id: customerId,
        },
      },
    });
    let memberships = [];
    if (credits.length) {
      const mapCredits = credits.map((credit) => ({ id: credit.id }));
      const creditHistories = await this.repo.manager.find(CreditHistory, {
        where: {
          credit: mapCredits,
          expiryDate: Not(IsNull()),
        },
      });
      if (creditHistories.length) {
        memberships = creditHistories.map((creditHistory) => {
          const detail = creditHistory?.detail as any;
          return {
            invoiceId: detail?.id,
            orderId: detail?.orders[0]?.id,
            productId: detail?.orders[0]?.product?.id,
            expiryDate: creditHistory.expiryDate,
          };
        });
      }
    }
    for (const invoice of data) {
      if (invoice.orders.length > 0) {
        invoice.orders.forEach((order) => {
          if (order.payload) {
            const orderPayload: any = order.payload;
            order.items = orderPayload.items;
            order.payload = undefined;
            for (const item of order.items) {
              const foundMembership = memberships.find(
                (membership) =>
                  membership.invoiceId === invoice.id &&
                  membership.orderId === item.id &&
                  membership.productId === item.product.id,
              );
              item.expiryDate = foundMembership
                ? foundMembership.expiryDate
                : null;
            }
          }
        });
      }
      if (invoice.parentInvoice) {
        invoice.parentInvoice = await this.repo.manager.findOne(Invoice, {
          where: { id: invoice.parentInvoice?.id },
        });
      }
    }
    return this.createPageInfo<Invoice>(
      data,
      total,
      qLimit || total,
      offset || 0,
    );
  }

  async getMembershipSummary(
    customerId: string,
    { creditType }: MembershipSummaryQueryDto,
    crudReq: CrudRequest,
  ): Promise<any> {
    const queryBuilder = this.creditRepo
      .createQueryBuilder('credit')
      .leftJoin('credit.customer', 'customer')
      .leftJoinAndSelect('credit.creditSetting', 'creditSetting')
      .where('creditSetting.creditType = :creditType', {
        creditType: creditType ? creditType : CreditType.NEW,
      })
      .andWhere('customer.id = :customerId', { customerId });
    const credits = await queryBuilder.getMany();
    if (credits.length) {
      for (const credit of credits) {
        const history = await this.creditHistoryRepo
          .createQueryBuilder('creditHistory')
          .leftJoin('creditHistory.credit', 'credit')
          .where('credit.id = :creditId', {
            creditId: credit.id,
          })
          .andWhere('creditHistory.isRefund = :isRefund', { isRefund: false })
          .andWhere('creditHistory.expiryDate IS NOT NULL')
          .orderBy('creditHistory.expiryDate', 'DESC')
          .getOne();
        if (history) {
          const detail = history.detail as any;
          const period = detail.orders[0]?.product?.period;
          const periodUnit = detail.orders[0]?.product?.periodUnit;
          credit['validityPeriod'] = {
            period,
            periodUnit: period > 1 ? `${periodUnit}s` : periodUnit,
          };
          credit['invoiceDate'] = detail?.date || null;
        }
        credit['issueDate'] = credit.updated;
      }
    }

    return credits;
  }

  async checkCustomerOrdersAvailable(customer: Customer): Promise<boolean> {
    try {
      const totalServiceProducts = await this.appointmentRepo
        .createQueryBuilder('appointment')
        .leftJoin('appointment.orders', 'order')
        .leftJoin('order.items', 'orderDetail')
        .leftJoin('orderDetail.product', 'product')
        .where('appointment.customerId = :customerId', {
          customerId: customer.id,
        })
        .andWhere('appointment.type = :appointmentType', {
          appointmentType: AppointmentType.APPOINTMENT,
        })
        .andWhere('DATE(orderDetail.created) = CURRENT_DATE')
        .andWhere('product.type = :type', { type: ProductType.SERVICE })
        .select('SUM(orderDetail.quantity)', 'total')
        .getRawOne();

      const previousCheckIns = await this.appointmentRepo
        .createQueryBuilder('appointment')
        .where('appointment.customerId = :customerId', {
          customerId: customer.id,
        })
        .andWhere('appointment.checkIn IS NOT NULL')
        .andWhere('DATE(appointment.checkIn) = CURRENT_DATE')
        .getCount();

      const remainingServices =
        parseInt(totalServiceProducts.total || '0', 10) - previousCheckIns;

      return remainingServices > 0;
    } catch (error) {
      this.filterWriteException(error);
      return false;
    }
  }

  async checkIn({
    customer,
    rfid,
    appointment,
    isContinue,
  }: checkInCustomerDto) {
    const appointmentId = appointment.id;
    const rfidExists = await this.repo.manager.findOne(Rfid, {
      where: {
        serialCode: rfid,
      },
      relations: ['branch'],
    });

    if (!rfidExists) {
      throw new NotFoundException('RFID not found');
    }

    if (rfidExists.token) {
      throw new BadRequestException('RFID is already checked in');
    }
    // find appointment of customer
    const condition = appointmentId
      ? { id: appointmentId }
      : { customer: { id: customer.id } };

    //check customer is checking
    const isCheckin = await this.repo.manager.findOne(Rfid, {
      where: {
        appointment: { customer: { id: customer.id } },
      },
      relations: ['appointment', 'appointment.customer'],
    });
    // if (isCheckin) {
    //   throw new BadRequestException(
    //     'This customer has checked in with other RFID',
    //   );
    // }
    let appointmentExists;
    if (!isCheckin) {
      appointmentExists = (await this.appointmentService.findOne({
        where: {
          ...condition,
          checkOut: IsNull(),
          checkIn: IsNull(),
          startTime: Between(
            moment().startOf('day').toDate(),
            moment().endOf('day').toDate(),
          ),
        },
        relations: ['orders', 'orders.items', 'orders.items.employees'],
      })) || {
        startTime: moment().toDate(),
        customer,
        branch: rfidExists.branch,
        status: AppointmentStatus.ARRIVED,
      };
    }

    if (isCheckin) {
      const isCustomerOrdersAvailable = await this.checkCustomerOrdersAvailable(
        customer,
      );

      if (!isCustomerOrdersAvailable && !isContinue) {
        throw new UnprocessableEntityException(
          'Purchased service has been used up',
        );
      }

      appointmentExists = appointmentId
        ? await this.appointmentService.findOne({
            where: {
              id: appointmentId,
              checkOut: IsNull(),
              checkIn: IsNull(),
              startTime: Between(
                moment().startOf('day').toDate(),
                moment().endOf('day').toDate(),
              ),
            },
            relations: ['orders', 'orders.items', 'orders.items.employees'],
          })
        : {
            startTime: moment().toDate(),
            customer,
            branch: rfidExists.branch,
            status: AppointmentStatus.ARRIVED,
          };
    }

    const credits = await this.creditRepo.find({
      where: {
        customer: { id: customer.id },
      },
    });
    let isMember = false;
    for (const c of credits) {
      if (c.creditBalance > 0) {
        isMember = true;
        break;
      }
    }
    const token = jwt.sign(
      {
        isMember,
        customerId: customer.id,
        rfid,
      },
      this.secretKey,
      { expiresIn: '1d' },
    );

    try {
      await this.repo.manager.transaction(async (trans) => {
        const orders = appointmentExists?.orders ?? [];
        if (orders.length) {
          appointmentExists.status = AppointmentStatus.ARRIVED;
          for (const order of orders) {
            const items = order.items;
            for (const item of items) {
              if (item.employees.length) {
                item.status = AppointmentStatus.REQUEST_ARRIVED;
                appointmentExists.status = AppointmentStatus.REQUEST_ARRIVED;
                // break;
              } else {
                item.status = AppointmentStatus.ARRIVED;
              }
              // update order item status
              await trans.update(
                OrderDetail,
                { id: item.id },
                { status: item.status },
              );
            }
          }
        }

        const appointment = await trans.save(Appointment, {
          ...appointmentExists,
          checkIn: moment().toDate(),
          rfid,
          rfids: rfidExists,
        });
        await trans.update(
          Rfid,
          {
            serialCode: rfid,
          },
          {
            appointment: { id: appointment?.id },
            token,
          },
        );
      });
    } catch (error) {
      this.filterWriteException(error);
    }
    return { token };
  }

  async checkOut(rfid: string) {
    const rfidExists = await this.repo.manager.findOne(Rfid, {
      where: {
        serialCode: rfid,
      },
      relations: [
        'appointment',
        'appointment.orders',
        'appointment.orders.invoice',
        'appointment.customer',
      ],
    });

    if (!rfidExists) {
      throw new NotFoundException('RFID not found');
    }

    if (!rfidExists.appointment) {
      throw new BadRequestException('RFID is already checked out');
    }

    if (
      rfidExists.appointment?.orders.length &&
      rfidExists.appointment?.orders?.some(
        (order) => !order.invoice && order.status !== InvoiceStatus.VOID,
      )
    ) {
      throw new BadRequestException(
        'Please complete payment before checking out',
      );
    }

    const passportExists = await this.repo.manager.findOne(CreditHistory, {
      where: {
        customer: {
          id: rfidExists.appointment.customer.id,
        },
        isPassportHistory: true,
        product: Not(IsNull()),
      },
      order: {
        created: 'DESC',
      },
    });

    await this.repo.manager.transaction(async (trans) => {
      await trans.update(
        Appointment,
        { id: rfidExists.appointment.id },
        {
          checkOut: moment().toDate(),
        },
      );
      await trans.update(
        Rfid,
        {
          serialCode: rfid,
        },
        {
          appointment: null,
          token: null,
        },
      );
      if (
        rfidExists.appointment.customer.passportUsageLimit > 0 &&
        rfidExists.appointment.customer.expiryDate &&
        moment().isBefore(rfidExists.appointment.customer.expiryDate)
      ) {
        await trans.update(
          Customer,
          { id: rfidExists.appointment.customer.id },
          {
            passportUsageLimit: () => 'passportUsageLimit - 1',
          },
        );

        await trans.insert(CreditHistory, {
          detail: {},
          usable: rfidExists.appointment.customer.passportUsageLimit - 1,
          opening: rfidExists.appointment.customer.passportUsageLimit,
          closing: rfidExists.appointment.customer.passportUsageLimit - 1,
          expiryDate: rfidExists.appointment.customer.expiryDate,
          customer: rfidExists.appointment.customer,
          isPassportHistory: true,
          paid: 1,
          membership: {
            id: passportExists?.id,
          },
        });
      }
    });
    return { success: true };
  }

  async checkInList(branchIds: string[], keySearch: string) {
    const queryAppointment = this.appointmentRepo
      .createQueryBuilder('appointment')
      .leftJoinAndSelect('appointment.customer', 'customer')
      .leftJoinAndSelect('appointment.branch', 'branch')
      .leftJoinAndSelect('appointment.rfids', 'rfid')
      .leftJoinAndSelect('rfid.group', 'group')
      .where(
        'branch.id ' +
          (branchIds && branchIds.length
            ? 'IN (:...branchIds)'
            : 'IS NOT NULL'),
        { branchIds },
      )
      .andWhere('appointment.checkIn IS NOT NULL')
      .andWhere('appointment.checkOut IS NULL');
    if (keySearch) {
      queryAppointment.andWhere(
        "(CONCAT(TRIM(customer.firstName), ' ', TRIM(customer.lastName)) ILIKE :name)",
        { name: `%${keySearch}%` },
      );
    }
    queryAppointment.select([
      'customer.firstName',
      'customer.lastName',
      'customer.code',
      'customer.phone',
      'customer.id',
      'rfid.lockerNumber',
      'group.name as groupName',
    ]);

    const appointmentCustomers = await queryAppointment.getRawMany();

    return appointmentCustomers.map((item) => ({
      id: item.customer_id,
      lastName: item.customer_lastName,
      firstName: item.customer_firstName,
      code: item.customer_code,
      phone: item.customer_phone,
      lockerNumber: item.rfid_lockerNumber,
      type: item.groupname,
    }));
  }

  async checkDuplicateNric(nric: string, customerId?: string) {
    if (!customerId) {
      const nricExists = await this.repo.manager.findOne(Customer, {
        where: {
          nric,
        },
      });
      if (nricExists) {
        throw new DuplicatedEntryException('nric');
      }
    } else {
      const customer = await this.repo.manager.findOne(Customer, {
        where: {
          id: customerId,
        },
      });
      if (customer) {
        if (customer.nric !== nric) {
          const nricExists = await this.repo.manager.findOne(Customer, {
            where: {
              nric,
              id: Not(customerId),
            },
          });
          if (nricExists) {
            throw new DuplicatedEntryException('nric');
          }
        }
      }
    }
  }

  async deleteOneCustomer(
    crudRequest: CrudRequest,
    req,
    customerId: string,
  ): Promise<any> {
    const targetCustomer = await this.repo.findOne({
      where: {
        id: customerId,
      },
    });

    if (!targetCustomer) {
      throw new BadRequestException(
        waMessage.exception.customerNotFound.message,
      );
    }

    const appointment = await this.repo.manager.findOne(Appointment, {
      where: {
        customer: {
          id: targetCustomer.id,
        },
      },
    });

    if (appointment) {
      throw new BadRequestException('Customer cannot delete');
    }

    const credit = await this.repo.manager.findOne(Credit, {
      where: {
        customer: {
          id: targetCustomer.id,
        },
      },
    });

    if (credit) {
      throw new BadRequestException('Customer cannot delete');
    }

    const invoice = await this.repo.manager.findOne(Invoice, {
      where: {
        customer: {
          id: targetCustomer.id,
        },
      },
    });

    if (invoice) {
      throw new BadRequestException('Customer cannot delete');
    }

    return await this.repo.softDelete({
      id: customerId,
    });
  }
}
