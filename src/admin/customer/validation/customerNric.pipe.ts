import { ArgumentMetadata, Injectable, PipeTransform } from '@nestjs/common';
import { CustomerService } from '../customer.service';
import { Customer } from '../customer.entity';

@Injectable()
export class CustomerNricPipe implements PipeTransform {
  constructor(private readonly service: CustomerService) {}

  async transform(value: Partial<Customer>, { metatype }: ArgumentMetadata) {
    if (!metatype || !CustomerNricPipe.toValidate(metatype) || !value.nric) {
      return value;
    }

    await this.service.checkDuplicateNric(value.nric, value?.id);

    return value;
  }

  private static toValidate(metatype: Function): boolean {
    const types: Function[] = [String, Boolean, Number, Array, Object];
    return !types.includes(metatype);
  }
}
