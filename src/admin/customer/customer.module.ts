import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { CustomerService } from './customer.service';
import { CustomerController } from './customer.controller';
import { Customer } from './customer.entity';
import { CategoryModule } from '../category/category.module';
import { AppointmentModule } from '../appointment/appointment.module';
import { RfidModule } from '../rfid/rfid.module';
import { Credit } from '../credit/credit.entity';
import { Appointment } from '../appointment/appointment.entity';
import { ProductModule } from '../product/product.module';
import { IssueCouponModule } from '../inventory/issue-coupon.module';
import { CreditHistory } from '../credit-history/credit-history.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Customer, Credit, Appointment, CreditHistory]),
    CategoryModule,
    AppointmentModule,
    RfidModule,
    ProductModule,
    IssueCouponModule,
  ],
  controllers: [CustomerController],
  providers: [CustomerService],
  exports: [CustomerService],
})
export class CustomerModule {}
