import { OmitType } from '@nestjs/swagger';
import { IsDateString, IsEmail, IsNotEmpty, IsOptional } from 'class-validator';
import { Customer } from '../customer.entity';

export class CreateCustomerDto extends OmitType(Customer, [] as const) {
  @IsOptional()
  nric: string;

  @IsOptional()
  membershipNo?: string;

  @IsOptional()
  @IsEmail()
  email: string;

  @IsOptional()
  @IsDateString()
  birthDay: Date;

  @IsOptional()
  @IsDateString()
  expiryDate?: Date;

  @IsOptional()
  @IsDateString()
  firstVisit?: Date;

  @IsNotEmpty()
  firstName: string;

  @IsNotEmpty()
  lastName: string;

  @IsNotEmpty()
  phone: string;
}
