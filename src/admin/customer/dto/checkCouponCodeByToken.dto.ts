import { ApiProperty, PickType } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { CouponItem } from 'src/admin/inventory/coupon-item.entity';

export class CheckCouponCodeByTokenDto extends PickType(CouponItem, ['id']) {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  code: string;

  @ApiProperty()
  @IsNotEmpty()
  items: object[];

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  token: string;
}
