import { OmitType } from '@nestjs/swagger';
import { IsDateString, IsEmail, IsOptional, IsString } from 'class-validator';
import { Customer } from '../customer.entity';

export class UpdateCustomerDto extends OmitType(Customer, [] as const) {
  @IsOptional()
  nric: string;

  @IsOptional()
  membershipNo?: string;

  @IsOptional()
  @IsEmail()
  email: string;

  @IsOptional()
  @IsDateString()
  birthDay: Date;

  @IsOptional()
  @IsDateString()
  expiryDate?: Date;

  @IsOptional()
  @IsDateString()
  firstVisit?: Date;

  @IsOptional()
  @IsString()
  firstName: string;

  @IsOptional()
  @IsString()
  lastName: string;

  @IsOptional()
  @IsString()
  phone?: string;
}
