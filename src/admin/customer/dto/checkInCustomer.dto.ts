import {
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Customer } from '../customer.entity';
import { Rfid } from 'src/admin/rfid/rfid.entity';
import { Appointment } from 'src/admin/appointment/appointment.entity';

export class checkInCustomerDto {
  @IsObject()
  @ValidateNested()
  customer: Customer;

  @IsString()
  rfid: string;

  @IsOptional()
  @ValidateNested()
  appointment?: Appointment;

  @IsOptional()
  isContinue: boolean;
}
