import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDateString, IsEnum, IsInt, IsOptional, IsString, Max, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { CreditType } from '../../../core/enums/entity';

export class AppointmentSummaryQueryDto {
  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  page: number;

  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  @Max(1000)
  @Min(1)
  limit: number;

  @ApiPropertyOptional()
  @IsDateString()
  @IsOptional()
  startDate?: Date;

  @ApiPropertyOptional()
  @IsDateString()
  @IsOptional()
  endDate?: Date;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keySearch?: string;
}

export class InvoiceSummaryQueryDto {
  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  page: number;

  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  @Max(1000)
  @Min(1)
  limit: number;

  @ApiPropertyOptional()
  @IsDateString()
  @IsOptional()
  startDate?: Date;

  @ApiPropertyOptional()
  @IsDateString()
  @IsOptional()
  endDate?: Date;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keySearch?: string;
}

export class MembershipSummaryQueryDto {
  @ApiPropertyOptional()
  @IsDateString()
  @IsOptional()
  startDate?: Date;

  @ApiPropertyOptional()
  @IsDateString()
  @IsOptional()
  endDate?: Date;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keySearch?: string;

  @ApiPropertyOptional({
    type: 'enum',
    enum: CreditType,
  })
  @IsEnum(CreditType)
  @IsOptional()
  creditType?: string
}