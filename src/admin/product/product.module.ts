import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { Product } from './product.entity';
import { ProductService } from './product.service';
import { ProductController } from './product.controller';
import { ProductAssignment } from './product-assignment.entity';
import { ProductAssignmentService } from './product-assignment.service';
import { ServiceModule } from './service/service.module';
import { MembershipModule } from './membership/membership.module';
import { FoodModule } from './food/food.module';
import { CouponModule } from './coupon/coupon.module';
import { CouponService } from './coupon/coupon.service';
import { IssueCoupon } from '../inventory/issue-coupon.entity';
import { Rfid } from '../rfid/rfid.entity';
import { CouponItem } from '../inventory/coupon-item.entity';
import { BeverageModule } from './beverage/beverage.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Product,
      ProductAssignment,
      CouponItem,
      IssueCoupon,
      Rfid,
    ]),
    ServiceModule,
    MembershipModule,
    FoodModule,
    BeverageModule,
    CouponModule,
  ],
  controllers: [ProductController],
  providers: [ProductService, ProductAssignmentService, CouponService],
  exports: [ProductService, ProductAssignmentService, CouponService],
})
export class ProductModule {}
