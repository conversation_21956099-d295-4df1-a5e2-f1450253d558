import * as moment from 'moment';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { Decimal } from 'decimal.js';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import {
  CouponType,
  CreditType,
  PeriodUnit,
  RecordStatus,
} from 'src/core/enums/entity';
import { Product } from '../product.entity';
import { CouponItem } from 'src/admin/inventory/coupon-item.entity';
import { IssueCoupon } from 'src/admin/inventory/issue-coupon.entity';
import { Rfid } from 'src/admin/rfid/rfid.entity';
import { Order } from 'src/admin/order/order.entity';
import { Credit } from '../../credit/credit.entity';
import { countExpiryDate } from 'src/core/common/common.utils';
import { Setting } from '../../setting/setting.entity';

@Injectable()
export class CouponService extends BaseCrudService<Product> {
  constructor(
    @InjectRepository(Product) repo: Repository<Product>,
    @InjectRepository(CouponItem)
    private couponItemRepo: Repository<CouponItem>,
    @InjectRepository(IssueCoupon)
    private issueCouponRepo: Repository<IssueCoupon>,
    @InjectRepository(Rfid)
    private rfidRepo: Repository<Rfid>,
  ) {
    super(repo);
  }

  async validateCouponCode(code: string, customerEmail?: string) {
    const couponItem = await this.couponItemRepo.findOne({
      where: { code },
      relations: [
        'issueCoupon',
        'issueCoupon.coupon',
        'issueCoupon.coupon.discountType',
        'issueCoupon.status',
        'issueCoupon.coupon.status',
      ],
    });
    if (!couponItem || !couponItem.issueCoupon) {
      throw new BadRequestException('The coupon code is invalid!');
    }
    if (
      customerEmail &&
      couponItem.email &&
      couponItem.email !== customerEmail
    ) {
      throw new BadRequestException(
        'This coupon was not purchased by this customer',
      );
    }
    if (couponItem.expiryDate && new Date(couponItem.expiryDate) < new Date()) {
      throw new BadRequestException('Coupon is expired');
    }
    if (couponItem.isUsed) {
      throw new BadRequestException('Coupon has already been used');
    }
    const issueCoupon = await this.issueCouponRepo.findOne({
      where: { id: couponItem.issueCoupon.id },
    });
    if (!issueCoupon) {
      throw new BadRequestException('The coupon code is invalid!');
    }
    if (issueCoupon.remain <= 0) {
      throw new BadRequestException('This coupon is out of stock');
    }
    if (
      couponItem.issueCoupon?.status?.name !== RecordStatus.ACTIVE ||
      couponItem.issueCoupon?.coupon?.status?.name !== RecordStatus.ACTIVE
    ) {
      throw new BadRequestException('This coupon is no longer available');
    }
    return couponItem;
  }

  async checkCouponCode(branchIds: string[], data: any) {
    if (data?.token) {
      const rfid = await this.rfidRepo.findOne({
        where: { token: data.token },
      });
      if (!rfid) {
        throw new NotFoundException('RFID token is invalid!');
      }
    }

    let result = [];
    let remainingTotal = new Decimal(0);

    const customerEmail = data?.customerEmail;

    const couponCodeCount: Record<string, number> = {};
    if (data.coupons && data.coupons.length) {
      for (const coupon of data.coupons) {
        if (coupon.type === CouponType.CODE) {
          couponCodeCount[coupon.code] =
            (couponCodeCount[coupon.code] || 0) + 1;
        }
      }
    }

    // Only validate coupon codes, don't mark them as used yet
    for (const code in couponCodeCount) {
      await this.validateCouponCode(code, customerEmail);
    }

    for (const order of data.orders) {
      const findOrder = await this.repo.manager.findOne(Order, {
        where: { id: order.id },
        relations: [
          'items',
          'items.product',
          'appointment',
          'appointment.customer',
          'invoice',
          'invoice.invoicePayments',
          'invoice.invoicePayments.paymentMethod',
        ],
      });

      if (findOrder && findOrder.items.length) {
        const orderPayload: any = findOrder.payload;
        for (const productItem of orderPayload.items) {
          const priceItem = !productItem?.couponCode
            ? new Decimal(productItem.quantity).times(
                new Decimal(productItem?.product?.price),
              )
            : new Decimal(0);
          if (!productItem.product.isNotApplyDiscount) {
            remainingTotal = remainingTotal.plus(priceItem);
          }
        }
      }
    }

    if (data.coupons && data.coupons.length) {
      const uniqueDataCoupons = data.coupons.filter((coupon, index, self) => {
        if (coupon.type !== CouponType.CODE) {
          return true;
        } else {
          return (
            self.findIndex(
              (c) => c.type === CouponType.CODE && c.code === coupon.code,
            ) === index
          );
        }
      });

      const codeCoupons = uniqueDataCoupons.filter(
        (c) => c.type === CouponType.CODE,
      );
      const percentCoupons = uniqueDataCoupons.filter(
        (c) => c.type === CouponType.PERCENT,
      );
      const moneyCoupons = uniqueDataCoupons.filter(
        (c) => c.type === CouponType.MONEY,
      );

      for (const coupon of codeCoupons) {
        const couponItem = await this.validateCouponCode(
          coupon.code,
          customerEmail,
        );
        const discountResult = await this.processDiscountCustom(data.orders, {
          value: couponItem.issueCoupon.coupon.discountValue,
          type: couponItem.issueCoupon.coupon.discountType.name,
        });
        result = result.concat({
          discountMoney: discountResult[0].discountMoney,
          couponCode: couponItem.code,
          couponName: couponItem.issueCoupon?.coupon?.name ?? '',
          couponType: coupon.type,
        });
        remainingTotal = remainingTotal.minus(discountResult[0].discountMoney);
        if (remainingTotal.lessThan(0)) {
          remainingTotal = new Decimal(0);
        }

        // Don't mark coupon as used here - this is just validation
        // Actual usage will be handled during invoice processing
      }

      for (const coupon of [...percentCoupons, ...moneyCoupons]) {
        const discountResults = await this.processDiscountCustom(
          data.orders,
          coupon,
          undefined,
          remainingTotal,
        );

        result = result.concat(discountResults);
        remainingTotal = new Decimal(discountResults[0].remainingTotal);

        if (remainingTotal.lessThan(0)) {
          remainingTotal = new Decimal(0);
        }
      }
    }

    if (!result.length) {
      throw new BadRequestException('No coupons are applicable');
    }

    return result;
  }

  async processCouponCode(branchIds: string[], orders: any, couponData: any) {
    const { code, type, customerEmail } = couponData;
    const whereClause: Record<string, any> = { code };
    if (branchIds.length > 0) {
      whereClause.issueCoupon = { branches: {} };
      whereClause.issueCoupon.branches.id = In(branchIds);
    }
    const existCouponCode = await this.couponItemRepo.findOne({
      where: whereClause,
      relations: [
        'issueCoupon',
        'branches',
        'issueCoupon.coupon',
        'issueCoupon.coupon.status',
        'issueCoupon.coupon.discountType',
        'issueCoupon.status',
      ],
    });

    if (
      !existCouponCode ||
      existCouponCode.isUsed ||
      !existCouponCode?.issueCoupon ||
      existCouponCode?.issueCoupon?.status?.name !== RecordStatus.ACTIVE ||
      existCouponCode?.issueCoupon?.coupon?.status?.name !== RecordStatus.ACTIVE
    ) {
      throw new BadRequestException('The coupon code is invalid!');
    }
    if (
      customerEmail &&
      existCouponCode.email &&
      existCouponCode.email !== customerEmail
    ) {
      throw new BadRequestException(
        'This coupon was not purchased by this customer',
      );
    }
    if (
      existCouponCode.expiryDate &&
      new Date(existCouponCode.expiryDate) < new Date()
    ) {
      throw new BadRequestException('Coupon is expired');
    }

    const issueCoupon = existCouponCode.issueCoupon;
    const couponProduct = issueCoupon.coupon;

    await this.couponItemRepo.update(existCouponCode.id, { isUsed: true });
    const couponItems = await this.couponItemRepo.find({
      where: { issueCoupon: { id: issueCoupon.id } },
    });
    const usedCoupons = couponItems.filter((item) => item.isUsed).length;
    const totalCoupons = couponItems.length;
    const remainCoupons = totalCoupons - usedCoupons;
    let updateStatus = {};
    if (remainCoupons === 0) {
      const inactiveStatus = await this.repo.manager.findOne(Setting, {
        where: { name: 'INACTIVE', type: 'STATUS' },
      });
      if (inactiveStatus) {
        updateStatus = {
          status: inactiveStatus,
          remain: 0,
          used: totalCoupons,
        };
      }
    } else {
      updateStatus = {
        remain: remainCoupons,
        used: usedCoupons,
      };
    }
    await this.issueCouponRepo.update(issueCoupon.id, updateStatus);

    // Set expiry date based on purchase date and coupon period
    const purchaseDate = new Date();
    const expiryDate = countExpiryDate(
      purchaseDate,
      couponProduct.period,
      couponProduct.periodUnit,
    );

    // Update coupon item with expiry date
    await this.couponItemRepo.update(existCouponCode.id, {
      startDate: purchaseDate,
      expiryDate: expiryDate,
    });

    const discount = await this.processDiscountCustom(orders, {
      value: existCouponCode.issueCoupon.coupon.discountValue,
      type: existCouponCode.issueCoupon.coupon.discountType.name,
    });

    return {
      discountMoney: discount[0].discountMoney,
      couponCode: existCouponCode.code,
      couponName: issueCoupon?.coupon?.name ?? '',
      couponType: type,
    };
  }

  async processCouponCodeUsed(
    branchIds: string[],
    orders: any,
    coupon: any,
    invoiceDiscountId?: any,
  ) {
    const { code, type } = coupon;
    const whereClause: Record<string, any> = { code };
    if (branchIds.length > 0) {
      whereClause.issueCoupon = { branches: {} };
      whereClause.issueCoupon.branches.id = In(branchIds);
    }
    const existCouponCode = await this.couponItemRepo.findOne({
      where: whereClause,
      relations: [
        'issueCoupon',
        'branches',
        'issueCoupon.coupon',
        'issueCoupon.coupon.status',
        'issueCoupon.coupon.discountType',
        'issueCoupon.status',
      ],
    });
    const issueCoupon = existCouponCode.issueCoupon;
    const discount = await this.processDiscountCustom(orders, {
      value: existCouponCode.issueCoupon.coupon.discountValue,
      type: existCouponCode.issueCoupon.coupon.discountType.name,
    });

    return {
      id: invoiceDiscountId,
      discountMoney: discount[0].discountMoney,
      couponCode: existCouponCode.code,
      couponName: issueCoupon?.coupon?.name ?? '',
      couponType: type,
    };
  }

  async processDiscountCustom(
    orders: any,
    coupon: any,
    invoiceDiscountId?: any,
    currentTotal?: Decimal,
  ) {
    const { value, type } = coupon;

    let sumPrice = currentTotal || new Decimal(0);
    let discountMoney = new Decimal(0);

    if (!currentTotal) {
      for (const order of orders) {
        const findOrder = await this.repo.manager.findOne(Order, {
          where: { id: order.id },
          relations: [
            'items',
            'items.product',
            'appointment',
            'appointment.customer',
            'invoice',
            'invoice.invoicePayments',
            'invoice.invoicePayments.paymentMethod',
          ],
        });

        if (findOrder && findOrder.items.length) {
          const orderPayload: any = findOrder.payload;
          for (const productItem of orderPayload.items) {
            const priceItem = !productItem?.couponCode
              ? new Decimal(productItem.quantity).times(
                  new Decimal(productItem?.product?.price),
                )
              : new Decimal(0);
            if (!productItem.product.isNotApplyDiscount) {
              sumPrice = sumPrice.plus(priceItem);
            }
          }
        }
      }
    }

    if (type === CouponType.MONEY) {
      discountMoney = discountMoney.plus(value);
    } else if (type === CouponType.PERCENT) {
      discountMoney = discountMoney.plus(
        sumPrice.times(new Decimal(value)).dividedBy(100),
      );
    }

    sumPrice = sumPrice.minus(discountMoney);
    if (sumPrice.lessThan(0)) {
      sumPrice = new Decimal(0);
    }

    return [
      {
        id: invoiceDiscountId,
        discountMoney: discountMoney.toFixed(2),
        couponType: type,
        percent: type === CouponType.PERCENT ? value : undefined,
        remainingTotal: sumPrice.toFixed(2),
      },
    ];
  }
}
