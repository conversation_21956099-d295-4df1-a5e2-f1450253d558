import {
  CrudController,
  CrudRequest,
  Override,
  ParsedBody,
  ParsedRequest,
} from 'src/core/crud/crud';

// import { UserService } from './user.service';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { Body, Inject, Param, Post, Req, forwardRef } from '@nestjs/common';
import { BaseCrud } from 'src/admin/auth/decorators/base-crud.decorator';
import { Product } from '../product.entity';
import { ProductService } from '../product.service';
import { ProductType } from 'src/core/enums/entity';
import { CreateProductDto } from '../dto/createProduct.dto';
import { CheckCouponCodeDto } from './dto/checkCouponCode.dto';
import { CouponDto } from './dto/createCoupon.dto';
import { CouponService } from './coupon.service';

@BaseCrud(
  {
    model: {
      type: Product,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    query: {
      filter: {
        type: ProductType.COUPON,
        $or: [{ name: { $contL: '' } }],
      },
      join: {
        category: { eager: true, allow: ['id', 'name'] },
        status: { eager: true, allow: ['id', 'name'] },
        avatar: { eager: true, allow: ['id', 'url'] },
        branches: { eager: true, allow: ['id', 'name'] },
        products: { eager: true, allow: ['id', 'name'] },
        discountType: { eager: true, allow: ['id', 'name'] },
        priceType: { eager: true, allow: ['id', 'name'] },
      },
    },
    dto: {
      // create: CouponDto,
    },
  },
  {
    alias: 'Coupon',
  },
)
export class CouponController extends BaseCrudController<Product> {
  constructor(
    @Inject(forwardRef(() => ProductService))
    private productService: ProductService,
    @Inject(forwardRef(() => CouponService))
    private couponService: CouponService,
  ) {
    super(productService);
  }
  get base(): CrudController<Product> {
    return this;
  }

  @Override('createOneBase')
  async createOne(
    @ParsedBody() dto: CreateProductDto,
    @ParsedRequest() crudRequest: CrudRequest,
  ): Promise<any> {
    return this.productService.createOne(crudRequest, {
      ...dto,
      type: ProductType.COUPON,
    });
  }

  @Post('check-code')
  async checkCouponCode(
    @Body() data: CheckCouponCodeDto,
    @Req() req: Request,
  ): Promise<any> {
    const branchIds = req.headers?.['branchid']?.split(',') || [];
    return await this.couponService.checkCouponCode(branchIds, data);
  }

  @Override('deleteOneBase')
  async deleteOneProduct(
    @ParsedRequest() crudRequest: CrudRequest,
    @Param('id') id: string,
  ): Promise<void | Product> {
    return this.productService.deleteOneProduct(id);
  }
}
