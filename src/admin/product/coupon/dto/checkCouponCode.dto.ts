import { ApiProperty, PickType } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { CouponItem } from 'src/admin/inventory/coupon-item.entity';
import { Type } from 'class-transformer';
import { CouponType } from 'src/core/enums/entity';

export class CouponDTO {
  @ApiProperty({
    enum: () => CouponType,
  })
  @IsEnum(CouponType)
  @IsNotEmpty()
  type: CouponType;

  @ApiProperty()
  @IsNotEmpty()
  @ValidateIf(
    (object, value) =>
      object.type === CouponType.MONEY || object.type === CouponType.PERCENT,
  )
  value?: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @ValidateIf((object, value) => object.type === CouponType.CODE)
  code: string;
}

export class CheckCouponCodeDto extends PickType(CouponItem, ['id']) {
  @ApiProperty()
  @IsNotEmpty()
  orders: object[];

  @ApiProperty({ type: CouponDTO, isArray: true })
  @IsNotEmpty()
  @Type(() => CouponDTO)
  @IsArray()
  @ValidateNested()
  coupons: CouponDTO[];
}
