import { TypeOrmModule } from '@nestjs/typeorm';
import { Module, forwardRef } from '@nestjs/common';
import { Product } from '../product.entity';
import { ProductModule } from '../product.module';
import { CouponService } from './coupon.service';
import { CouponController } from './coupon.controller';
import { CouponItem } from 'src/admin/inventory/coupon-item.entity';
import { IssueCoupon } from 'src/admin/inventory/issue-coupon.entity';
import { Rfid } from 'src/admin/rfid/rfid.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Product, CouponItem, IssueCoupon, Rfid]),
    forwardRef(() => ProductModule),
  ],
  controllers: [CouponController],
  providers: [CouponService],
  exports: [CouponService],
})
export class CouponModule {}
