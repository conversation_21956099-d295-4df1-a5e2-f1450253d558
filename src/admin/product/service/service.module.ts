import { TypeOrmModule } from '@nestjs/typeorm';
import { Module, forwardRef } from '@nestjs/common';
import { Product } from '../product.entity';
import { ServiceController } from './service.controller';
import { ProductModule } from '../product.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Product]),
    forwardRef(() => ProductModule),
  ],
  controllers: [ServiceController],
})
export class ServiceModule {}
