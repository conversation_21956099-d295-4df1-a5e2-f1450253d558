import {
  Crud<PERSON><PERSON>roller,
  CrudRequest,
  Override,
  ParsedBody,
  ParsedRequest,
} from 'src/core/crud/crud';

// import { UserService } from './user.service';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { Inject, Param, Req, forwardRef } from '@nestjs/common';
import { BaseCrud } from 'src/admin/auth/decorators/base-crud.decorator';
import { Product } from '../product.entity';
import { ProductService } from '../product.service';
import { ProductType } from 'src/core/enums/entity';
import { CreateProductDto } from '../dto/createProduct.dto';

@BaseCrud(
  {
    model: {
      type: Product,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    query: {
      filter: { type: ProductType.MEMBERSHIP, $or: [{ name: { $contL: '' } }] },
      join: {
        category: { eager: true, allow: ['id', 'name'] },
        status: { eager: true, allow: ['id', 'name'] },
        avatar: { eager: true, allow: ['id', 'url'] },
        branches: { eager: true, allow: ['id', 'name'] },
      },
    },
    dto: {
      // create: CreateProductDto,
      // update: UpdateProductDto,
    },
  },
  {
    alias: 'Membership',
  },
)
export class MembershipController extends BaseCrudController<Product> {
  constructor(
    @Inject(forwardRef(() => ProductService))
    private productService: ProductService,
  ) {
    super(productService);
  }
  get base(): CrudController<Product> {
    return this;
  }

  @Override('createOneBase')
  async createOne(
    @ParsedBody() dto: CreateProductDto,
    @ParsedRequest() crudRequest: CrudRequest,
  ): Promise<any> {
    return this.productService.createOne(crudRequest, {
      ...dto,
      type: ProductType.MEMBERSHIP,
    });
  }

  @Override('deleteOneBase')
  async deleteOneProduct(
    @ParsedRequest() crudRequest: CrudRequest,
    @Param('id') id: string,
  ): Promise<void | Product> {
    return this.productService.deleteOneProduct(id);
  }
}
