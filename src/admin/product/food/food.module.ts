import { TypeOrmModule } from '@nestjs/typeorm';
import { Module, forwardRef } from '@nestjs/common';
import { Product } from '../product.entity';
import { FoodController } from './food.controller';
import { ProductModule } from '../product.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Product]),
    forwardRef(() => ProductModule),
  ],
  controllers: [FoodController],
})
export class FoodModule {}
