import { Entity, Column, ManyToOne } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Customer } from '../customer/customer.entity';
import { Product } from './product.entity';

// NOTE: NEED TO use SAME FIELDS of Product entity to keep what customer assigned not change overtime when Product have deleted or updated

@Entity('product_assignment')
export class ProductAssignment extends DocEntity {
  @Column()
  @ApiProperty()
  name: string;

  @Column({ type: 'real' })
  @ApiProperty()
  cost: number;

  @Column({ type: 'real' })
  @ApiProperty()
  price: number;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  avatar?: string;

  @ManyToOne(() => Product, { onDelete: 'SET NULL' })
  @ApiPropertyOptional({ type: () => Product })
  refProduct?: Product; // NEED this to keep tracking for 'category' vs 'branches'. If ref product was deleted then message 'unknown info'

  @ManyToOne(() => Customer)
  @ApiProperty()
  customer: Customer;
}
