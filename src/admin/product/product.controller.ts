import {
  Crud<PERSON>ontroller,
  CrudRequest,
  Override,
  ParsedRequest,
} from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { Product } from './product.entity';
import { ProductService } from './product.service';
import { CreateProductDto } from './dto/createProduct.dto';
import { UpdateProductDto } from './dto/updateProduct.dto';
import { Param } from '@nestjs/common';
import { PriceType, ProductType } from 'src/core/enums/entity';

@BaseCrud(
  {
    model: {
      type: Product,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    validation: {},

    query: {
      filter: { type: ProductType.PRODUCT, $or: [{ name: { $contL: '' } }] },
      join: {
        category: { eager: true, allow: ['id', 'name'] },
        status: { eager: true, allow: ['id', 'name'] },
        avatar: { eager: true, allow: ['id', 'url'] },
        branches: { eager: true, allow: ['id', 'name'] },
        priceType: { eager: true, allow: ['id', 'name'] },
      },
    },
    dto: {
      create: CreateProductDto,
      update: UpdateProductDto,
    },
  },
  {},
)
export class ProductController extends BaseCrudController<Product> {
  constructor(public service: ProductService) {
    super(service);
  }

  get base(): CrudController<Product> {
    return this;
  }

  @Override('deleteOneBase')
  async deleteOneProduct(
    @ParsedRequest() crudRequest: CrudRequest,
    @Param('id') id: string,
  ): Promise<void | Product> {
    return this.service.deleteOneProduct(id);
  }
}
