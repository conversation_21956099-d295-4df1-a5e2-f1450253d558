import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { Product } from './product.entity';
import { OrderDetail } from '../order-detail/order-detail.entity';
import { InvoiceStatus } from '../../core/enums/entity';

@Injectable()
export class ProductService extends BaseCrudService<Product> {
  constructor(@InjectRepository(Product) repo: Repository<Product>) {
    super(repo);
  }

  async deleteOneProduct(productId: string) {
    await this.repo.manager.transaction(async (trans) => {
      const orderDetail = await trans.findOne(OrderDetail, {
        where: {
          product: { id: productId },
          order: {
            status: InvoiceStatus.UNPAID,
          },
        },
        relations: ['order'],
      });
      if (orderDetail) {
        throw new BadRequestException('Can not delete product has order!');
      }
      await trans.softDelete(Product, { id: productId });
    });
  }
}
