import { TypeOrmModule } from '@nestjs/typeorm';
import { Module, forwardRef } from '@nestjs/common';
import { Product } from '../product.entity';
import { BeverageController } from './beverage.controller';
import { ProductModule } from '../product.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Product]),
    forwardRef(() => ProductModule),
  ],
  controllers: [BeverageController],
})
export class BeverageModule {}
