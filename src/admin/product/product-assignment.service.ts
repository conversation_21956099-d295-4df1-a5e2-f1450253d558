import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { Product } from './product.entity';
import { ProductAssignment } from './product-assignment.entity';
@Injectable()
export class ProductAssignmentService extends BaseCrudService<ProductAssignment> {
  constructor(@InjectRepository(Product) repo: Repository<ProductAssignment>) {
    super(repo);
  }
}
