import {
  Column,
  <PERSON>tity,
  ManyToOne,
  OneToMany,
  TreeChildren,
  TreeParent,
} from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BaseProduct } from 'src/core/base/base-product.entity';
import { Category } from '../category/category.entity';
import { CreditType, PeriodUnit, PriceType, ProductType } from 'src/core/enums/entity';
import { IsNumber, IsOptional } from 'class-validator';
import { Setting } from '../setting/setting.entity';
import { IssueCoupon } from '../inventory/issue-coupon.entity';
import { CreditHistory } from '../credit-history/credit-history.entity';

@Entity('product')
export class Product extends BaseProduct {
  @Column('enum', {
    enum: ProductType,
    default: ProductType.PRODUCT,
  })
  @ApiProperty()
  type: ProductType;

  @Column({ type: 'boolean', default: false })
  isPassport: boolean;

  @ManyToOne(() => Category, {
    onDelete: 'CASCADE',
  })
  @ApiProperty({
    type: () => Category,
  })
  category: Category;

  @Column({
    default: PeriodUnit.MONTH,
    type: 'enum',
    enum: Object.values(PeriodUnit),
  })
  @ApiProperty()
  periodUnit: PeriodUnit;

  @Column({
    type: 'real',
    nullable: true,
  })
  @ApiPropertyOptional()
  period?: number;

  @Column({ type: 'real', nullable: true, default: 0 })
  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  credit?: number;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Setting })
  duration: Setting;

  @Column({
    nullable: true,
    default: null,
    type: 'enum',
    enum: Object.values(CreditType),
  })
  @ApiProperty()
  creditType?: CreditType;

  @Column({ type: 'boolean', default: false })
  isMember: boolean;

  @Column({ type: 'boolean', default: false })
  isNotApplyDiscount: boolean;

  //coupon properties
  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  startDate?: Date;

  @TreeChildren()
  @ApiProperty({
    type: () => [Product],
    required: false,
    readOnly: true,
    example: null,
    properties: {
      hide: { default: true },
    },
  })
  products: Product[];

  @TreeParent({ onDelete: 'SET NULL' })
  @ApiProperty({
    type: () => Product,
    example: null,
    properties: {
      hide: { default: true },
    },
  })
  parent: Product;

  @ManyToOne(() => IssueCoupon, {
    onDelete: 'CASCADE',
  })
  @ApiProperty({
    type: () => IssueCoupon,
  })
  issue: IssueCoupon;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Setting })
  discountType?: Setting;

  @Column({ type: 'real', default: 0 })
  @ApiProperty()
  discountValue?: number;

  @OneToMany(() => CreditHistory, (creditHistory) => creditHistory.product)
  creditHistory: CreditHistory[];

  @Column({ type: 'real', nullable: true, default: 0 })
  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  quantity?: number;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Setting })
  priceType?: Setting;
}
