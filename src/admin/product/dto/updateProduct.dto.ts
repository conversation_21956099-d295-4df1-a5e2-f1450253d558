import { ApiPropertyOptional, OmitType } from '@nestjs/swagger';
import { Product } from '../product.entity';
import { Branch } from 'src/admin/branch/branch.entity';
import { BranchRelationDto } from 'src/admin/branch/dto/Branch.dto';
import { Category } from 'src/admin/category/category.entity';

export class UpdateProductDto extends OmitType(Product, [
  'createdUser',
  'lastUpdatedUser',
  'branches',
  'category',
]) {
  @ApiPropertyOptional({
    // not allow create branch when create product then DON'T need such other fields
    type: () => BranchRelationDto,
    isArray: true,
  })
  branches?: Branch[];

  @ApiPropertyOptional({
    type: () => Category,
  })
  category?: Category;
}
