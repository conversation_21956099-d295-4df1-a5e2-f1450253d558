import { Entity, Column, ManyToOne } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Setting } from '../setting/setting.entity';
import { GroupDocEntity } from 'src/core/base/group-doc.entity';
import { IssueCoupon } from './issue-coupon.entity';

@Entity()
export class CouponItem extends GroupDocEntity {
  @Column({
    nullable: false,
  })
  @ApiProperty()
  code: string;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Setting })
  status?: Setting;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  email?: string;

  @ManyToOne(() => IssueCoupon, {
    onDelete: 'CASCADE',
  })
  @ApiProperty({
    type: () => IssueCoupon,
  })
  issueCoupon: IssueCoupon;

  @Column({ type: 'boolean', default: false })
  isUsed: boolean;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  startDate?: Date;

  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  expiryDate?: Date;
}
