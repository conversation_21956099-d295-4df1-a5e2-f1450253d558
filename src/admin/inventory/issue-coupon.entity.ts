import { <PERSON>tity, Column, ManyTo<PERSON>ne, ManyToMany, JoinTable } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Setting } from '../setting/setting.entity';
import { GroupDocEntity } from 'src/core/base/group-doc.entity';
import { Product } from '../product/product.entity';
import { IsNumber, IsOptional, IsString, Min } from 'class-validator';

@Entity()
export class IssueCoupon extends GroupDocEntity {
  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  @ApiPropertyOptional()
  issueDate?: Date;

  @Column({ type: 'integer', nullable: true })
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Min(1)
  issued?: number;

  @Column({ type: 'integer', nullable: true })
  @ApiPropertyOptional()
  remain?: number;

  @Column({ type: 'integer', nullable: true })
  @ApiPropertyOptional()
  used?: number;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  remark?: string;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Setting })
  status?: Setting;

  @ManyToOne(() => Product, {
    cascade: true,
  })
  @ApiProperty({
    type: () => Product,
  })
  coupon: Product;

  @Column({ type: 'integer', nullable: true })
  @ApiPropertyOptional()
  minLength?: number;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  startNo: number;

  @Column({
    nullable: true,
  })
  @ApiProperty()
  prefix?: string;

  @Column({
    nullable: true,
  })
  @ApiProperty()
  suffix?: string;
}
