import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { IssueCouponController } from './issue-coupon.controller';
import { IssueCoupon } from './issue-coupon.entity';
import { IssueCouponService } from './issue-coupon.service';
import { CouponItem } from './coupon-item.entity';

@Module({
  imports: [TypeOrmModule.forFeature([IssueCoupon, CouponItem])],
  controllers: [IssueCouponController],
  providers: [IssueCouponService],
  exports: [IssueCouponService],
})
export class IssueCouponModule {}
