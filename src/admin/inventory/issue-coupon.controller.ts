import {
  Crud<PERSON><PERSON>roller,
  CrudRequest,
  Override,
  ParsedBody,
  ParsedRequest,
} from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { IssueCoupon } from './issue-coupon.entity';
import { IssueCouponService } from './issue-coupon.service';
import { CreateIssueCouponDto } from './dto/createIssueCoupon.dto';
import { Body, Get, Param, Post, Query, Req, Res } from '@nestjs/common';
import { SendManyCouponCodeDto } from './dto/sendManyCouponCode.dto';
import { SendOneCouponCodeDto } from './dto/sendOneCouponCode.dto';
import { CouponItemListQueryDto } from './dto/issueCoupon.dto';

@BaseCrud(
  {
    model: {
      type: IssueCoupon,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    query: {
      join: {
        status: { eager: true, allow: ['id', 'name'] },
        coupon: { eager: true, allow: ['id', 'name'] },
        branches: { eager: true, allow: ['id', 'name'] },
      },
      filter: {
        $or: [{ 'coupon.name': { $contL: '' } }],
      },
    },
  },
  {},
)
export class IssueCouponController extends BaseCrudController<IssueCoupon> {
  constructor(public service: IssueCouponService) {
    super(service);
  }
  get base(): CrudController<IssueCoupon> {
    return this;
  }

  @Override('createOneBase')
  createOne(
    @ParsedBody() dto: CreateIssueCouponDto,
    @ParsedRequest() crudRequest: CrudRequest,
  ): Promise<IssueCoupon> {
    return this.service.createOneIssueCoupon(crudRequest, dto);
  }

  @Override('getManyBase')
  async getMany(
    @Query('name') name: string,
    @Req() req: Request,
    @Query() { keySearch }: { keySearch: string },
  ) {
    const branchIds = req.headers?.['branchid']?.split(',') || [];
    return this.service.getIssueCoupon(name, keySearch, branchIds);
  }

  @Override('updateOneBase')
  async updateOne(
    @ParsedRequest() crudRequest: CrudRequest,
    @ParsedBody() dto: CreateIssueCouponDto,
  ) {
    return this.service.UpdateIssueCoupon(crudRequest, dto);
  }

  @Get('/:id/code-list')
  async getCouponItemList(
    @Param('id') issueCouponId: string,
    @Query() query: CouponItemListQueryDto,
  ): Promise<any> {
    return this.service.getCouponItemList(issueCouponId, null, query);
  }

  @Post('/sendManyCouponCode')
  async sendManyCouponCode(
    @Body() dto: SendManyCouponCodeDto,
    @Res() res,
  ): Promise<any> {
    return this.service.sendManyCouponCode(dto, res);
  }

  @Post('/sendOneCouponCode')
  async sendCouponCode(
    @Body() dto: SendOneCouponCodeDto,
    @Res() res,
  ): Promise<any> {
    return this.service.sendOneCouponCode(dto, res);
  }

  @Override('deleteOneBase')
  async deleteOne(
    @ParsedRequest() crudRequest: CrudRequest,
    @Param('id') id: string,
  ) {
    return this.service.deleteOneIssueCoupon(id);
  }
}
