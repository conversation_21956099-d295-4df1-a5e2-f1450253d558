import { ApiProperty, PickType } from '@nestjs/swagger';
import { IssueCoupon } from '../issue-coupon.entity';
import { IsArray, IsNotEmpty, IsUUID } from 'class-validator';
import { UUID } from 'crypto';

export class SendManyCouponCodeDto extends PickType(IssueCoupon, []) {
  @ApiProperty()
  @IsNotEmpty()
  @IsArray()
  emails: string[];

  @ApiProperty()
  @IsNotEmpty()
  @IsUUID()
  issueCouponId: UUID;
}
