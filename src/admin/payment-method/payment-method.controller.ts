import {
  Crud<PERSON>ontroller,
  CrudRequest,
  Override,
  ParsedBody,
  ParsedRequest,
} from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { PaymentMethod } from './payment-method.entity';
import { PaymentMethodService } from './payment-method.service';
import { PaymentMethodDto } from './dto/createPaymentMethod.dto';
import { RecordStatus } from '../../core/enums/entity';

@BaseCrud(
  {
    model: {
      type: PaymentMethod,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    dto: {
      create: PaymentMethodDto,
      update: PaymentMethodDto,
    },
    query: {
      join: {
        role: { eager: true, allow: ['id', 'name'] },
        restrictions: { eager: true, allow: ['id', 'name'] },
        status: { eager: true, allow: ['id', 'name'] },
        consumptionPeriod: { eager: true, allow: ['id', 'name'] },
      },
      filter: {
        $or: [{ name: { $cont: '' } }, { code: { $cont: '' } }],
      },
      sort: [
        {
          field: 'order',
          order: 'ASC',
        },
      ],
    },
  },
  {
    // grantPerm: userPerm,
    // group: ResourceGroup.SYSTEM,
  },
)
export class PaymentMethodController extends BaseCrudController<PaymentMethod> {
  constructor(public service: PaymentMethodService) {
    super(service);
  }
  get base(): CrudController<PaymentMethod> {
    return this;
  }
  @Override('createOneBase')
  createOne(
    @ParsedBody() dto: PaymentMethodDto,
    @ParsedRequest() crudRequest: CrudRequest,
  ): Promise<PaymentMethod> {
    return this.service.createOnePaymentMethod(crudRequest, dto);
  }
}
