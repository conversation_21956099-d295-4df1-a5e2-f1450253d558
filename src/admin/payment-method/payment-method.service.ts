import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import { PaymentMethod } from './payment-method.entity';
import { CrudRequest } from 'src/core/crud/crud';
import * as _ from 'lodash';
@Injectable()
export class PaymentMethodService extends BaseCrudService<PaymentMethod> {
  constructor(
    @InjectRepository(PaymentMethod) repo: Repository<PaymentMethod>,
  ) {
    super(repo);
  }

  async createOnePaymentMethod(
    crudRequest: CrudRequest,
    dto: any,
  ): Promise<any> {
    dto.code = _.snakeCase(dto.name);
    const paymentMethod = await super.createOne(crudRequest, {
      ...dto,
      type: dto.type,
    });
    return paymentMethod;
  }

  async updateOne(req: CrudRequest, dto: any): Promise<PaymentMethod> {
    return super.updateOne(req, {
      ...dto,
      type: dto.type,
    });
  }
}
