import { Entity, Column, ManyToOne } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Setting } from '../setting/setting.entity';

export enum PaymentMethodType {
  CASH = 'cash',
  CARD = 'card',
  FOC = 'foc',
  OTHERS = 'others',
}

@Entity('payment_method')
export class PaymentMethod extends DocEntity {
  @Column({
    nullable: false,
  })
  @ApiProperty()
  name: string;

  @Column({
    nullable: true,
  })
  @ApiPropertyOptional()
  code?: string;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Setting })
  status?: Setting;

  @Column({
    nullable: false,
    default: 0,
  })
  @ApiProperty()
  order: number;

  @Column({ type: 'boolean', default: false })
  isOptionBillCode: boolean;

  @Column({
    type: 'enum',
    enum: PaymentMethodType,
    default: PaymentMethodType.OTHERS,
  })
  @ApiProperty({ enum: PaymentMethodType })
  type: PaymentMethodType;
}
