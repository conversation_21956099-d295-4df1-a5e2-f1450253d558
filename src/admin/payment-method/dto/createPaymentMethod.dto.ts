import { ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { PaymentMethod } from '../payment-method.entity';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { PaymentMethodType } from '../payment-method.entity';

export class PaymentMethodDto extends PickType(PaymentMethod, ['id']) {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ enum: PaymentMethodType })
  @IsOptional()
  @IsEnum(PaymentMethodType)
  type?: PaymentMethodType;
}
