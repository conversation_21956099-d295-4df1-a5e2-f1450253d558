import {
  <PERSON>rud<PERSON>ontroller,
  CrudRequest,
  Override,
  ParsedRequest,
} from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { Credit } from './credit.entity';
import { CreditService } from './credit.service';
import { CreditDto } from './dto/createCredit.dto';
import {
  Body,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  Request,
} from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { UUID } from 'crypto';
import { UpdateResult } from 'typeorm';
import { UpdateCreditDto } from './dto/updateCredit.dto';

@BaseCrud(
  {
    model: {
      type: Credit,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    dto: {
      create: CreditDto,
    },
    query: {
      sort: [
        { field: 'status.name', order: 'ASC' },
        { field: 'customer.firstName', order: 'ASC' },
        { field: 'customer.lastName', order: 'ASC' },
      ],
      filter: {
        $and: [
          {
            total: { $gt: 1 },
          },
          {
            $or: [
              { 'customer.firstName': { $contL: '' } },
              { 'customer.lastName': { $contL: '' } },
              { 'customer.phone': { $contL: '' } },
            ],
          },
        ],
      },
      join: {
        role: { eager: true, allow: ['id', 'name'] },
        restrictions: { eager: true, allow: ['id', 'name'] },
        status: { eager: true, allow: ['id', 'name'] },
        consumptionPeriod: { eager: true, allow: ['id', 'name'] },
        customer: {
          eager: true,
          allow: ['id', 'firstName', 'lastName', 'phone'],
        },
        creditSetting: {
          eager: true,
          allow: ['id', 'name', 'credit', 'price', 'creditType'],
        },
      },
    },
  },
  {
    // grantPerm: userPerm,
    // group: ResourceGroup.SYSTEM,
  },
)
export class CreditController extends BaseCrudController<Credit> {
  constructor(public service: CreditService) {
    super(service);
  }

  get base(): CrudController<Credit> {
    return this;
  }

  @Patch(':id')
  @Override('updateOneBase')
  async update(
    @Param() params: { id: string },
    @Body() body: UpdateCreditDto,
  ): Promise<UpdateResult> {
    const res = await this.service.update(params.id, body);
    return res;
  }

  @Get('/history')
  getCreditHistory(
    @ParsedRequest() crudRequest: CrudRequest,
    @Query()
    query: { customerId: string; creditType: string },
  ) {
    return this.service.getCreditHistory(query.customerId, query.creditType);
  }

  @Get('/membership/refund')
  getCreditMembershipRefund(
    @ParsedRequest() crudRequest: CrudRequest,
    @Query()
    query: { customerId: string },
  ) {
    return this.service.getCreditMembershipRefund(query.customerId);
  }

  @Get('/membership/refund/purged')
  getCreditMembershipRefundPurged(
    @ParsedRequest() crudRequest: CrudRequest,
    @Query()
    query: {
      customerId: string;
    },
  ) {
    return this.service.getCreditMembershipRefundPurged(query.customerId);
  }

  @Get('/membership/refund/history/:id')
  getCreditMembershipRefundHistory(@Param('id') id: UUID) {
    return this.service.getCreditMembershipRefundHistory(id);
  }

  @Post('/membership/refund/:id')
  creditMembershipRefund(
    @Req() req: Request,
    @Body() dto: any,
    @Param('id') id: UUID,
  ) {
    return this.service.creditMembershipRefund(id, dto, req);
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  @Get('/membership/update-expired')
  async scheduleCheckMembershipExpired(): Promise<any> {
    return this.service.scheduleCheckMembershipExpired();
  }
}
