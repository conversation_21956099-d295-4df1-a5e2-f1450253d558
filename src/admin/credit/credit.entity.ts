import {
  Entity,
  Column,
  ManyToOne,
  ManyToMany,
  JoinTable,
  OneToMany,
} from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Branch } from '../branch/branch.entity';
import { Customer } from '../customer/customer.entity';
import { Setting } from '../setting/setting.entity';
import { CreditSetting } from '../settings/credit/credit-setting.entity';
import { CreditStatus } from 'src/core/enums/entity';
import { CreditHistory } from '../credit-history/credit-history.entity';
@Entity('credit')
export class Credit extends DocEntity {
  @Column({
    nullable: false,
    type: 'timestamptz',
  })
  @ApiProperty()
  issueDate: Date;

  @Column({
    nullable: false,
    type: 'timestamptz',
  })
  @ApiProperty()
  expiryDate: Date;

  @Column({ type: 'real', nullable: true })
  @ApiPropertyOptional()
  total?: number;

  @Column({ type: 'real', nullable: true })
  @ApiPropertyOptional()
  creditBalance?: number;

  @ManyToOne(() => CreditSetting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => CreditSetting })
  creditSetting?: CreditSetting;

  @Column({
    default: CreditStatus.VALID,
    type: 'enum',
    enum: Object.values(CreditStatus),
  })
  @ApiProperty()
  status: CreditStatus;

  @ManyToOne(() => Branch, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Branch })
  branch: Branch;

  @ManyToOne(() => Customer, { onDelete: 'CASCADE' })
  @ApiProperty({ type: () => Customer })
  customer: Customer;

  @OneToMany(() => CreditHistory, (history) => history.credit)
  creditHistory: CreditHistory[];
}
