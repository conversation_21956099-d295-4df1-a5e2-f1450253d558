import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IsNull, MoreThan, Not, Repository, UpdateResult } from 'typeorm';
import { Decimal } from 'decimal.js';
import { CreditStatus, CreditType, PeriodUnit } from 'src/core/enums/entity';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import { Credit } from './credit.entity';
import { CreditHistory } from '../credit-history/credit-history.entity';
import { UUID } from 'crypto';
import { User } from '../user/user.entity';
import * as moment from 'moment-timezone';
import { countExpiryDate } from 'src/core/common/common.utils';
import { UpdateCreditDto } from './dto/updateCredit.dto';

@Injectable()
export class CreditService extends BaseCrudService<Credit> {
  constructor(
    @InjectRepository(Credit) repo: Repository<Credit>,
    @InjectRepository(CreditHistory)
    private creditHistoryRepo: Repository<CreditHistory>,
  ) {
    super(repo);
  }

  async update(id: string, body: UpdateCreditDto): Promise<UpdateResult> {
    const exist = await this.repo.findOne({
      where: { id },
      relations: ['customer'],
    });

    if (!exist) {
      throw new NotFoundException('Credit not found');
    }

    if (body.expiryDate) {
      const credits = await this.repo.find({
        where: {
          customer: { id: exist.customer?.id },
          status: CreditStatus.VALID,
        },
      });

      await Promise.all(
        credits.map(async (credit) => {
          credit.expiryDate = body.expiryDate;

          // Update the credit entity
          await this.repo.save(credit);

          // Now also update all related credit histories with active membership packages
          const creditHistories = await this.creditHistoryRepo.find({
            where: {
              credit: { id: credit.id },
              isMembershipPkg: true,
              isPurgeExpired: false,
              usable: MoreThan(0),
            },
            relations: ['invoice'],
          });

          if (creditHistories.length > 0) {
            // Group credit histories by invoice to identify packages purchased together
            const invoiceGroups =
              this.groupCreditHistoriesByInvoice(creditHistories);

            // Update each group of credit histories
            for (const group of invoiceGroups.values()) {
              // Update all histories in this group to use the new expiry date
              for (const history of group.histories) {
                await this.creditHistoryRepo.update(
                  { id: history.id },
                  { expiryDate: body.expiryDate },
                );
              }
            }
          }
        }),
      );
    }

    try {
      return await this.repo.update({ id }, { ...body });
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  // Helper method to group credit histories by invoice
  private groupCreditHistoriesByInvoice(
    creditHistories: CreditHistory[],
  ): Map<string, { histories: CreditHistory[] }> {
    const invoiceGroups = new Map<string, { histories: CreditHistory[] }>();

    for (const history of creditHistories) {
      if (history.invoice?.id) {
        const invoiceId = history.invoice.id;

        if (!invoiceGroups.has(invoiceId)) {
          invoiceGroups.set(invoiceId, {
            histories: [],
          });
        }

        const group = invoiceGroups.get(invoiceId);
        group.histories.push(history);
      } else {
        // For histories without invoice, treat each as its own group with ID = history.id
        invoiceGroups.set(history.id, {
          histories: [history],
        });
      }
    }

    return invoiceGroups;
  }

  async getCreditHistory(customerId: string, creditType: string) {
    let creditHistory = [];
    let responseCredit = {};
    if (customerId && creditType === CreditType.NEW) {
      const credit = await this.repo.findOne({
        where: {
          customer: { id: customerId },
          creditSetting: { creditType: CreditType.NEW },
        },
        relations: ['creditSetting'],
      });
      if (credit) {
        responseCredit = credit;
        creditHistory = await this.creditHistoryRepo.find({
          where: {
            credit: {
              id: credit.id,
            },
          },
        });
      }
    }

    return {
      credit: responseCredit,
      creditHistory: creditHistory,
    };
  }

  async getCreditMembershipRefund(customerId: string) {
    const credits = await this.repo.find({
      where: {
        customer: { id: customerId },
      },
      relations: ['creditSetting'],
    });
    let creditMemberships;
    const memberships = [];
    if (credits && credits.length) {
      for (const credit of credits) {
        creditMemberships = await this.creditHistoryRepo.find({
          where: {
            credit: {
              id: credit.id,
            },
            isMembershipPkg: true,
            expiryDate: Not(IsNull()),
            product: {
              isPassport: false,
            },
            purged: IsNull(),
          },
        });
        if (creditMemberships && creditMemberships.length) {
          const exchange = new Decimal(credit.creditSetting?.price ?? 1);
          for (const creditMembership of creditMemberships) {
            const orders = creditMembership.detail?.orders;
            const purchaseDate = creditMembership.detail?.date;
            const referenceNo = creditMembership.detail?.code;
            const expiredDate = creditMembership.expiryDate;
            const swapExpiredDate = new Date(expiredDate);
            const currentDate = new Date();
            const usable =
              creditMembership.isRefund === true
                ? new Decimal(creditMembership?.refundBeforeTax ?? 0)
                : new Decimal(creditMembership?.usable ?? 0);
            const usableExchange = usable.dividedBy(exchange);
            if (orders && orders.length) {
              for (const order of orders) {
                const totalBeforeTax = new Decimal(
                  order.product.price * order.quantity,
                );
                const newTotal = totalBeforeTax.isPositive()
                  ? totalBeforeTax.plus(totalBeforeTax.times(0.09))
                  : new Decimal(0);
                memberships.push({
                  id: creditMembership.id,
                  type: credit.creditSetting.creditType,
                  purchaseDate,
                  expiredDate,
                  referenceNo,
                  description: order.product.name,
                  price: parseFloat(totalBeforeTax.toFixed(2)),
                  total: parseFloat(newTotal.toFixed(2)),
                  refund: parseFloat(usable.toFixed(2)),
                  value: parseFloat(usableExchange.toFixed(2)),
                  isExpired: swapExpiredDate.getTime() < currentDate.getTime(),
                  isRefund: creditMembership.isRefund,
                });
              }
            }
          }
        }
      }
    }
    return memberships;
  }

  async getCreditMembershipRefundPurged(customerId: string) {
    const transformedData = [];
    let total = new Decimal(0);
    if (customerId) {
      const queryBuilder = this.creditHistoryRepo
        .createQueryBuilder('creditHistory')
        .leftJoinAndSelect('creditHistory.credit', 'credit')
        .leftJoinAndSelect('credit.creditSetting', 'creditSetting')
        .leftJoinAndSelect('credit.customer', 'customer')
        .where('customer.id = :customerId', { customerId })
        .andWhere('creditHistory.isPurgeExpired = :isPurgeExpired', {
          isPurgeExpired: true,
        });
      const memberships = await queryBuilder.getMany();
      for (const membership of memberships) {
        const purgedCredit = new Decimal(membership.paid);
        total = total.plus(purgedCredit);
        transformedData.push({
          purgedDate:
            membership.purgedDate ||
            countExpiryDate(membership.expiryDate, 'days', 1),
          purgedCredit,
        });
      }
    }
    return {
      data: transformedData,
      total,
    };
  }

  async getCreditMembershipRefundHistory(id: UUID) {
    const creditMembership = await this.creditHistoryRepo.findOne({
      where: {
        id,
        usable: MoreThan(0),
      },
      relations: ['credit', 'credit.creditSetting'],
    });
    let transformedData = {};
    const orders = [];
    if (creditMembership) {
      const queryBuilder = this.creditHistoryRepo
        .createQueryBuilder('creditHistory')
        .where('creditHistory.membership.id = :membershipId', {
          membershipId: creditMembership.id,
        })
        .andWhere('creditHistory.isRefund = :isRefund', { isRefund: false });
      const membershipUsables = await queryBuilder.getMany();
      if (membershipUsables && membershipUsables.length) {
        for (const membershipUsable of membershipUsables) {
          const detail = membershipUsable?.detail as any;
          if (detail.orders && detail.orders.length) {
            for (const order of detail.orders) {
              const items = [];
              for (const item of order.items) {
                items.push({
                  ...item,
                });
              }
              orders.push({
                id: order.id,
                referenceNo: detail.code,
                date: detail.date,
                items,
              });
            }
          }
        }
        const exchange = new Decimal(
          creditMembership.credit.creditSetting?.price ?? 1,
        );
        const usable = new Decimal(creditMembership?.usable ?? 0);
        const usableExchange = usable.dividedBy(exchange);
        transformedData = {
          refund: parseFloat(usableExchange.toFixed(2)),
        };
      }

      const creditMembershipDetail = creditMembership.detail as any;
      transformedData = {
        ...transformedData,
        referenceNo: creditMembershipDetail?.code,
        purchaseDate: creditMembershipDetail?.date,
        description: creditMembershipDetail?.orders[0]?.product.name,
        orders,
      };
    }

    return transformedData;
  }

  async creditMembershipRefund(id: UUID, dto: any, req: any) {
    const user = req.user;
    const creditMembership = await this.creditHistoryRepo.findOne({
      where: {
        id,
        usable: MoreThan(0),
        isRefund: false, // Only find original transactions
        isRefunded: false, // Only find non-refunded transactions
      },
      relations: ['credit', 'credit.creditSetting', 'credit.customer'],
    });

    if (!creditMembership) {
      throw new BadRequestException(
        'This membership has a zero balance or has already been refunded',
      );
    }

    let result;
    let adjustedValue;
    const queryRunner = this.repo.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction('SERIALIZABLE');
    try {
      const ordersData = await this.getMembershipOrders(id, dto);

      if (
        creditMembership &&
        (creditMembership.credit.creditSetting.creditType === CreditType.OLD ||
          creditMembership.credit.creditSetting.creditType === CreditType.NEW)
      ) {
        result = await this.processMembershipRefund(
          queryRunner,
          creditMembership,
          [],
          user,
        );

        if (!result) {
          throw new BadRequestException('Failed to process membership refund');
        }

        let totalPriceDifference = new Decimal(0);
        if (ordersData && ordersData.length > 0) {
          for (const order of ordersData) {
            if (order.items && order.items.length) {
              for (const item of order.items) {
                const memberPrice = new Decimal(item.price ?? 0);
                const nonMemberPrice = new Decimal(
                  item.nonMemberPrice ?? 0,
                ).mul(item.quantity);
                item.nonMemberPrice = nonMemberPrice;
                const difference = nonMemberPrice.minus(memberPrice);
                totalPriceDifference = totalPriceDifference.plus(difference);
              }
            }
          }
        }

        adjustedValue = new Decimal(result.value).minus(totalPriceDifference);

        const paid = new Decimal(creditMembership.paid);

        const fixedRefundDate =
          creditMembership.refundDate || creditMembership.detail['date'];

        // Convert adjustedValue (Decimal) to number for calculations
        const adjustedValueNumber = Number(adjustedValue.toFixed(2));
        const refundValueNumber = Number(
          (adjustedValueNumber + adjustedValueNumber * 0.09).toFixed(2),
        );
        result = {
          ...result,
          orders: ordersData,
          refund: adjustedValueNumber,
          value: adjustedValueNumber,
          price: parseFloat(paid.toFixed(2)),
          purchaseDate: fixedRefundDate,
          refundDate:
            typeof creditMembership.detail === 'object' &&
            creditMembership.detail &&
            'date' in creditMembership.detail
              ? creditMembership.detail.date
              : undefined,
          refundBeforeTax: adjustedValueNumber,
          refundValue: refundValueNumber,
        };
      } else {
        throw new BadRequestException('Invalid credit type for refund');
      }

      await queryRunner.commitTransaction();

      // Find the newly created refund transaction
      const refundedCreditHistory = await this.creditHistoryRepo.findOne({
        where: {
          membership: { id: creditMembership.id },
          isRefund: true,
          isRefunded: false,
        },
      });

      if (refundedCreditHistory) {
        // Ensure adjustedValue is a number, fallback to the 'paid' value
        const refundBase =
          Number(
            adjustedValue !== undefined
              ? adjustedValue.toFixed(2)
              : creditMembership.paid,
          ) || 0;
        const refundValueCalc = Number(
          (refundBase + refundBase * 0.09).toFixed(2),
        );
        await this.creditHistoryRepo.update(
          {
            id: refundedCreditHistory.id,
          },
          {
            refund: refundBase,
            value: refundBase,
            refundBeforeTax: refundBase,
            refundValue: refundValueCalc,
            refundDto: dto,
            refundDate:
              typeof creditMembership.detail === 'object' &&
              creditMembership.detail &&
              'date' in creditMembership.detail
                ? creditMembership.detail.date
                : undefined,
          },
        );
      }

      result.refund = parseFloat(
        (refundedCreditHistory.refund ?? 0).toFixed(2),
      );
      result.value = parseFloat((refundedCreditHistory.paid ?? 0).toFixed(2));

      return result;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private async getMembershipOrders(membershipId: string, dto: any) {
    if (!dto.orders?.length) {
      return [];
    }

    const membershipUsables = await this.creditHistoryRepo
      .createQueryBuilder('creditHistory')
      .where('creditHistory.membership.id = :membershipId', {
        membershipId,
      })
      .andWhere('creditHistory.isRefund = :isRefund', { isRefund: false })
      .andWhere('creditHistory.isRefunded = :isRefunded', { isRefunded: false })
      .getMany();

    const orders = [];

    if (membershipUsables?.length) {
      for (const membershipUsable of membershipUsables) {
        const detail = membershipUsable?.detail as any;
        if (detail?.orders?.length) {
          for (const order of detail.orders) {
            const foundOrder = dto.orders.find((f) => f.id === order.id);
            if (foundOrder) {
              const items = [];
              for (const item of order.items) {
                const foundItem = foundOrder.items.find(
                  (f) => f.id === item.id,
                );
                if (foundItem) {
                  items.push({
                    ...item,
                    nonMemberPrice: foundItem.nonMemberPrice
                      ? foundItem.nonMemberPrice
                      : item.price,
                  });
                }
              }
              orders.push({
                ...order,
                invoiceCode: detail.code,
                invoiceDate: detail.date,
                items: items,
              });
            }
          }
        }
      }
    }

    return orders;
  }

  async scheduleCheckMembershipExpired() {
    const credits = await this.repo.find({
      where: {
        status: CreditStatus.VALID,
      },
      relations: ['creditSetting'],
    });
    if (credits && credits.length) {
      for (const credit of credits) {
        const currentDate = moment();
        const isExpired = moment(credit.expiryDate).isBefore(currentDate);
        let creditCurrent = new Decimal(credit.total);
        let creditBalanceCurrent = new Decimal(credit.creditBalance);
        if (isExpired) {
          const creditMemberships = await this.creditHistoryRepo
            .createQueryBuilder('creditMembership')
            .select([
              'creditMembership.id',
              'creditMembership.usable',
              'creditMembership.paid',
              'creditMembership.detail',
              'creditMembership.expiryDate',
            ])
            .where('creditMembership.credit.id = :creditId', {
              creditId: credit.id,
            })
            .andWhere('creditMembership.usable > :usable', { usable: 0 })
            .andWhere('creditMembership.isRefund = :isRefund', {
              isRefund: false,
            })
            .andWhere('creditMembership.purged IS NULL')
            .getMany();
          if (creditMemberships && creditMemberships.length) {
            for (const creditMembership of creditMemberships) {
              const paid = new Decimal(creditMembership.paid);
              creditCurrent = creditCurrent.minus(paid);
              const usable = new Decimal(creditMembership.usable);
              const creditBalanceCurrentNew =
                creditBalanceCurrent.minus(usable);
              await this.repo.manager.save(CreditHistory, {
                id: creditMembership.id,
                purged: creditMembership.usable,
              });
              await this.repo.manager.save(CreditHistory, {
                usable: 0,
                paid: parseFloat(usable.toFixed(2)),
                opening: parseFloat(creditBalanceCurrent.toFixed(2)),
                closing: parseFloat(creditBalanceCurrentNew.toFixed(2)),
                credit: {
                  id: credit.id,
                },
                membership: {
                  id: creditMembership.id,
                },
                isMembershipPkg: false,
                detail: creditMembership.detail,
                expiryDate: creditMembership.expiryDate,
                purgedDate: new Date(),
                isPurgeExpired: true,
              });
              creditBalanceCurrent = creditBalanceCurrent.minus(usable);
            }
          }
          await this.repo.manager.save(Credit, {
            id: credit.id,
            status: CreditStatus.EXPIRED,
            total: parseFloat(creditCurrent.toFixed(2)),
            creditBalance: parseFloat(creditBalanceCurrent.toFixed(2)),
          });
        }
      }
    }
    return 'Successfully!';
  }

  async processMembershipRefund(
    queryRunner: any,
    creditMembership: any,
    orders: any,
    user: any,
  ) {
    const findUser = await queryRunner.manager.findOne(User, {
      where: { id: user.id },
    });

    const dtMembership = creditMembership.detail.orders[0]?.product;
    let newDate = new Date(creditMembership?.credit?.expiryDate);

    if (dtMembership) {
      newDate = countExpiryDate(
        new Date(creditMembership?.credit?.expiryDate),
        dtMembership.periodUnit,
        dtMembership.period,
      );
    }

    const usable = new Decimal(creditMembership.usable);
    const paid = new Decimal(creditMembership.paid);
    const exchange = new Decimal(
      creditMembership.credit.creditSetting?.price ?? 1,
    );
    const creditCurrent = new Decimal(creditMembership.credit.total);
    const creditBalanceCurrent = new Decimal(
      creditMembership.credit.creditBalance,
    );

    let usableExchange = usable.dividedBy(exchange);
    const valueUsableExchange = usableExchange;
    let refundValue = new Decimal(0);
    const creditType = creditMembership.credit.creditSetting.creditType;

    if (creditType === CreditType.NEW && orders.length) {
      const getOrders = [];
      let totalPriceDifference = new Decimal(0);

      for (const order of orders) {
        const items = order.items;
        const getItems = [];
        if (items && items.length) {
          for (const item of items) {
            const price = new Decimal(item.price ?? 0);
            const nonMemberPrice = new Decimal(item.nonMemberPrice ?? 0);
            const priceDifference = nonMemberPrice.minus(price);
            totalPriceDifference = totalPriceDifference.plus(priceDifference);

            getItems.push({
              ...item,
              nonMemberPrice: nonMemberPrice.toNumber(),
            });
          }
        }
        getOrders.push({
          id: order.id,
          referenceNo: order.invoiceCode,
          date: order.invoiceDate,
          items: getItems,
        });
      }

      usableExchange = usableExchange.minus(totalPriceDifference);
      refundValue = usableExchange.isPositive()
        ? usableExchange.plus(usableExchange.times(0.09))
        : new Decimal(0);

      const creditCurrentNew = creditCurrent.minus(paid);
      const creditBalanceCurrentNew = creditBalanceCurrent.minus(usable);

      await this.processUpdateCreditHistory(
        queryRunner,
        creditMembership,
        user,
        refundValue,
        usable,
        usableExchange,
        creditBalanceCurrent,
        creditBalanceCurrentNew,
        creditCurrentNew,
        newDate,
      );

      return {
        referenceNo: creditMembership.detail?.code,
        purchaseDate: creditMembership.detail?.date,
        refundDate:
          typeof creditMembership.detail === 'object' &&
          creditMembership.detail &&
          'date' in creditMembership.detail
            ? creditMembership.detail.date
            : undefined,
        refund: parseFloat(usableExchange.toFixed(2)),
        value: parseFloat(usableExchange.toFixed(2)),
        price: parseFloat(paid.toFixed(2)),
        refundBeforeTax: parseFloat(usableExchange.toFixed(2)),
        tax: 9,
        refundValue: parseFloat(refundValue.toFixed(2)),
        customer: creditMembership.credit.customer,
        employee: findUser
          ? {
              id: findUser?.id,
              name:
                findUser?.fullname ||
                findUser?.displayName ||
                findUser?.username,
            }
          : null,
        orders: getOrders,
      };
    }
    if (
      (creditType === CreditType.OLD || creditType === CreditType.NEW) &&
      !orders.length
    ) {
      refundValue = usableExchange.isPositive()
        ? usableExchange.plus(usableExchange.times(0.09))
        : new Decimal(0);
      const creditCurrentNew = creditCurrent.minus(paid);
      const creditBalanceCurrentNew = creditBalanceCurrent.minus(usable);
      const membershipUsable = await this.creditHistoryRepo
        .createQueryBuilder('creditHistory')
        .where('creditHistory.membership.id = :membershipId', {
          membershipId: creditMembership.id,
        })
        .andWhere('creditHistory.isRefund = :isRefund', { isRefund: false })
        .getOne();
      await this.processUpdateCreditHistory(
        queryRunner,
        creditMembership,
        user,
        refundValue,
        usable,
        usableExchange,
        creditBalanceCurrent,
        creditBalanceCurrentNew,
        creditCurrentNew,
        newDate,
        membershipUsable.detail,
      );
      return {
        referenceNo: creditMembership.detail?.code,
        purchaseDate: creditMembership.detail?.date,
        refundDate:
          typeof creditMembership.detail === 'object' &&
          creditMembership.detail &&
          'date' in creditMembership.detail
            ? creditMembership.detail.date
            : undefined,
        refund: parseFloat(usable.toFixed(2)),
        value: parseFloat(valueUsableExchange.toFixed(2)),
        price: parseFloat(paid.toFixed(2)),
        description: creditMembership?.detail?.orders[0]?.product?.name,
        refundBeforeTax: parseFloat(usableExchange.toFixed(2)),
        tax: 9,
        refundValue: parseFloat(refundValue.toFixed(2)),
        customer: creditMembership.credit.customer,
        employee: findUser
          ? {
              id: findUser?.id,
              name:
                findUser?.fullname ||
                findUser?.displayName ||
                findUser?.username,
            }
          : null,
        orders: [],
      };
    }
    return null;
  }

  async previewCreditMembershipRefund(
    creditHistoryId: string,
    req: any,
  ): Promise<any> {
    interface DetailType {
      orders?: Array<{
        id: string;
        __v?: number;
        tax?: number;
        code?: string;
        note?: string;
        items?: Array<{
          price: number;
          nonMemberPrice?: number;
          [key: string]: any;
        }>;
        [key: string]: any;
      }>;
      code?: string;
      date?: string | Date;
    }

    const findUser = await this.creditHistoryRepo.manager.findOne(User, {
      where: { id: req.user.id },
    });

    const creditMembership = await this.creditHistoryRepo.findOne({
      where: {
        id: creditHistoryId,
      },
      relations: [
        'credit',
        'credit.creditSetting',
        'credit.customer',
        'membership',
      ],
    });

    if (!creditMembership) {
      throw new BadRequestException('This membership has a zero balance');
    }

    const detail = creditMembership.detail as DetailType;
    const orders = Array.isArray(detail?.orders)
      ? detail.orders.map((order) => ({
          id: order.id,
          __v: order.__v,
          tax: order.tax || 0,
          code: order.code,
          note: order.note,
          items: Array.isArray(order.items)
            ? order.items.map((item) => ({
                ...item,
                nonMemberPrice: item.nonMemberPrice || item.price,
              }))
            : [],
          total: order.total,
          status: order.status,
          created: order.created,
          deleted: order.deleted,
          isDraft: order.isDraft,
          payload: order.payload,
          updated: order.updated,
          discount: order.discount,
          isEdited: order.isEdited,
          subTotal: order.subTotal,
          isPrinted: order.isPrinted,
          orderType: order.orderType,
          couponCode: order.couponCode,
          couponName: order.couponName,
          isComplete: order.isComplete,
          discountMoney: order.discountMoney,
          totalBeforeTax: order.totalBeforeTax,
          discountProductId: order.discountProductId,
          invoiceCode: detail.code,
          invoiceDate: detail.date,
        }))
      : [];

    const usable = new Decimal(creditMembership.paid);
    const paid = new Decimal(creditMembership.paid);
    const exchange = new Decimal(
      creditMembership.credit.creditSetting?.price ?? 1,
    );
    const usableExchange = usable.dividedBy(exchange);
    const valueUsableExchange = usableExchange;

    // Calculate total price difference from orders
    let totalPriceDifference = new Decimal(0);
    if (orders?.length > 0) {
      for (const order of orders) {
        if (order.items?.length) {
          for (const item of order.items) {
            const memberPrice = new Decimal(item.price ?? 0);
            const nonMemberPrice = new Decimal(
              item.nonMemberPrice ?? item.price ?? 0,
            );
            const difference = nonMemberPrice.minus(memberPrice);
            totalPriceDifference = totalPriceDifference.plus(difference);
          }
        }
      }
    }

    // Adjust values based on price difference
    const adjustedValue = new Decimal(valueUsableExchange).minus(
      totalPriceDifference,
    );
    const refundValue = adjustedValue.plus(adjustedValue.times(0.09));

    const ordersData = await this.getMembershipOrders(
      creditMembership.membership.id,
      creditMembership.refundDto,
    );

    const fixedRefundDate = creditMembership.refundDate || detail.date;
    const result = {
      referenceNo: detail.code,
      purchaseDate: fixedRefundDate,
      refundDate:
        typeof creditMembership.detail === 'object' &&
        creditMembership.detail &&
        'date' in creditMembership.detail
          ? creditMembership.detail.date
          : undefined,
      refund: parseFloat(paid.toFixed(2)),
      value: parseFloat(adjustedValue.toFixed(2)),
      price: parseFloat(paid.toFixed(2)),
      refundBeforeTax: parseFloat(adjustedValue.toFixed(2)),
      tax: 9,
      refundValue: parseFloat(refundValue.toFixed(2)),
      customer: creditMembership.credit.customer,
      employee: findUser
        ? {
            id: findUser?.id,
            name:
              findUser?.fullname || findUser?.displayName || findUser?.username,
          }
        : null,
      orders: ordersData,
    };

    if (creditMembership.isRefund) {
      result.price = creditMembership.membership?.paid ?? result.price;
      result.value = creditMembership.paid ?? 0;
      result.refund = result.value;
      result.refundBeforeTax = creditMembership.refundBeforeTax ?? result.value;
      result.refundValue = creditMembership.refundValue ?? 0;
    }
    return result;
  }

  async processUpdateCreditHistory(
    queryRunner: any,
    creditMembership: CreditHistory,
    user: any,
    refundValue: any,
    usable: any,
    usableExchange: any,
    creditBalanceCurrent: any,
    creditBalanceCurrentNew: any,
    creditCurrentNew: any,
    newDate: any,
    newDetail?: any,
    adjustedValue?: any,
  ) {
    // Update original transaction to mark as refunded
    await queryRunner.manager.save(CreditHistory, {
      id: creditMembership.id,
      isRefunded: true, // Mark original transaction as refunded
      isRefund: false, // Original transaction is not a refund
      usable: 0,
      refund: parseFloat((adjustedValue ?? usable).toFixed(2)),
      value: parseFloat((adjustedValue ?? usable).toFixed(2)),
      price: parseFloat((adjustedValue ?? usable).toFixed(2)),
      refundValue: parseFloat((adjustedValue ?? usable).toFixed(2)),
      refundBeforeTax: parseFloat((adjustedValue ?? usable).toFixed(2)),
      tax: 9,
      refundedBy: {
        id: user.id,
      },
      refundDate:
        typeof creditMembership.detail === 'object' &&
        creditMembership.detail &&
        'date' in creditMembership.detail
          ? creditMembership.detail.date
          : undefined,
      isMembershipPkg: true,
    });

    // Create refund transaction
    // Ensure adjustedValue is a number, fallback to the 'usable' parameter
    const refundBase =
      Number(adjustedValue !== undefined ? adjustedValue : usable) || 0;
    const refundValueCalc = Number((refundBase + refundBase * 0.09).toFixed(2));
    await queryRunner.manager.save(CreditHistory, {
      isRefund: true, // Mark new transaction as refund
      isRefunded: false, // Refund transaction cannot be refunded
      usable: 0,
      credit: {
        id: creditMembership.credit.id,
      },
      opening: parseFloat(creditMembership.closing.toFixed(2)),
      closing: 0,
      paid: refundBase,
      membership: {
        id: creditMembership.id,
      },
      isMembershipPkg: false,
      detail: creditMembership.detail,
      refund: refundBase,
      refundBeforeTax: refundBase,
      refundValue: refundValueCalc,
      tax: 9,
    });

    const currentDate = moment();
    const isExpired = moment(newDate).isBefore(currentDate);
    await queryRunner.manager.save(Credit, {
      id: creditMembership.credit.id,
      total: parseFloat(creditCurrentNew.toFixed(2)),
      creditBalance: parseFloat(creditBalanceCurrentNew.toFixed(2)),
      expiryDate: newDate,
      status: isExpired ? CreditStatus.EXPIRED : CreditStatus.VALID,
    });
  }
}
