import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { CreditController } from './credit.controller';
import { Credit } from './credit.entity';
import { CreditService } from './credit.service';
import { CreditHistory } from '../credit-history/credit-history.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Credit, CreditHistory])],
  controllers: [CreditController],
  providers: [CreditService],
  exports: [CreditService],
})
export class CreditModule {}
