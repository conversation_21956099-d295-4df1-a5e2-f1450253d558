import {
  CrudController,
  CrudRequest,
  Override,
  ParsedRequest,
} from 'src/core/crud/crud';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { Resource, ResourceType } from './resource.entity';
import { ResourceService } from './resource.service';
import { Post, Req } from '@nestjs/common';
import { Public } from '../auth/decorators/public.decerator';

@BaseCrud({
  model: {
    type: Resource,
  },
  routes: {
    only: ['getManyBase'],
  },
  query: {
    filter: {
      $and: [
        {
          type: { $eq: ResourceType.CUSTOM },
        },
      ],
    },
    join: {
      // status: { eager: true, allow: ['id', 'name'] },
    },
  },
})
export class ResourceController implements CrudController<Resource> {
  constructor(public service: ResourceService) {}
  get base(): CrudController<Resource> {
    return this;
  }

  @Post('/gen-default')
  async genDefaultResource() {
    return this.service.genDefaultResource();
  }

  @Override('getManyBase')
  getMany(@ParsedRequest() crudRequest: CrudRequest, @Req() req: Request) {
    return this.service.getManyResource(crudRequest);
  }
}
