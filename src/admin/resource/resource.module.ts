import { TypeOrmModule } from '@nestjs/typeorm';
import { Module } from '@nestjs/common';
import { Resource } from './resource.entity';
import { ResourceController } from './resource.controller';
import { ResourceService } from './resource.service';
import { Setting } from '../setting/setting.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Resource, Setting])],
  controllers: [ResourceController],
  providers: [ResourceService],
  exports: [ResourceService],
})
export class ResourceModule {}
