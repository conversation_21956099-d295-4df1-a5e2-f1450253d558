import {
  <PERSON><PERSON><PERSON>,
  Column,
  OneToMany,
  ManyToOne,
  TreeChildren,
  TreeParent,
} from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty } from '@nestjs/swagger';
import { Permission } from '../permission/permission.entity';
import { Setting } from '../setting/setting.entity';

export enum ResourceType {
  DEFAULT = 'DEFAULT',
  CUSTOM = 'CUSTOM',
}

@Entity('resource')
export class Resource extends DocEntity {
  @Column()
  name: string;

  @Column()
  code: string;

  @Column()
  order: number;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Setting })
  status: Setting;

  @TreeParent({ onDelete: 'SET NULL' })
  @ApiProperty({
    type: () => Resource,
    example: null,
    properties: {
      hide: { default: true },
    },
  })
  parent: Resource;

  @TreeChildren()
  @ApiProperty({
    type: () => [Resource],
    required: false,
    readOnly: true,
    example: null,
    properties: {
      hide: { default: true },
    },
  })
  children: Resource[];

  @OneToMany(() => Permission, (permission) => permission.resource)
  permission: Permission[];

  @ApiProperty()
  @Column({
    type: 'enum',
    enum: Object.values(ResourceType),
    default: ResourceType.DEFAULT,
  })
  type: ResourceType;

  @Column({ nullable: true })
  create: boolean;

  @Column({ nullable: true })
  read: boolean;

  @Column({ nullable: true })
  list: boolean;

  @Column({ nullable: true })
  update: boolean;

  @Column({ nullable: true })
  delete: boolean;

  @Column({ nullable: true })
  print: boolean;

  @Column({ nullable: true })
  download: boolean;

  @Column({ nullable: true })
  active: boolean;

  @Column({ nullable: true })
  changePass: boolean;

  @Column({ nullable: true })
  listCalendar: boolean;

  @Column({ nullable: true })
  addAppointment: boolean;

  @Column({ nullable: true })
  export: boolean;

  @Column({ nullable: true })
  sendMail: boolean;

  @Column({ nullable: true })
  checkOut: boolean;

  @Column({ nullable: true })
  changePaymentSingleDay: boolean;

  @Column({ nullable: true })
  changePaymentAllDays: boolean;

  @Column({ nullable: true })
  editSingleDay: boolean;

  @Column({ nullable: true })
  editAllDays: boolean;

  @Column({ nullable: true })
  refund: boolean;
}
