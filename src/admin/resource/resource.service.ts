import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BaseCrudService } from 'src/core/base/base-crud.service';
import * as _ from 'lodash';
import { Resource, ResourceType } from './resource.entity';
import { genUUID } from 'src/core/crypto/crypto.provider';
import { Setting } from '../setting/setting.entity';
import { CrudRequest } from 'src/core/crud/crud';
import { REPORT_NAME_ALIAS, REPORT_ROUTE } from '../report/report.const';
@Injectable()
export class ResourceService extends BaseCrudService<Resource> {
  constructor(
    @InjectRepository(Resource) repo: Repository<Resource>,
    @InjectRepository(Setting)
    private settingRepo: Repository<Setting>,
  ) {
    super(repo);
  }

  async genDefaultResource(): Promise<boolean> {
    const activeSatus = await this.settingRepo.findOne({
      where: { name: 'Active' },
    });
    const childReport = [];

    Object.keys(REPORT_NAME_ALIAS).forEach((rKey, index) => {
      const reportCode = REPORT_ROUTE[rKey].replaceAll('-', '_');
      childReport.push({
        id: genUUID(reportCode, 'Resource-report-child'),
        code: reportCode,
        name: REPORT_NAME_ALIAS[rKey],
        type: ResourceType.CUSTOM,
        order: index + 1,
        status: activeSatus,
        list: true,
        export: true,
        print: true,
      });
    });

    const resources = [
      {
        id: genUUID('home', 'Resource'),
        code: 'home',
        name: 'Home',
        type: ResourceType.CUSTOM,
        order: 1,
        status: activeSatus,
      },
      {
        id: genUUID('reports', 'Resource'),
        code: 'reports',
        name: 'Reports',
        type: ResourceType.CUSTOM,
        order: 2,
        status: activeSatus,
        children: childReport,
      },
      {
        id: genUUID('sales', 'Resource'),
        code: 'sales',
        name: 'Sales',
        type: ResourceType.CUSTOM,
        order: 3,
        status: activeSatus,
        children: [
          {
            id: genUUID('daily_sales', 'Resource'),
            code: 'daily_sales',
            name: 'DAILY SALES',
            type: ResourceType.CUSTOM,
            order: 1,
            status: activeSatus,
            list: true,
            read: true,
            export: true,
          },
          {
            id: genUUID('sales_invoices', 'Resource'),
            code: 'sales_invoices',
            name: 'INVOICES',
            type: ResourceType.CUSTOM,
            order: 2,
            status: activeSatus,
            list: true,
            read: true,
            changePaymentSingleDay: true,
            changePaymentAllDays: true,
          },
          {
            id: genUUID('sales_credit', 'Resource'),
            code: 'sales_credit',
            name: 'CREDIT',
            type: ResourceType.CUSTOM,
            order: 3,
            status: activeSatus,
            list: true,
            read: true,
            refund: true,
          },
        ],
      },
      {
        id: genUUID('appointment', 'Resource'),
        code: 'appointment',
        name: 'Appointment',
        type: ResourceType.CUSTOM,
        order: 4,
        status: activeSatus,
        children: [],
        listCalendar: true,
        create: true,
        list: true,
        read: true,
        update: true,
        delete: true,
      },
      {
        id: genUUID('admin', 'Resource'),
        code: 'admin',
        name: 'Admin',
        type: ResourceType.CUSTOM,
        order: 5,
        status: activeSatus,
        children: [
          {
            id: genUUID('user', 'Resource-admin-Child'),
            code: 'admin_user',
            name: 'User',
            type: ResourceType.CUSTOM,
            order: 1,
            status: activeSatus,
            create: true,
            list: true,
            read: true,
            update: true,
            delete: true,
            active: true,
            changePass: true,
          },
          {
            id: genUUID('role', 'Resource-admin-Child'),
            code: 'admin_role',
            name: 'Role',
            type: ResourceType.CUSTOM,
            order: 1,
            status: activeSatus,
            create: true,
            list: true,
            read: true,
            update: true,
            delete: true,
            active: true,
          },
        ],
      },
      {
        id: genUUID('customer', 'Resource'),
        code: 'customer',
        name: 'Customer',
        type: ResourceType.CUSTOM,
        order: 6,
        status: activeSatus,
        create: true,
        list: true,
        read: true,
        update: true,
        delete: true,
        active: true,
        addAppointment: true,
        children: [],
      },
      {
        id: genUUID('employee', 'Resource'),
        code: 'employee',
        name: 'Employee',
        type: ResourceType.CUSTOM,
        order: 7,
        status: activeSatus,
        create: false,
        list: true,
        read: false,
        update: false,
        delete: false,
        children: [
          {
            id: genUUID('working_hours', 'Resource'),
            code: 'working_hours',
            name: 'WORKING HOURS',
            type: ResourceType.CUSTOM,
            order: 1,
            status: activeSatus,
            create: true,
            list: true,
            read: true,
            update: true,
            delete: true,
          },
          {
            id: genUUID('employee_list', 'Resource'),
            code: 'employee_list',
            name: 'EMPLOYEE LIST',
            type: ResourceType.CUSTOM,
            order: 2,
            status: activeSatus,
            create: true,
            list: true,
            read: true,
            update: true,
            delete: true,
            active: true,
          },
          {
            id: genUUID('commission', 'Resource'),
            code: 'commission',
            name: 'COMMISSION',
            type: ResourceType.CUSTOM,
            order: 3,
            status: activeSatus,
            list: true,
          },
          {
            id: genUUID('payslip', 'Resource'),
            code: 'payslip',
            name: 'PAYSLIP',
            type: ResourceType.CUSTOM,
            order: 4,
            status: activeSatus,
            list: true,
            read: true,
            print: true,
            download: true,
          },
          {
            id: genUUID('day_offs', 'Resource'),
            code: 'day_offs',
            name: 'DAY-OFFS',
            type: ResourceType.CUSTOM,
            order: 5,
            status: activeSatus,
            create: true,
            list: true,
            read: true,
            update: true,
            delete: true,
          },
        ],
      },
      {
        id: genUUID('product_service', 'Resource'),
        code: 'product_service',
        name: 'Product & Service',
        type: ResourceType.CUSTOM,
        order: 8,
        status: activeSatus,
        children: [
          {
            id: genUUID('product', 'Resource-product_service-child'),
            code: 'product',
            name: 'PRODUCT',
            type: ResourceType.CUSTOM,
            order: 1,
            status: activeSatus,
            create: true,
            list: true,
            read: true,
            update: true,
            delete: true,
            active: true,
          },
          {
            id: genUUID('service', 'Resource-product_service-child'),
            code: 'service',
            name: 'Service',
            type: ResourceType.CUSTOM,
            order: 2,
            status: activeSatus,
            create: true,
            list: true,
            read: true,
            update: true,
            delete: true,
            active: true,
          },
          {
            id: genUUID('membership', 'Resource-product_service-child'),
            code: 'membership',
            name: 'Membership',
            type: ResourceType.CUSTOM,
            order: 3,
            status: activeSatus,
            create: true,
            list: true,
            read: true,
            update: true,
            delete: true,
            active: true,
          },
          {
            id: genUUID('coupon', 'Resource-product_service-child'),
            code: 'coupon',
            name: 'Coupon',
            type: ResourceType.CUSTOM,
            order: 4,
            status: activeSatus,
            create: true,
            list: true,
            read: true,
            update: true,
            delete: true,
            active: true,
          },
          {
            id: genUUID('setting', 'Resource-product_service-child'),
            code: 'setting_product_service',
            name: 'Setting',
            type: ResourceType.CUSTOM,
            order: 5,
            status: activeSatus,
            create: true,
            list: true,
            update: true,
            delete: true,
            active: true,
          },
        ],
      },
      {
        id: genUUID('FoodBeverage', 'Resource'),
        code: 'food_beverage',
        name: 'F&B',
        type: ResourceType.CUSTOM,
        order: 9,
        status: activeSatus,
        create: true,
        list: true,
        read: true,
        update: true,
        delete: true,
        children: [
          {
            id: genUUID('food', 'Resource-FoodBeverage-Child'),
            code: 'food',
            name: 'FOOD',
            type: ResourceType.CUSTOM,
            order: 1,
            status: activeSatus,
            create: true,
            list: true,
            read: true,
            update: true,
            delete: true,
            active: true,
          },
          {
            id: genUUID('beverage', 'Resource-FoodBeverage-Child'),
            code: 'beverage',
            name: 'BEVERAGE',
            type: ResourceType.CUSTOM,
            order: 2,
            status: activeSatus,
            create: true,
            list: true,
            read: true,
            update: true,
            delete: true,
            active: true,
          },
          {
            id: genUUID('setting', 'Resource-FoodBeverage-child'),
            code: 'setting_food_beverage',
            name: 'Setting',
            type: ResourceType.CUSTOM,
            order: 3,
            status: activeSatus,
            create: true,
            list: true,
            update: true,
            delete: true,
            active: true,
          },
        ],
      },
      {
        id: genUUID('inventory', 'Resource'),
        code: 'inventory',
        name: 'Coupon',
        type: ResourceType.CUSTOM,
        order: 10,
        status: activeSatus,
        create: true,
        list: true,
        read: true,
        update: true,
        delete: true,
        children: [
          {
            id: genUUID('issue_coupon', 'Resource'),
            code: 'issue_coupon',
            name: 'Issue Coupon',
            type: ResourceType.CUSTOM,
            order: 1,
            create: true,
            list: true,
            read: true,
            update: true,
            delete: true,
            active: true,
            sendMail: true,
          },
        ],
      },
      {
        id: genUUID('availability', 'Resource'),
        code: 'availability',
        name: 'Availability',
        type: ResourceType.CUSTOM,
        order: 11,
        status: activeSatus,
        list: true,
        children: [],
      },
      {
        id: genUUID('rfid', 'Resource'),
        code: 'rfid',
        name: 'RFID',
        type: ResourceType.CUSTOM,
        order: 12,
        status: activeSatus,
        create: true,
        list: true,
        read: true,
        update: true,
        delete: true,
        active: true,
        checkOut: true,
        children: [],
      },
      {
        id: genUUID('cashier', 'Resource'),
        code: 'cashier',
        name: 'Cashier',
        type: ResourceType.CUSTOM,
        order: 13,
        status: activeSatus,
        children: [
          {
            id: genUUID('cashier_product', 'Resource-Cashier-Child'),
            code: 'cashier_product',
            name: 'Product',
            type: ResourceType.CUSTOM,
            order: 1,
            list: true,
          },
          {
            id: genUUID('cashier_service', 'Resource-Cashier-Child'),
            code: 'cashier_service',
            name: 'Service',
            type: ResourceType.CUSTOM,
            order: 2,
            list: true,
          },
          {
            id: genUUID('cashier_membership', 'Resource-Cashier-Child'),
            code: 'cashier_membership',
            name: 'Membership',
            type: ResourceType.CUSTOM,
            order: 3,
            list: true,
          },
          {
            id: genUUID('cashier_coupon', 'Resource-Cashier-Child'),
            code: 'cashier_coupon',
            name: 'Coupon',
            type: ResourceType.CUSTOM,
            order: 4,
            list: true,
          },
          {
            id: genUUID('cashier_food', 'Resource-Cashier-Child'),
            code: 'cashier_food',
            name: 'Food',
            type: ResourceType.CUSTOM,
            order: 5,
            list: true,
          },
          {
            id: genUUID('cashier_beverage', 'Resource-Cashier-Child'),
            code: 'cashier_beverage',
            name: 'Beverage',
            type: ResourceType.CUSTOM,
            order: 6,
            list: true,
          },
          {
            id: genUUID('cashier_order', 'Resource-Cashier-Child'),
            code: 'cashier_order',
            name: 'Order',
            type: ResourceType.CUSTOM,
            order: 6,
            editSingleDay: true,
            editAllDays: true,
          },
        ],
      },
      {
        id: genUUID('kitchen', 'Resource'),
        code: 'kitchen',
        name: 'Kitchen/Bar',
        type: ResourceType.CUSTOM,
        order: 14,
        status: activeSatus,
        create: false,
        list: true,
        read: true,
        update: true,
        delete: false,
        children: [
          {
            id: genUUID('kitchen_food', 'Resource-Kitchen-Child'),
            code: 'kitchen_food',
            name: 'Food',
            type: ResourceType.CUSTOM,
            order: 1,
            list: true,
          },
          {
            id: genUUID('kitchen_beverage', 'Resource-Kitchen-Child'),
            code: 'kitchen_beverage',
            name: 'Beverage',
            type: ResourceType.CUSTOM,
            order: 2,
            list: true,
          },
        ],
      },
      {
        id: genUUID('survey', 'Resource'),
        code: 'survey',
        name: 'Survey',
        type: ResourceType.CUSTOM,
        order: 15,
        status: activeSatus,
        create: true,
        list: true,
        read: true,
        update: true,
        delete: true,
        active: true,
        children: [],
      },
      {
        id: genUUID('branch', 'Resource'),
        code: 'branch',
        name: 'Branch',
        order: 16,
        type: ResourceType.CUSTOM,
        status: activeSatus,
        list: true,
        create: true,
        active: true,
        update: true,
        delete: true,
        children: [],
      },
      {
        id: genUUID('setting', 'Resource'),
        code: 'setting',
        name: 'Setting',
        type: ResourceType.CUSTOM,
        order: 17,
        status: activeSatus,
        create: true,
        list: true,
        read: true,
        update: true,
        delete: true,
        children: [
          {
            id: genUUID('credit_setting', 'Resource-setting-Child'),
            code: 'credit_setting',
            name: 'Credit Setting',
            type: ResourceType.CUSTOM,
            order: 1,
            create: true,
            list: true,
            update: true,
            delete: true,
            active: true,
          },
          {
            id: genUUID('commission_setting', 'Resource-setting-Child'),
            code: 'commission_setting',
            name: 'Commission Setting',
            type: ResourceType.CUSTOM,
            order: 2,
            list: true,
            update: true,
          },
          {
            id: genUUID('payslip_setting', 'Resource-setting-Child'),
            code: 'payslip_setting',
            name: 'Payslip Setting',
            type: ResourceType.CUSTOM,
            order: 3,
            list: true,
            update: true,
          },
          {
            id: genUUID('payment_method', 'Resource-setting-Child'),
            code: 'payment_method',
            name: 'Payment Method',
            type: ResourceType.CUSTOM,
            order: 4,
            create: true,
            list: true,
            update: true,
            delete: true,
            active: true,
          },
          {
            id: genUUID('discount_setting', 'Resource-setting-Child'),
            code: 'discount_setting',
            name: 'Discount Setting',
            type: ResourceType.CUSTOM,
            order: 5,
            create: true,
            list: true,
            update: true,
            delete: true,
          },
        ],
      },
    ];

    for (const resource of resources) {
      const children = resource.children || [];
      const exists = await this.repo.findOne({ where: { id: resource.id } });
      delete resource.children;
      if (!exists) {
        await this.repo.insert({ ...resource, status: activeSatus });
      } else {
        await this.repo.update(
          { id: resource.id },
          {
            ...resource,
            status: activeSatus,
          },
        );
      }

      //children
      if (children.length > 0) {
        for (const child of children) {
          const existsChild = await this.repo.findOne({
            where: { id: child.id },
          });

          if (!existsChild) {
            await this.repo.insert({
              ...child,
              status: activeSatus,
              parent: { id: resource.id },
            });
          } else {
            await this.repo.update(
              { id: child.id },
              { ...child, status: activeSatus, parent: { id: resource.id } },
            );
          }
        }
      }
    }

    return true;
  }

  async getManyResource(req: CrudRequest, getAllType = false) {
    const query = this.repo
      .createQueryBuilder('resource')
      .select([
        'resource.id',
        'resource.code',
        'resource.name',
        'resource.type',
        'resource.order',
        'resource.create',
        'resource.list',
        'resource.read',
        'resource.update',
        'resource.delete',
        'resource.print',
        'resource.download',
        'resource.active',
        'resource.changePass',
        'resource.listCalendar',
        'resource.addAppointment',
        'resource.export',
        'resource.sendMail',
        'resource.checkOut',
        'resource.changePaymentSingleDay',
        'resource.changePaymentAllDays',
        'resource.editSingleDay',
        'resource.editAllDays',
        'resource.refund',
        'children.id',
        'children.code',
        'children.name',
        'children.type',
        'children.order',
        'children.create',
        'children.list',
        'children.read',
        'children.update',
        'children.delete',
        'children.print',
        'children.download',
        'children.active',
        'children.changePass',
        'children.listCalendar',
        'children.addAppointment',
        'children.export',
        'children.sendMail',
        'children.checkOut',
        'children.changePaymentSingleDay',
        'children.changePaymentAllDays',
        'children.editSingleDay',
        'children.editAllDays',
        'children.refund',
      ])
      .leftJoin('resource.children', 'children')
      .where('resource.parent IS NULL')

      .orderBy('resource.order', 'ASC')
      .addOrderBy('children.order', 'ASC');

    if (!getAllType) {
      query.andWhere('resource.type = :type', { type: ResourceType.CUSTOM });
    }

    return await query.getMany();
  }
}
