import {
  CrudController,
  CrudRequest,
  Override,
  ParsedRequest,
} from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { SalesService } from './sales.service';
import { Invoice } from '../invoice/invoice.entity';
import { Sales } from './sales.entity';
import { Get, Query, Req } from '@nestjs/common';

@BaseCrud(
  {
    model: {
      type: Sales,
    },
    routes: {
      only: ['getManyBase'],
    },
    query: {},
  },
  {
    // grantPerm: userPerm,
    // group: ResourceGroup.SYSTEM,
  },
)
export class SalesController extends BaseCrudController<Invoice> {
  constructor(public service: SalesService) {
    super(service);
  }
  get base(): CrudController<Invoice> {
    return this;
  }

  @Get('/transaction-summary')
  async getTransactionSummary(
    @ParsedRequest() crudReq: CrudRequest,
    @Req() req: Request,
  ) {
    const branchIds = req.headers?.['branchid']?.split(',') || [];
    return this.service.getTransactionSummary(crudReq, branchIds);
  }

  @Get('/payment-summary')
  async getPaymentSummary(
    @ParsedRequest() crudReq: CrudRequest,
    @Req() req: Request,
  ) {
    const branchIds = req.headers?.['branchid']?.split(',') || [];
    return this.service.getPaymentSummary(crudReq, branchIds);
  }

  @Get('/customer-summary')
  async getCustomerSummary(
    @ParsedRequest() crudReq: CrudRequest,
    @Req() req: Request,
  ) {
    const branchIds = req.headers?.['branchid']?.split(',') || [];
    return this.service.getCustomerSummary(crudReq, branchIds, req);
  }

  @Get('/current-customer')
  async getCurrentCustomer(
    @ParsedRequest() crudReq: CrudRequest,
    @Req() req: Request,
    @Query('keySearch') keySearch: string,
  ) {
    const branchIds = req.headers?.['branchid']?.split(',') || [];
    return this.service.getCurrentCustomer(crudReq, branchIds, keySearch, req);
  }

  @Get('/credits')
  async getCreditList(
    @Query('keySearch') keySearch: string,
    @Req() req: Request,
  ) {
    const branchIds = req.headers?.['branchid']?.split(',') || [];
    return this.service.getCreditList(keySearch, branchIds);
  }
}
