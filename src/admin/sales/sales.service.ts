import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, In, IsNull, Like, Not, Repository } from 'typeorm';
import * as moment from 'moment';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { Invoice } from '../invoice/invoice.entity';
import { InvoiceStatus, ProductType } from 'src/core/enums/entity';
import { Order } from '../order/order.entity';
import { OrderDetail } from '../order-detail/order-detail.entity';
import { InvoicePaymentMethod } from '../invoice/invoice-payment-method.entity';
import { PaymentMethod } from '../payment-method/payment-method.entity';
import { Appointment } from '../appointment/appointment.entity';
import { Customer } from '../customer/customer.entity';
import { CrudRequest } from 'src/core/crud/crud';
import { Rfid } from '../rfid/rfid.entity';
import { UTCtimeZone } from 'src/core/common/common.utils';

@Injectable()
export class SalesService extends BaseCrudService<any> {
  constructor(
    @InjectRepository(Invoice) repo: Repository<any>,
    @InjectRepository(Order) private orderRepo: Repository<Order>,
    @InjectRepository(OrderDetail)
    private orderDetailRepo: Repository<OrderDetail>,
    @InjectRepository(InvoicePaymentMethod)
    private invoicePaymentMethodRepo: Repository<InvoicePaymentMethod>,
    @InjectRepository(PaymentMethod)
    private paymentMethodRepo: Repository<PaymentMethod>,
    @InjectRepository(Appointment)
    private appointmentRepo: Repository<Appointment>,
    @InjectRepository(Customer)
    private customerRepo: Repository<Customer>,
    @InjectRepository(Rfid)
    private rfidRepo: Repository<Rfid>,
  ) {
    super(repo);
  }

  private normalizeDateRangeForSingleDay(
    start: string,
    end: string,
    timezone: string,
  ) {
    const startMoment = moment.tz(start, timezone);
    const endMoment = moment.tz(end, timezone);
    if (startMoment.isSame(endMoment, 'day')) {
      return {
        startTime: startMoment.startOf('day').toISOString(),
        endTime: startMoment.endOf('day').toISOString(),
      };
    }
    return {
      startTime: startMoment.toISOString(),
      endTime: endMoment.toISOString(),
    };
  }

  async getTransactionSummary(
    crudReq: CrudRequest,
    branchIds: string[],
    req?: any,
  ) {
    const data: any = {
      startTime: moment().startOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS'),
      endTime: moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS'),
    };
    let clientZoneName = UTCtimeZone;
    if (req && req.headers && req.headers['timezone']) {
      clientZoneName = req.headers['timezone'];
    }
    if (crudReq.parsed?.filter) {
      for (const f of crudReq.parsed.filter) {
        if (f.field === 'date' && f.operator === '$between') {
          const { startTime, endTime } = this.normalizeDateRangeForSingleDay(
            f.value[0],
            f.value[1],
            clientZoneName,
          );
          data.startTime = startTime;
          data.endTime = endTime;
        }
      }
    }

    const queryBuilder = this.repo
      .createQueryBuilder('invoice')
      .select([
        'invoice.id',
        'invoice.paid',
        'invoice.unPaid',
        'invoice.status',
        'invoice.discount',
      ])
      .leftJoinAndSelect('invoice.parentInvoice', 'parentInvoice')
      .where('invoice.status IN (:...statuses)', {
        statuses: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      });

    if (branchIds.length > 0) {
      queryBuilder.andWhere('invoice.branchId IN (:...branchIds)', {
        branchIds,
      });
    }

    queryBuilder.andWhere('invoice.date BETWEEN :startTime AND :endTime', {
      startTime: data.startTime,
      endTime: data.endTime,
    });

    const invoiceList = await queryBuilder.getMany();

    let productSummary = 0;
    let serviceSummary = 0;
    let membershipSummary = 0;
    let foodBeverageSummary = 0;
    let couponSummary = 0;
    if (invoiceList?.length > 0) {
      for (const invoice of invoiceList) {
        const orderList = invoice?.id
          ? await this.orderRepo.find({
              where: { invoice: { id: invoice?.id } },
              relations: ['items', 'items.product'],
            })
          : [];

        if (invoice?.status == InvoiceStatus.PAID) {
          const typeSubtotals = {
            [ProductType.PRODUCT]: 0,
            [ProductType.SERVICE]: 0,
            [ProductType.MEMBERSHIP]: 0,
            [ProductType.FOOD]: 0,
            [ProductType.BEVERAGE]: 0,
            [ProductType.COUPON]: 0,
          };
          let totalSubtotal = 0;

          if (orderList?.length > 0) {
            for (const order of orderList) {
              const orderPayload: any = order.payload;
              if (orderPayload && orderPayload.items) {
                const items: any[] = orderPayload.items;
                items.forEach((item: any) => {
                  const money =
                    (item?.quantity ?? 1) * (item?.product?.price ?? 0);
                  if (
                    item?.product?.type &&
                    typeSubtotals.hasOwnProperty(item.product.type)
                  ) {
                    typeSubtotals[item.product.type] += money;
                    totalSubtotal += money;
                  }
                });
              }
            }
          }

          const discount = invoice?.discount || 0;
          const typeDiscounts: Record<string, number> = {};
          Object.keys(typeSubtotals).forEach((type) => {
            if (totalSubtotal > 0) {
              typeDiscounts[type] =
                (typeSubtotals[type] / totalSubtotal) * discount;
            } else {
              typeDiscounts[type] = 0;
            }
          });

          productSummary +=
            typeSubtotals[ProductType.PRODUCT] -
            (typeDiscounts[ProductType.PRODUCT] || 0);
          serviceSummary +=
            typeSubtotals[ProductType.SERVICE] -
            (typeDiscounts[ProductType.SERVICE] || 0);
          membershipSummary +=
            typeSubtotals[ProductType.MEMBERSHIP] -
            (typeDiscounts[ProductType.MEMBERSHIP] || 0);
          foodBeverageSummary +=
            typeSubtotals[ProductType.FOOD] +
            typeSubtotals[ProductType.BEVERAGE] -
            ((typeDiscounts[ProductType.FOOD] || 0) +
              (typeDiscounts[ProductType.BEVERAGE] || 0));
          couponSummary +=
            typeSubtotals[ProductType.COUPON] -
            (typeDiscounts[ProductType.COUPON] || 0);
        } else if (invoice?.status == InvoiceStatus.PART_PAID) {
          if (
            orderList?.length > 0 &&
            orderList[0]?.items[0]?.product?.type === ProductType.SERVICE &&
            invoice?.parentInvoice
          ) {
            serviceSummary +=
              (orderList[0]?.items[0]?.quantity ?? 1) *
              (orderList[0]?.items[0]?.product?.price ?? 0);
          }
        }
      }
    }
    return [
      {
        items: 'Product',
        total: parseFloat(productSummary.toFixed(2)),
      },
      {
        items: 'Service',
        total: parseFloat(serviceSummary.toFixed(2)),
      },
      {
        items: 'Membership',
        total: parseFloat(membershipSummary.toFixed(2)),
      },
      {
        items: 'F&B',
        total: parseFloat(foodBeverageSummary.toFixed(2)),
      },
      {
        items: 'Coupon',
        total: parseFloat(couponSummary.toFixed(2)),
      },
      {
        items: 'sum',
        total:
          parseFloat(productSummary.toFixed(2)) +
          parseFloat(serviceSummary.toFixed(2)) +
          parseFloat(membershipSummary.toFixed(2)) +
          parseFloat(foodBeverageSummary.toFixed(2)) +
          parseFloat(couponSummary.toFixed(2)),
      },
    ];
  }

  async getPaymentSummary(
    crudReq: CrudRequest,
    branchIds: string[],
    req?: any,
  ) {
    const data: any = {
      startTime: moment().startOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS'),
      endTime: moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS'),
    };
    let clientZoneName = UTCtimeZone;
    if (req && req.headers && req.headers['timezone']) {
      clientZoneName = req.headers['timezone'];
    }
    if (crudReq.parsed?.filter) {
      for (const f of crudReq.parsed.filter) {
        if (f.field === 'date' && f.operator === '$between') {
          const { startTime, endTime } = this.normalizeDateRangeForSingleDay(
            f.value[0],
            f.value[1],
            clientZoneName,
          );
          data.startTime = startTime;
          data.endTime = endTime;
        }
      }
    }

    const queryBuilder = this.invoicePaymentMethodRepo
      .createQueryBuilder('invoice_payment_method')
      .leftJoinAndSelect('invoice_payment_method.paymentMethod', 'method')
      .leftJoin(
        'invoice_invoice_payments_invoice_payment_method',
        'link',
        'link.invoicePaymentMethodId = invoice_payment_method.id',
      )
      .leftJoin('invoice', 'invoice', 'invoice.id = link.invoiceId')
      .where('invoice.status IN (:...statuses)', {
        statuses: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      });

    if (branchIds.length > 0) {
      queryBuilder.andWhere(
        'invoice_payment_method.branchId IN (:...branchIds)',
        {
          branchIds,
        },
      );
    }

    queryBuilder.andWhere('invoice.date BETWEEN :startTime AND :endTime', {
      startTime: data.startTime,
      endTime: data.endTime,
    });

    const paymentList = await queryBuilder.getMany();
    const paymentSummaryList = {};
    const methodList = await this.paymentMethodRepo.find({});
    let paymentSummary = 0;
    if (paymentList?.length > 0) {
      if (methodList?.length > 0) {
        for (const method of methodList) {
          const matchedPayments = paymentList.filter(
            (payment) => method?.id === payment?.paymentMethod?.id,
          );
          if (!paymentSummaryList[method.code]) {
            paymentSummaryList[method.code] = {
              items: method.name,
              total: 0,
            };
          }
          for (const payment of matchedPayments) {
            if (!paymentSummaryList[method.code]) {
              paymentSummaryList[method.code] = {
                items: method.name,
                total: 0,
              };
            }
            paymentSummaryList[method.code].total += payment?.paid ?? 0;
            paymentSummaryList[method.code].total = parseFloat(
              paymentSummaryList[method.code].total.toFixed(2),
            );
          }
          paymentSummary += paymentSummaryList[method.code].total;
        }
      }
    } else {
      if (methodList?.length > 0) {
        for (const method of methodList) {
          paymentSummaryList[method.code] = {
            items: method.name,
            total: 0,
          };
        }
      }
    }
    // add payment summary to pyamentSummaryList
    paymentSummaryList['sum'] = {
      items: 'Total payment',
      total: paymentSummary,
    };
    return Object.values(paymentSummaryList);
  }

  async getCustomerSummary(
    crudReq: CrudRequest,
    branchIds: string[],
    req?: any,
  ) {
    const whereClauseCaseCheckOut: Record<string, any> = {};
    const whereClauseCaseCheckIn: Record<string, any> = {};
    let searchDate = {};
    const data: any = {
      startTime: moment().startOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS'),
      endTime: moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS'),
    };
    const clientZoneName =
      req && req.headers && req.headers['timezone']
        ? req.headers['timezone']
        : UTCtimeZone;
    if (crudReq.parsed?.filter) {
      for (const f of crudReq.parsed.filter) {
        if (f.field === 'date' && f.operator === '$between') {
          const { startTime, endTime } = this.normalizeDateRangeForSingleDay(
            f.value[0],
            f.value[1],
            clientZoneName,
          );
          data.startTime = startTime;
          data.endTime = endTime;
        }
      }
    }
    searchDate = Between(data.startTime, data.endTime);
    if (branchIds.length > 0) {
      whereClauseCaseCheckOut.branch = {};
      whereClauseCaseCheckIn.branch = {};
      whereClauseCaseCheckOut.branch.id = In(branchIds);
      whereClauseCaseCheckIn.branch.id = In(branchIds);
    }

    whereClauseCaseCheckOut.created = searchDate;
    whereClauseCaseCheckOut.checkIn = Not(IsNull());
    whereClauseCaseCheckOut.checkOut = Not(IsNull());
    const checkOutApointment = await this.appointmentRepo.count({
      where: whereClauseCaseCheckOut,
    });

    whereClauseCaseCheckIn.created = searchDate;
    whereClauseCaseCheckIn.checkIn = Not(IsNull());
    whereClauseCaseCheckIn.checkOut = IsNull();
    const checkInApointment = await this.appointmentRepo.count({
      where: whereClauseCaseCheckIn,
    });

    return [
      {
        items: 'Current customers',
        total: checkInApointment,
      },
      {
        items: 'Customers checked out',
        total: checkOutApointment,
      },
      {
        items: 'sum',
        total: checkOutApointment + checkInApointment,
      },
    ];
  }

  async getCreditList(keySearch: string, branchIds: string[]) {
    const queryBuilder = this.customerRepo.createQueryBuilder('customer');
    queryBuilder.leftJoin('customer.credits', 'credits');
    queryBuilder.leftJoin('credits.branch', 'branch');
    queryBuilder.leftJoin('credits.creditSetting', 'creditSetting');

    if (keySearch !== undefined) {
      queryBuilder.andWhere(
        '(customer.firstName ILIKE :keySearch OR customer.lastName ILIKE :keySearch OR customer.phone ILIKE :keySearch)',
        { keySearch: `%${keySearch}%` },
      );
    }
    if (branchIds.length > 0) {
      queryBuilder.andWhere('credits.branch IN (:...branchIds)', { branchIds });
    }
    queryBuilder.andWhere('credits.id NOTNULL');
    queryBuilder
      .addSelect([
        'branch.id',
        'branch.name',
        'credits.id',
        'credits.creditBalance',
        'credits.status',
        'credits.expiryDate',
        'creditSetting.name',
        'creditSetting.creditType',
      ])
      .orderBy('customer.lastName', 'ASC')
      .addOrderBy('customer.lastName', 'ASC');
    const customerByCredits = await queryBuilder.getMany();
    return customerByCredits;
  }

  async getCurrentCustomer(
    crudReq: CrudRequest,
    branchIds: string[],
    keySearch: string,
    req?: any,
  ) {
    const whereClauseCaseCheckIn: Record<string, any> = {};
    let searchDate = {};
    const data: any = {
      startTime: moment().startOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS'),
      endTime: moment().endOf('day').format('YYYY-MM-DDTHH:mm:ss.SSS'),
    };
    let clientZoneName = UTCtimeZone;
    if (req && req.headers && req.headers['timezone']) {
      clientZoneName = req.headers['timezone'];
    }
    if (crudReq.parsed?.filter) {
      for (const f of crudReq.parsed.filter) {
        if (f.field === 'date' && f.operator === '$between') {
          const { startTime, endTime } = this.normalizeDateRangeForSingleDay(
            f.value[0],
            f.value[1],
            clientZoneName,
          );
          data.startTime = startTime;
          data.endTime = endTime;
        }
      }
    }
    searchDate = Between(data.startTime, data.endTime);
    if (branchIds.length > 0) {
      whereClauseCaseCheckIn.branch = {};
      whereClauseCaseCheckIn.branch.id = In(branchIds);
    }

    if (keySearch) {
      const [firstNameSearch, lastNameSearch] = keySearch.split(' ');
      if (!lastNameSearch) {
        whereClauseCaseCheckIn.customer = [
          { firstName: Like(`%${firstNameSearch}%`) },
          { lastName: Like(`%${firstNameSearch}%`) },
          { email: Like(`%${firstNameSearch}%`) },
          { phone: Like(`%${firstNameSearch}%`) },
        ];
      } else {
        whereClauseCaseCheckIn.customer = [
          {
            firstName: Like(`%${firstNameSearch}%`),
            lastName: Like(`%${lastNameSearch}%`),
          },
          {
            email: Like(`%${keySearch}%`),
          },
          {
            phone: Like(`%${keySearch}%`),
          },
        ];
      }
    }

    whereClauseCaseCheckIn.created = searchDate;
    whereClauseCaseCheckIn.checkIn = Not(IsNull());
    // whereClauseCaseCheckIn.checkOut = IsNull();
    const checkInApointment = await this.appointmentRepo.find({
      where: whereClauseCaseCheckIn,
      relations: ['customer', 'rfids', 'rfids.group'],
      order: {
        checkIn: 'DESC',
        checkOut: 'DESC',
      },
    });

    let resData = [];
    if (checkInApointment.length > 0) {
      const resDataPromises = checkInApointment.map(
        async ({ rfids, ...appoinment }) => {
          const rfid = await this.rfidRepo.findOne({
            where: {
              appointment: {
                id: appoinment.id,
              },
            },
            relations: ['group'],
          });

          return {
            ...appoinment,
            rfid: rfid ? rfid : rfids,
          };
        },
      );
      resData = await Promise.all(resDataPromises);
    }
    return {
      data: resData,
    };
  }
}
