import { ApiProperty } from '@nestjs/swagger';
import { DocEntity } from 'src/core/base/doc.entity';
import { Column, Entity, ManyToOne } from 'typeorm';
import { User } from '../user/user.entity';

export enum TypeToken {
  ACCESS_TOKEN = 'ACCESS_TOKEN',
  REFRESH_TOKEN = 'REFRESH_TOKEN',
  PASSWORD_TOKEN = 'PASSWORD_TOKEN',
}

@Entity()
export class Token extends DocEntity {
  @ManyToOne(() => User, (user) => user.tokens)
  @ApiProperty({
    type: () => User,
    example: null,
    properties: {
      alwaysOn: {
        default: 'user.id',
      },
    },
  })
  user: User;

  @Column({
    length: 256,
  })
  @ApiProperty()
  tokenString: string;

  @Column()
  @ApiProperty()
  expires: Date;

  @Column('enum', {
    enum: TypeToken,
  })
  @ApiProperty()
  type: string;
}
