import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { Token } from './token.entity';

@BaseCrud(
  {
    model: { type: Token },
    routes: {
      exclude: [
        'getManyBase',
        'getOneBase',
        'createOneBase',
        'createManyBase',
        'updateOneBase',
        'deleteOneBase',
      ],
    },
  },
  {
    // group: ResourceGroup.SYSTEM,
  },
)
export class TokenController {}
