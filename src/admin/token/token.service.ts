import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Repository } from 'typeorm';

import { Token } from './token.entity';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import * as bcrypt from 'bcryptjs';

@Injectable()
export class TokenService extends BaseCrudService<Token> {
  constructor(@InjectRepository(Token) repo: Repository<Token>) {
    super(repo);
  }

  async storeToken(
    req,
    token: string,
    userId: string,
    tokenType: string,
    expires: Date,
  ): Promise<string> {
    const tokenUser = await this.repo.findOne({
      where: {
        type: tokenType,
        user: {
          id: userId,
        },
      },
    });
    if (tokenUser?.id) {
      await this.repo.update(tokenUser?.id, {
        tokenString: token,
        expires,
        type: tokenType,
      });
      return tokenUser?.id;
    } else {
      const createdToken = await this.repo.insert({
        tokenString: token,
        expires,
        user: {
          id: userId,
        },
        type: tokenType,
      });
      return createdToken.identifiers?.[0]?.id;
    }
  }

  async revokeToken(userId: string) {
    return this.repo.delete({ user: { id: userId } });
  }

  async revokeUserTokens(userId: string, tokenType: string) {
    return this.repo.delete({
      user: {
        id: userId,
      },
      type: tokenType,
    });
  }

  async revokeUserTokensBySoftDelete(userId: string) {
    return this.repo.softDelete({
      user: {
        id: userId,
      },
    });
  }

  async getUserIfRefreshTokenMatches(refreshToken: string, userId: string) {
    const tokenUser = await this.repo.findOne({
      where: { user: { id: userId } },
    });

    const isRefreshTokenMatching = await bcrypt.compare(
      refreshToken,
      tokenUser.tokenString,
    );

    if (isRefreshTokenMatching) {
      return tokenUser;
    }
  }

  async getTokenByUserId(userId: string) {
    return await this.repo.findOne({
      where: { id: userId },
    });
  }

  async getUserIdByToken(token: string) {
    const data = await this.repo.findOne({
      relations: ['user'],
      where: { tokenString: token },
    });
    return data;
  }
}
