import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import { ILike, In, Repository } from 'typeorm';

import { ReportCouponQueryDTO } from './dto/report-coupon.dto';
import { Product } from 'src/admin/product/product.entity';
import { ProductType } from 'src/core/enums/entity';

@Injectable()
export class ReportCouponService {
  constructor(
    @InjectRepository(Product)
    private couponRepo: Repository<Product>,
  ) {}

  async getReport(
    req: CrudRequest,
    {
      keySearch,
      categoryId,
      statusId,
      branchIds,
      currencyCode,
    }: ReportCouponQueryDTO, // : Promise< //   GetManyDefaultResponse<IReportProductResponse> | IReportProductResponse[] // >
    isExport = false,
  ) {
    const { page, offset, limit, sort } = req?.parsed;

    const conditions = {};
    if (categoryId) {
      conditions['category.id'] = In(categoryId.split(','));
    }

    if (statusId) {
      conditions['status.id'] = In(statusId.split(','));
    }

    if (keySearch) {
      conditions['name'] = ILike(`%${keySearch}%`);
    }

    if (branchIds && branchIds.length > 0) {
      conditions['branches'] = {
        id: In(branchIds),
      };
    }

    const coupons = await this.couponRepo.find({
      where: { ...conditions, type: ProductType.COUPON },
      relations: ['category', 'discountType', 'status'],
      order: { name: 'ASC' },
    });

    if (isExport) {
      return {
        data: [
          {
            order: '',
            name: '',
            category: 'CATEGORY',
            time: 'PERIOD',
            discountType: {
              name: 'DISCOUNT',
            },
            discountValue: 'VALUE',
            price: `PRICE (${currencyCode})`,
          },
          ...coupons.map((c, index) => {
            let productName = c.name;
            if (c.status?.name === 'Inactive') {
              productName += '          Inactive';
            }
            return {
              order: index + 1,
              name: productName,
              category: c.category?.name,
              period: c.period,
              periodUnit: c.periodUnit,
              time: c.period + ' ' + c.periodUnit,
              status: {
                id: c.status?.id,
                name: c.status?.name,
              },
              discountValue: c.discountValue.toFixed(2),
              discountType: {
                id: c.discountType?.id,
                name: c.discountType?.name,
              },
              price: c.price.toFixed(2),
            };
          }),
        ],
      };
    }

    return {
      data: coupons.map((c, index) => {
        return {
          order: index + 1,
          name: c.name,
          category: c.category?.name,
          period: c.period,
          periodUnit: c.periodUnit,
          time: c.period + ' ' + c.periodUnit,
          status: {
            id: c.status?.id,
            name: c.status?.name,
          },
          discountValue: c.discountValue,
          discountType: {
            id: c.discountType?.id,
            name: c.discountType?.name,
          },
          price: c.price,
        };
      }),
      total: coupons.length,
    };
  }
}
