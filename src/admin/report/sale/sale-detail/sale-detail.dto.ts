import { ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { ReportQueryDTO } from '../../dto/report.dto';

export class ReportSaleDetailQueryDTO extends PickType(ReportQueryDTO, [
  'startDate',
  'endDate',
  'branchIds',
  'clientZoneName',
  'currencyCode',
]) {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  customerReference?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keySearch?: string;
}
