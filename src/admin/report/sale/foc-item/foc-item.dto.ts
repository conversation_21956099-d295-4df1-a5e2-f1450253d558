import { ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { IsDateString, IsOptional, IsString } from 'class-validator';
import { ReportQueryDTO } from '../../dto/report.dto';

export class ReportFocItemQueryDTO extends PickType(ReportQueryDTO, [
  'branchIds',
  'clientZoneName',
  'currencyCode',
]) {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  searchText?: string;

  @ApiPropertyOptional()
  @IsDateString()
  @IsOptional()
  purchaseStartDate?: string;

  @ApiPropertyOptional()
  @IsDateString()
  @IsOptional()
  purchaseEndDate?: string;
}
