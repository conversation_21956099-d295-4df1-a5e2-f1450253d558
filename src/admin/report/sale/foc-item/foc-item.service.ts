import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import * as moment from 'moment-timezone';
import { OrderDetail } from 'src/admin/order-detail/order-detail.entity';
import { ReportFocItemQueryDTO } from './foc-item.dto';
import { InvoiceStatus } from 'src/core/enums/entity';

@Injectable()
export class ReportFocItemService {
  constructor(
    @InjectRepository(OrderDetail)
    private orderDetailRepo: Repository<OrderDetail>,
  ) {}

  async getReport(
    req: CrudRequest,
    {
      purchaseStartDate,
      purchaseEndDate,
      branchIds,
      clientZoneName,
      searchText,
    }: ReportFocItemQueryDTO,
    isExport = false,
  ) {
    const focItems = await this.getFocItems(
      purchaseStartDate,
      purchaseEndDate,
      branchIds,
      searchText,
      clientZoneName,
    );

    let sumQty = 0;
    let sumValue = 0; // FOC items always have value = 0
    for (const item of focItems) {
      sumQty += Number(item.qty) || 0;
      sumValue += Number(item.value) || 0;
    }

    if (isExport) {
      const grandTotal = {
        lineNumber: '',
        date: 'GRAND TOTAL:',
        referenceNo: '',
        customer: '',
        employee: '',
        item: '',
        qty: sumQty,
        value: sumValue.toFixed(2),
      };
      return [...focItems, grandTotal];
    }

    return {
      data: focItems,
      sumQty: sumQty,
      sumValue: sumValue.toFixed(2), // Add sumValue to match design
    };
  }

  private async getFocItems(
    startDate: string,
    endDate: string,
    branchIds: string[],
    searchText?: string,
    clientZoneName?: string,
  ) {
    let query = this.orderDetailRepo
      .createQueryBuilder('orderDetail')
      .leftJoinAndSelect('orderDetail.order', 'order')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('order.branch', 'branch')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .leftJoinAndSelect('orderDetail.employees', 'employee')
      .where('invoice.created BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('invoice.status = :status', {
        status: InvoiceStatus.PAID,
      })
      .andWhere('orderDetail.price = :price', { price: 0 })
      .andWhere('product.type = :productType', {
        productType: 'coupon',
      });

    if (branchIds && branchIds.length > 0) {
      query = query.andWhere('branch.id IN (:...branchIds)', {
        branchIds,
      });
    }

    if (searchText) {
      query = query.andWhere(
        '(customer.code LIKE :search OR invoice.code LIKE :search)',
        { search: `%${searchText}%` },
      );
    }

    const orderDetails = await query
      .orderBy('invoice.created', 'ASC')
      .addOrderBy('invoice.id', 'ASC')
      .getMany();

    const focItems = [];
    let lineCount = 1;

    for (const detail of orderDetails) {
      const invoice = detail.order.invoice;
      const customer = invoice.customer;
      const formattedDate = moment(invoice.created)
        .tz(clientZoneName || 'Asia/Ho_Chi_Minh')
        .format('DD/MM/YYYY hh:mm A');

      let employeeName = '';
      if (detail.employees && detail.employees.length > 0) {
        employeeName = detail.employees[0].fullName || '';
      }

      focItems.push({
        lineNumber: lineCount++,
        date: formattedDate,
        referenceNo: invoice.code ? `IN${invoice.code}` : '',
        customer: customer
          ? `${customer.code} ${customer.firstName} ${customer.lastName}`
          : '',
        employee: employeeName,
        item: detail.product ? detail.product.name : '',
        qty: detail.quantity || 0,
        value: '0.00',
      });
    }

    return focItems;
  }
}
