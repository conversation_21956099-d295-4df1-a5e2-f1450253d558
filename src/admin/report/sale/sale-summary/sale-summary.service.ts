import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import { Repository } from 'typeorm';
import { Invoice } from 'src/admin/invoice/invoice.entity';
import { ReportSaleSummaryQueryDTO } from './sale-summary.dto';
import Decimal from 'decimal.js';
import { InvoicePaymentMethod } from 'src/admin/invoice/invoice-payment-method.entity';
import { InvoiceStatus } from 'src/core/enums/entity';

@Injectable()
export class ReportSaleSummaryService {
  constructor(
    @InjectRepository(Invoice)
    private invoiceRepo: Repository<Invoice>,
    @InjectRepository(InvoicePaymentMethod)
    private invoicePaymentMethodRepo: Repository<InvoicePaymentMethod>,
  ) {}

  async getReport(
    req: CrudRequest,
    {
      startDate,
      endDate,
      branchIds,
      clientZoneName,
      summarizeBy,
      currencyCode,
    }: ReportSaleSummaryQueryDTO,
    isExport = false,
  ) {
    let dateFormat;
    if (summarizeBy === 'week') {
      dateFormat = 'IYYY-IW'; // ISO Year and ISO Week number
    } else if (summarizeBy === 'month') {
      dateFormat = 'YYYY-MM'; // Year and Month
    } else if (summarizeBy === 'year') {
      dateFormat = 'YYYY'; // Year
    }

    const builderQueryPayment = this.invoicePaymentMethodRepo
      .createQueryBuilder('invoice_payment_method')
      .select([
        `TO_CHAR(invoice_payment_method.created AT TIME ZONE :clientZoneName, '${dateFormat}') AS formatted_date`, // Dynamic date format based on summarizeBy
        `JSON_AGG(JSON_BUILD_OBJECT('method_name', method.name, 'total_paid', invoice_payment_method.paid, 'type', method.type)) AS payment_methods`,
      ])
      .setParameter('clientZoneName', clientZoneName)
      .where('invoice.created BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('invoice.status IN  (:...statusArr)', {
        statusArr: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      })
      .leftJoin('invoice_payment_method.paymentMethod', 'method')
      .leftJoin(
        'invoice_invoice_payments_invoice_payment_method',
        'link',
        'link.invoicePaymentMethodId = invoice_payment_method.id',
      )
      .leftJoin('invoice', 'invoice', 'invoice.id = link.invoiceId')
      .groupBy('formatted_date') // Group by the dynamically formatted date
      .orderBy('formatted_date', 'ASC');

    if (branchIds && branchIds.length > 0) {
      builderQueryPayment.andWhere(
        'invoice_payment_method."branchId" IN (:...branchIds)',
        {
          branchIds,
        },
      );
    }

    const paymentData = await builderQueryPayment.getRawMany();

    const sumTotalPaidByMethod = (paymentData) => {
      return paymentData.map(({ formatted_date, payment_methods }) => {
        // Create an object to store sum of total_paid by method_name
        const totalsByMethod = payment_methods.reduce((acc, method) => {
          const { method_name, total_paid } = method;
          // If method_name exists in acc, add to the total, otherwise set the initial value
          acc[method_name] = (acc[method_name] || 0) + total_paid;
          return acc;
        }, {});

        return {
          formatted_date,
          payment_methods_summarized: totalsByMethod,
        };
      });
    };
    const sumTotalPaidByType = (data) => {
      return data.map(({ formatted_date, payment_methods }) => {
        const totalsByType = payment_methods.reduce((acc, method) => {
          const { type, total_paid, method_name } = method;

          // Check if it's 'Credits' and type is 'others', group it under 'Credits'
          if (type === 'others' && method_name === 'Credits') {
            acc['credit'] = (acc['credit'] || 0) + total_paid;
          } else {
            // Group by type for other methods
            acc[type] = (acc[type] || 0) + total_paid;
          }
          return acc;
        }, {});

        return {
          formatted_date,
          payment_methods_summarized: totalsByType,
        };
      });
    };

    const summarizedData = sumTotalPaidByMethod(paymentData);
    const summarizedDataByType = sumTotalPaidByType(paymentData);
    let grandTotal: any = {
      saleTotal: new Decimal(0),
      received: new Decimal(0),
      charge: new Decimal(0),
      netReceived: new Decimal(0),
      cash: new Decimal(0),
      card: new Decimal(0),
      others: new Decimal(0),
      credit: new Decimal(0),
      due: new Decimal(0),
      formatted_date: 'grand_total',
      paymentTotal: new Decimal(0),
      foc: new Decimal(0),
    };
    const reportData = summarizedDataByType.map((data) => {
      const { formatted_date, payment_methods_summarized } = data;
      const {
        cash = 0,
        card = 0,
        others = 0,
        credit = 0,
        due = 0,
      } = payment_methods_summarized;

      const received = cash + card + others;
      const netReceived = received / 1.09;
      const saleTotal = received;
      const paymentTotal = 0;
      const charge = 0;
      const foc = 0;
      let formatDate = '';
      if (summarizeBy === 'week') {
        const [year, week] = formatted_date.split('-');
        formatDate = `Week ${week}, ${year}`;
      } else if (summarizeBy === 'month') {
        const [year, month] = formatted_date.split('-');
        const monthNames = [
          'Jan',
          'Feb',
          'Mar',
          'Apr',
          'May',
          'Jun',
          'Jul',
          'Aug',
          'Sep',
          'Oct',
          'Nov',
          'Dec',
        ];
        formatDate = `${monthNames[parseInt(month, 10) - 1]}, ${year}`;
      } else if (summarizeBy === 'year') {
        formatDate = formatted_date;
      }

      // grand total
      grandTotal.saleTotal = new Decimal(grandTotal.saleTotal)
        .plus(saleTotal)
        .toFixed(2);
      grandTotal.received = new Decimal(grandTotal.received)
        .plus(received)
        .toFixed(2);
      grandTotal.netReceived = new Decimal(grandTotal.netReceived)
        .plus(netReceived)
        .toFixed(2);
      grandTotal.cash = new Decimal(grandTotal.cash).plus(cash).toFixed(2);
      grandTotal.card = new Decimal(grandTotal.card).plus(card).toFixed(2);
      grandTotal.others = new Decimal(grandTotal.others)
        .plus(others)
        .toFixed(2);
      grandTotal.credit = new Decimal(grandTotal.credit)
        .plus(credit)
        .toFixed(2);
      grandTotal.due = new Decimal(grandTotal.due).plus(due).toFixed(2);
      grandTotal.paymentTotal = new Decimal(grandTotal.paymentTotal)
        .plus(paymentTotal)
        .toFixed(2);
      grandTotal.charge = new Decimal(grandTotal.charge)
        .plus(charge)
        .toFixed(2);
      grandTotal.foc = new Decimal(grandTotal.foc).plus(foc).toFixed(2);

      return {
        formatted_date: formatDate,
        received: received.toFixed(2),
        netReceived: netReceived.toFixed(2),
        cash: cash.toFixed(2),
        card: card.toFixed(2),
        others: others.toFixed(2),
        credit: credit.toFixed(2),
        due: due.toFixed(2),
        paymentTotal: paymentTotal.toFixed(2),
        saleTotal: saleTotal.toFixed(2),
        foc: foc.toFixed(2),
        charge: charge.toFixed(2),
      };
    });

    // Exclude 'grand_total' row for min/max/average calculations
    const dataRows = reportData.filter(
      (row) => row.formatted_date !== 'grand_total',
    );

    // Find max/min of saleTotal in dataRows, return the full row
    const maxSaleTotal = dataRows.reduce((acc, curr) => {
      return parseFloat(acc.saleTotal) > parseFloat(curr.saleTotal)
        ? acc
        : curr;
    }, dataRows[0]);
    const minSaleTotal = dataRows.reduce((acc, curr) => {
      return parseFloat(acc.saleTotal) < parseFloat(curr.saleTotal)
        ? acc
        : curr;
    }, dataRows[0]);

    // Get average of saleTotal in dataRows
    const averageSale = (
      dataRows.reduce((acc, curr) => {
        return acc + parseFloat(curr.saleTotal);
      }, 0) / (dataRows.length || 1)
    ).toFixed(2);

    // get summary for payment details
    const paymentMethodList = await this.invoicePaymentMethodRepo
      .createQueryBuilder('invoice_payment_method')
      .select(['method.name'])
      .leftJoin('invoice_payment_method.paymentMethod', 'method')
      .groupBy('method.name')
      .getRawMany();

    const paymentDetail = paymentMethodList
      .filter((paymentMethod) => paymentMethod.method_name) // Filter out empty method_name
      .map((paymentMethod) => {
        const totalPaid = summarizedData.reduce((acc, data) => {
          const payment =
            data.payment_methods_summarized[paymentMethod.method_name];
          return acc + (payment ? Number(payment) : 0);
        }, 0);
        return {
          method_name: paymentMethod.method_name,
          total_paid: totalPaid.toFixed(2),
        };
      });
    if (isExport) {
      grandTotal.formatted_date = 'GRAND TOTAL:';
      reportData.push(grandTotal);
      let exportData = [];
      exportData = reportData;
      exportData.push(
        { formatted_date: '' },
        { formatted_date: 'Sale Total Summary' },
        {
          formatted_date: 'Highest Sale:',
          saleTotal:
            `${currencyCode} ` +
            maxSaleTotal.saleTotal +
            ` (${maxSaleTotal.formatted_date})`,
        },
        {
          formatted_date: 'Lowest Sale:',
          saleTotal:
            `${currencyCode} ` +
            minSaleTotal.saleTotal +
            ` (${minSaleTotal.formatted_date})`,
        },
        {
          formatted_date: 'Average:',
          saleTotal: `${currencyCode} ` + averageSale,
        },
        { formatted_date: '' },
        { formatted_date: 'Payment Details' },
      );
      paymentDetail.forEach((payment) => {
        exportData.push({
          formatted_date: payment.method_name,
          saleTotal: `${currencyCode} ` + payment.total_paid,
        });
      });

      return exportData;
    }
    reportData.push(grandTotal);
    return {
      data: reportData,
      hight_sale: maxSaleTotal,
      lowest_sale: minSaleTotal,
      average_sale: averageSale,
      payment_detail: paymentDetail,
    };
  }
}
