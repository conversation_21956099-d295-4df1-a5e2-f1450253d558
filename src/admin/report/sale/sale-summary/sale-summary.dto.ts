import { ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID } from 'class-validator';
import { ReportQueryDTO } from '../../dto/report.dto';
import { UUID } from 'crypto';
import { ProductType } from 'src/core/enums/entity';

export type SummarizeBy = 'week' | 'month' | 'year';

export class ReportSaleSummaryQueryDTO extends PickType(ReportQueryDTO, [
  'startDate',
  'endDate',
  'branchIds',
  'clientZoneName',
  'currencyCode'
]) {

  @ApiPropertyOptional({ enum: ['week', 'month', 'year'] })
  summarizeBy: SummarizeBy;
}
