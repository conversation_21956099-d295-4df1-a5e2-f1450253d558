import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import { Repository } from 'typeorm';

import { ReportDailySaleSummaryQueryDTO } from './daily-received-sumary.dto';
import { Invoice } from 'src/admin/invoice/invoice.entity';
import { InvoicePaymentMethod } from 'src/admin/invoice/invoice-payment-method.entity';
import Decimal from 'decimal.js';
import { InvoiceStatus } from 'src/core/enums/entity';
import { PaymentMethod } from 'src/admin/payment-method/payment-method.entity';

@Injectable()
export class ReportDailySaleSummaryService {
  constructor(
    @InjectRepository(Invoice)
    private invoiceRepo: Repository<Invoice>,
    @InjectRepository(InvoicePaymentMethod)
    private invoicePaymentMethodRepo: Repository<InvoicePaymentMethod>,
    @InjectRepository(PaymentMethod)
    private paymentMethodRepo: Repository<PaymentMethod>,
  ) {}

  async getReport(
    req: CrudRequest,
    {
      startDate,
      endDate,
      branchIds,
      clientZoneName,
      currencyCode,
    }: ReportDailySaleSummaryQueryDTO,
    isExport = false,
  ) {
    const builderQueryPayment = this.invoicePaymentMethodRepo
      .createQueryBuilder('invoice_payment_method')
      .select([
        `TO_CHAR(invoice.date AT TIME ZONE :clientZoneName, 'DD-MM-YYYY') AS formatted_date`,
        `JSON_AGG(JSON_BUILD_OBJECT('method_name', method.name, 'total_paid', invoice_payment_method.paid, 'type', method.type)) AS payment_methods`,
      ])
      .setParameter('clientZoneName', clientZoneName)
      .where('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('invoice.status IN  (:...statusArr)', {
        statusArr: [InvoiceStatus.PAID, InvoiceStatus.PART_PAID],
      })
      .leftJoin('invoice_payment_method.paymentMethod', 'method')
      .leftJoin(
        'invoice_invoice_payments_invoice_payment_method',
        'link',
        'link.invoicePaymentMethodId = invoice_payment_method.id',
      )
      .leftJoin('invoice', 'invoice', 'invoice.id = link.invoiceId')
      .groupBy('formatted_date')
      .orderBy('formatted_date', 'ASC');

    if (branchIds && branchIds.length > 0) {
      builderQueryPayment.andWhere(
        'invoice_payment_method."branchId" IN (:...branchIds)',
        {
          branchIds,
        },
      );
    }

    const paymentData = await builderQueryPayment.getRawMany();

    const groupedData = paymentData.map((day) => {
      const paymentMethodSums = day.payment_methods.reduce((acc, payment) => {
        const { method_name, total_paid } = payment;

        if (!acc[method_name]) {
          acc[method_name] = 0;
        }
        acc[method_name] += total_paid;
        return acc;
      }, {});

      const payment_methods = Object.entries(paymentMethodSums).map(
        ([method_name, total_paid]) => ({
          method_name,
          total_paid,
        }),
      );
      return {
        formatted_date: day.formatted_date,
        payment_methods,
      };
    });
    const grandTotal: any = {
      saleTotal: 0,
      received: 0,
      tax: 0,
      taxReceived: 0,
      charge: 0,
      netReceived: 0,
      cash: 0,
      card: 0,
      others: 0,
      credit: 0,
      due: 0,
      date: 'grand_total',
      total_invoice: 0,
      paymentTotal: 0,
      credits: 0,
      old_credits: 0,
      visa: 0,
      master: 0,
      amex: 0,
      shopify: 0,
      paynow: 0,
      nets: 0,
      capital_star: 0,
    };
    const filterDataPromises = groupedData.map(async (data) => {
      const date = data.formatted_date;
      let cash = new Decimal(0);
      let others = new Decimal(0);
      let credit = new Decimal(0);
      let card = new Decimal(0);
      let saleTotal = new Decimal(0);
      let paymentTotal = new Decimal(0);
      let tax = new Decimal(0);
      let taxReceived = new Decimal(0);
      let netReceived = new Decimal(0);
      let received = new Decimal(0);
      const due = new Decimal(0);
      const charge = new Decimal(0);
      let credits = new Decimal(0);
      let old_credits = new Decimal(0);
      let visa = new Decimal(0);
      let master = new Decimal(0);
      let amex = new Decimal(0);
      let shopify = new Decimal(0);
      let paynow = new Decimal(0);
      let nets = new Decimal(0);
      let capital_star = new Decimal(0);

      data.payment_methods.forEach((payment) => {
        if (['Master', 'AMEX', 'Visa', 'Nets'].includes(payment.method_name)) {
          card = card.plus(new Decimal(payment.total_paid as number));
          if (payment.method_name === 'Visa') {
            visa = visa.plus(new Decimal(payment.total_paid as number));
          }
          if (payment.method_name === 'Master') {
            master = master.plus(new Decimal(payment.total_paid as number));
          }
          if (payment.method_name === 'AMEX') {
            amex = amex.plus(new Decimal(payment.total_paid as number));
          }
          if (payment.method_name === 'Nets') {
            nets = nets.plus(new Decimal(payment.total_paid as number));
          }
        } else if (payment.method_name === 'Cash') {
          cash = cash.plus(new Decimal(payment.total_paid as number));
        } else if (payment.method_name === 'Credits') {
          credit = credit.plus(new Decimal(payment.total_paid as number));
          credits = credits.plus(new Decimal(payment.total_paid as number));
        } else {
          others = others.plus(new Decimal(payment.total_paid as number));
          if (payment.method_name === 'Shopify') {
            shopify = shopify.plus(new Decimal(payment.total_paid as number));
          }
          if (payment.method_name === 'PayNow') {
            paynow = paynow.plus(new Decimal(payment.total_paid as number));
          }
          if (payment.method_name === 'Capital Star') {
            capital_star = capital_star.plus(
              new Decimal(payment.total_paid as number),
            );
          }
          if (payment.method_name === 'Old Credits') {
            old_credits = old_credits.plus(
              new Decimal(payment.total_paid as number),
            );
          }
        }
      });
      saleTotal = cash.plus(card).plus(others);
      paymentTotal = new Decimal(0);
      received = saleTotal;
      netReceived = new Decimal(received.div(1.09));
      tax = received.minus(netReceived);
      taxReceived = tax;

      //grand total
      grandTotal.saleTotal = new Decimal(grandTotal.saleTotal).plus(saleTotal);

      grandTotal.received = new Decimal(grandTotal.received).plus(received);

      grandTotal.tax = new Decimal(grandTotal.tax).plus(tax);
      grandTotal.taxReceived = new Decimal(grandTotal.taxReceived).plus(
        taxReceived,
      );

      grandTotal.charge = new Decimal(grandTotal.charge).plus(charge);

      grandTotal.netReceived = new Decimal(grandTotal.netReceived).plus(
        netReceived,
      );
      grandTotal.cash = new Decimal(grandTotal.cash).plus(cash);
      grandTotal.card = new Decimal(grandTotal.card).plus(card);
      grandTotal.others = new Decimal(grandTotal.others).plus(others);

      grandTotal.credit = new Decimal(grandTotal.credit).plus(credit);

      grandTotal.due = new Decimal(grandTotal.due).plus(0);
      grandTotal.paymentTotal = new Decimal(grandTotal.paymentTotal).plus(
        paymentTotal,
      );
      grandTotal.credits = new Decimal(grandTotal.credits).plus(credits);
      grandTotal.old_credits = new Decimal(grandTotal.old_credits).plus(
        old_credits,
      );
      grandTotal.visa = new Decimal(grandTotal.visa).plus(visa);
      grandTotal.master = new Decimal(grandTotal.master).plus(master);
      grandTotal.amex = new Decimal(grandTotal.amex).plus(amex);
      grandTotal.shopify = new Decimal(grandTotal.shopify).plus(shopify);
      grandTotal.paynow = new Decimal(grandTotal.paynow).plus(paynow);
      grandTotal.nets = new Decimal(grandTotal.nets).plus(nets);
      grandTotal.capital_star = new Decimal(grandTotal.capital_star).plus(
        capital_star,
      );

      const builderQuery = this.invoiceRepo
        .createQueryBuilder('invoice')
        .select(['COUNT(invoice.id) AS total_invoice'])
        .where(
          'TO_CHAR(invoice.date AT TIME ZONE :clientZoneName, :format) = :date',
          {
            date,
            format: 'DD-MM-YYYY',
            clientZoneName,
          },
        );
      if (branchIds && branchIds.length > 0) {
        builderQuery.andWhere('invoice."branchId" IN (:...branchIds)', {
          branchIds,
        });
      }
      const totalInvoice = await builderQuery.getRawOne();
      grandTotal.total_invoice = new Decimal(grandTotal.total_invoice).plus(
        totalInvoice?.total_invoice,
      );
      return {
        date: date ? date.replace(/-/g, '/') : '',
        total_invoice: totalInvoice?.total_invoice
          ? totalInvoice.total_invoice
          : 0,
        cash: cash.toFixed(2),
        card: card.toFixed(2),
        others: others.toFixed(2),
        credit: credit.toFixed(2),
        saleTotal: saleTotal.toFixed(2),
        paymentTotal: paymentTotal.toFixed(2),
        tax: tax.toFixed(2),
        taxReceived: taxReceived.toFixed(2),
        charge: charge.toFixed(2),
        netReceived: netReceived.toFixed(2),
        received: received.toFixed(2),
        due: due.toFixed(2),
        credits: credits.toFixed(2),
        old_credits: old_credits.toFixed(2),
        visa: visa.toFixed(2),
        master: master.toFixed(2),
        amex: amex.toFixed(2),
        shopify: shopify.toFixed(2),
        paynow: paynow.toFixed(2),
        nets: nets.toFixed(2),
        capital_star: capital_star.toFixed(2),
      };
    });

    //map filter by date field ASC filterData
    const filterData = await Promise.all(filterDataPromises);
    filterData.sort((a, b) => {
      return new Date(a.date).getTime() - new Date(b.date).getTime();
    });

    // get summary for payment details
    const paymentMethodList = await this.getPaymentMethodList();
    const paymentDetail = paymentMethodList.map((paymentMethod) => {
      const totalPaid = groupedData.reduce((acc, data) => {
        const payment = data.payment_methods.find(
          (payment) => payment.method_name === paymentMethod.name,
        );
        return acc + (payment ? Number(payment.total_paid) : 0);
      }, 0);
      return {
        method_name: paymentMethod.name,
        total_paid: totalPaid.toFixed(2),
      };
    });

    if (isExport) {
      grandTotal.date = 'GRAND TOTAL:';
      filterData.push({
        ...grandTotal,
        saleTotal: grandTotal.saleTotal.toFixed(2),
        netReceived: grandTotal.netReceived.toFixed(2),
        received: grandTotal.received.toFixed(2),
        tax: grandTotal.tax.toFixed(2),
        taxReceived: grandTotal.taxReceived.toFixed(2),
        charge: grandTotal.charge.toFixed(2),
        cash: grandTotal.cash.toFixed(2),
        card: grandTotal.card.toFixed(2),
        others: grandTotal.others.toFixed(2),
        credit: grandTotal.credit.toFixed(2),
        due: grandTotal.due.toFixed(2),
        paymentTotal: grandTotal.paymentTotal.toFixed(2),
        credits: grandTotal.credits.toFixed(2),
        old_credits: grandTotal.old_credits.toFixed(2),
        visa: grandTotal.visa.toFixed(2),
        master: grandTotal.master.toFixed(2),
        amex: grandTotal.amex.toFixed(2),
        shopify: grandTotal.shopify.toFixed(2),
        paynow: grandTotal.paynow.toFixed(2),
        nets: grandTotal.nets.toFixed(2),
        capital_star: grandTotal.capital_star.toFixed(2),
      });
      filterData.push({
        date: '',
        total_invoice: '',
        cash: '',
        card: '',
        others: '',
        credit: '',
        saleTotal: '',
        paymentTotal: '',
        tax: '',
        taxReceived: '',
        charge: '',
        netReceived: '',
        received: '',
        due: '',
        credits: '',
        old_credits: '',
        visa: '',
        master: '',
        amex: '',
        shopify: '',
        paynow: '',
        nets: '',
        capital_star: '',
      });
      if (paymentDetail.length > 0) {
        filterData.push({
          date: '',
          total_invoice: '',
          cash: '',
          card: '',
          others: '',
          credit: '',
          saleTotal: '',
          paymentTotal: '',
          tax: 'Payment Details',
          taxReceived: '',
          charge: '',
          netReceived: '',
          received: '',
          due: '',
          credits: '',
          old_credits: '',
          visa: '',
          master: '',
          amex: '',
          shopify: '',
          paynow: '',
          nets: '',
          capital_star: '',
        });
        paymentDetail.forEach((payment) => {
          filterData.push({
            date: '',
            total_invoice: '',
            cash: '',
            card: '',
            others: '',
            credit: '',
            saleTotal: '',
            paymentTotal: '',
            tax: payment.method_name,
            taxReceived: `${currencyCode} ` + payment.total_paid,
            charge: '',
            netReceived: '',
            received: '',
            due: '',
            credits: '',
            old_credits: '',
            visa: '',
            master: '',
            amex: '',
            shopify: '',
            paynow: '',
            nets: '',
            capital_star: '',
          });
        });
      }
      return filterData;
    }
    filterData.push({
      ...grandTotal,
      saleTotal: grandTotal.saleTotal.toFixed(2),
      netReceived: grandTotal.netReceived.toFixed(2),
      received: grandTotal.received.toFixed(2),
      tax: grandTotal.tax.toFixed(2),
      taxReceived: grandTotal.taxReceived.toFixed(2),
      charge: grandTotal.charge.toFixed(2),
      cash: grandTotal.cash.toFixed(2),
      card: grandTotal.card.toFixed(2),
      others: grandTotal.others.toFixed(2),
      credit: grandTotal.credit.toFixed(2),
      due: grandTotal.due.toFixed(2),
      paymentTotal: grandTotal.paymentTotal.toFixed(2),
      credits: grandTotal.credits.toFixed(2),
      old_credits: grandTotal.old_credits.toFixed(2),
      visa: grandTotal.visa.toFixed(2),
      master: grandTotal.master.toFixed(2),
      amex: grandTotal.amex.toFixed(2),
      shopify: grandTotal.shopify.toFixed(2),
      paynow: grandTotal.paynow.toFixed(2),
      nets: grandTotal.nets.toFixed(2),
      capital_star: grandTotal.capital_star.toFixed(2),
    });
    return {
      data: filterData.length > 1 ? filterData : [],
      payment_detail: paymentDetail,
    };
  }

  async getPaymentMethodList() {
    const paymentMethodList = await this.paymentMethodRepo
      .createQueryBuilder('payment_method')
      .select(['name', 'code'])
      .orderBy('created', 'DESC')
      .getRawMany();
    return paymentMethodList;
  }
}
