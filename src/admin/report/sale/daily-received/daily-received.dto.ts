import { ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID } from 'class-validator';
import { ReportQueryDTO } from '../../dto/report.dto';
import { UUID } from 'crypto';
import { ProductType } from 'src/core/enums/entity';

export class ReportDailySaleSummaryQueryDTO extends PickType(ReportQueryDTO, [
  'startDate',
  'endDate',
  'branchIds',
  'clientZoneName'
]) {}
