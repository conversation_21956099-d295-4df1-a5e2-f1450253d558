import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsDateString, IsOptional, IsString } from 'class-validator';

export class SerialNumberTrackingQueryDTO {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  searchText?: string;

  @ApiPropertyOptional()
  @IsDateString()
  @IsOptional()
  purchaseStartDate?: string;

  @ApiPropertyOptional()
  @IsDateString()
  @IsOptional()
  purchaseEndDate?: string;

  @ApiPropertyOptional()
  @IsDateString()
  @IsOptional()
  redeemedStartDate?: string;

  @ApiPropertyOptional()
  @IsDateString()
  @IsOptional()
  redeemedEndDate?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  clientZoneName?: string;

  @ApiPropertyOptional()
  branchIds?: string[];
}
