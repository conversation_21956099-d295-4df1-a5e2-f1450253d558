import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import * as moment from 'moment-timezone';
import { OrderDetail } from 'src/admin/order-detail/order-detail.entity';
import { SerialNumberTrackingQueryDTO } from './serial-number-tracking.dto';
import { InvoiceStatus, ProductType } from 'src/core/enums/entity';
import { Invoice } from 'src/admin/invoice/invoice.entity';

@Injectable()
export class SerialNumberTrackingService {
  private readonly logger = new Logger(SerialNumberTrackingService.name);

  constructor(
    @InjectRepository(OrderDetail)
    private orderDetailRepo: Repository<OrderDetail>,
    @InjectRepository(Invoice)
    private invoiceRepo: Repository<Invoice>,
  ) {}

  async getReport(
    req: CrudRequest,
    {
      purchaseStartDate,
      purchaseEndDate,
      redeemedStartDate,
      redeemedEndDate,
      branchIds,
      clientZoneName,
      searchText,
    }: SerialNumberTrackingQueryDTO,
    isExport = false,
  ) {
    const soldCoupons = await this.getSoldCoupons(
      purchaseStartDate,
      purchaseEndDate,
      branchIds,
      searchText,
      clientZoneName,
    );

    const redeemedCoupons = await this.getRedeemedCoupons(
      redeemedStartDate,
      redeemedEndDate,
      branchIds,
      searchText,
      clientZoneName,
    );

    const mergedData = this.mergeCouponData(soldCoupons, redeemedCoupons);

    if (isExport) {
      return [
        ...mergedData,
        {
          lineNumber: '',
          coupon: 'GRAND TOTAL:',
          serialNumber: '',
          soldDate: '',
          soldCustomer: '',
          soldReferenceCode: '',
          usedDate: '',
          usedCustomer: '',
          usedReferenceCode: '',
        },
      ];
    }

    return {
      data: mergedData,
      totalSold: soldCoupons.length,
      totalUsed: redeemedCoupons.length,
    };
  }

  private async getSoldCoupons(
    startDate: string,
    endDate: string,
    branchIds: string[],
    searchText?: string,
    clientZoneName?: string,
  ) {
    let query = this.orderDetailRepo
      .createQueryBuilder('orderDetail')
      .leftJoinAndSelect('orderDetail.order', 'order')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('order.branch', 'branch')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .where('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('invoice.status = :status', {
        status: InvoiceStatus.PAID,
      })
      .andWhere('product.type = :productType', {
        productType: ProductType.COUPON,
      });

    if (branchIds && branchIds.length > 0) {
      query = query.andWhere('branch.id IN (:...branchIds)', {
        branchIds,
      });
    }

    if (searchText) {
      query = query.andWhere('(orderDetail.couponCode LIKE :search)', {
        search: `%${searchText}%`,
      });
    }

    const orderDetails = await query.getMany();

    return orderDetails.map((detail, index) => {
      const invoice = detail.order.invoice;
      const customer = invoice.customer;
      const formattedDate = moment(invoice.date)
        .tz(clientZoneName || 'Asia/Ho_Chi_Minh')
        .format('DD/MM/YYYY hh:mm A');

      return {
        lineNumber: index + 1,
        coupon: detail.product.name,
        serialNumber: detail.couponCode,
        soldDate: formattedDate,
        soldCustomer: customer
          ? `${customer.code} ${customer.firstName} ${customer.lastName}`
          : '',
        soldReferenceCode: invoice.code ? `IN${invoice.code}` : '',
        usedDate: '',
        usedCustomer: '',
        usedReferenceCode: '',
      };
    });
  }

  private async getRedeemedCoupons(
    startDate: string,
    endDate: string,
    branchIds: string[],
    searchText?: string,
    clientZoneName?: string,
  ) {
    let query = this.orderDetailRepo
      .createQueryBuilder('orderDetail')
      .leftJoinAndSelect('orderDetail.order', 'order')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .leftJoinAndSelect('order.branch', 'branch')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .where('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('invoice.status = :status', {
        status: InvoiceStatus.PAID,
      })
      .andWhere('orderDetail.couponCode IS NOT NULL')
      .andWhere('product.type != :productType', {
        productType: ProductType.COUPON,
      });

    if (branchIds && branchIds.length > 0) {
      query = query.andWhere('branch.id IN (:...branchIds)', {
        branchIds,
      });
    }

    if (searchText) {
      query = query.andWhere('(orderDetail.couponCode LIKE :search)', {
        search: `%${searchText}%`,
      });
    }

    const orderDetails = await query.getMany();

    return orderDetails.map((detail) => ({
      couponCode: detail.couponCode,
      date: moment(detail.order.invoice.date)
        .tz(clientZoneName || 'Asia/Ho_Chi_Minh')
        .format('DD/MM/YYYY hh:mm A'),
      customer: detail.order.invoice.customer
        ? `${detail.order.invoice.customer.code} ${detail.order.invoice.customer.firstName} ${detail.order.invoice.customer.lastName}`
        : '',
      referenceCode: detail.order.invoice.code
        ? `IN${detail.order.invoice.code}`
        : '',
    }));
  }

  private mergeCouponData(soldCoupons: any[], redeemedCoupons: any[]) {
    return soldCoupons.map((sold) => {
      const possibleRedeemed = redeemedCoupons.filter(
        (r) => r.couponCode === sold.serialNumber,
      );

      const soldMoment = moment(sold.soldDate, 'DD/MM/YYYY hh:mm A');

      const redeemed = possibleRedeemed.find((r) => {
        const usedMoment = moment(r.date, 'DD/MM/YYYY hh:mm A');
        return usedMoment.isAfter(soldMoment);
      });

      return {
        ...sold,
        usedDate: redeemed?.date || '',
        usedCustomer: redeemed?.customer || '',
        usedReferenceCode: redeemed?.referenceCode || '',
      };
    });
  }
}
