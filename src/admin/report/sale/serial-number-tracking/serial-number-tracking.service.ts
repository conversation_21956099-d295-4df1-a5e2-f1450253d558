import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import * as moment from 'moment-timezone';
import { OrderDetail } from 'src/admin/order-detail/order-detail.entity';
import { SerialNumberTrackingQueryDTO } from './serial-number-tracking.dto';
import { InvoiceStatus, ProductType } from 'src/core/enums/entity';
import { Invoice } from 'src/admin/invoice/invoice.entity';

@Injectable()
export class SerialNumberTrackingService {
  private readonly logger = new Logger(SerialNumberTrackingService.name);

  constructor(
    @InjectRepository(OrderDetail)
    private orderDetailRepo: Repository<OrderDetail>,
    @InjectRepository(Invoice)
    private invoiceRepo: Repository<Invoice>,
  ) {}

  async getReport(
    req: CrudRequest,
    {
      purchaseStartDate,
      purchaseEndDate,
      redeemedStartDate,
      redeemedEndDate,
      branchIds,
      clientZoneName,
      searchText,
    }: SerialNumberTrackingQueryDTO,
    isExport = false,
  ) {
    const soldCoupons = await this.getSoldCoupons(
      purchaseStartDate,
      purchaseEndDate,
      branchIds,
      searchText,
      clientZoneName,
    );

    const redeemedCoupons = await this.getRedeemedCoupons(
      redeemedStartDate,
      redeemedEndDate,
      branchIds,
      searchText,
      clientZoneName,
    );

    const mergedData = this.mergeCouponData(soldCoupons, redeemedCoupons);

    if (isExport) {
      return [
        ...mergedData,
        {
          lineNumber: '',
          coupon: 'GRAND TOTAL:',
          serialNumber: '',
          soldDate: '',
          soldCustomer: '',
          soldReferenceCode: '',
          usedDate: '',
          usedCustomer: '',
          usedReferenceCode: '',
        },
      ];
    }

    return {
      data: mergedData,
      totalSold: soldCoupons.length,
      totalUsed: redeemedCoupons.length,
    };
  }

  private async getSoldCoupons(
    startDate: string,
    endDate: string,
    branchIds: string[],
    searchText?: string,
    clientZoneName?: string,
  ) {
    let query = this.orderDetailRepo
      .createQueryBuilder('orderDetail')
      .leftJoinAndSelect('orderDetail.order', 'order')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('order.branch', 'branch')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .where('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('invoice.status = :status', {
        status: InvoiceStatus.PAID,
      })
      .andWhere('product.type = :productType', {
        productType: ProductType.COUPON,
      });

    if (branchIds && branchIds.length > 0) {
      query = query.andWhere('branch.id IN (:...branchIds)', {
        branchIds,
      });
    }

    if (searchText) {
      query = query.andWhere('(orderDetail.couponCode LIKE :search)', {
        search: `%${searchText}%`,
      });
    }

    const orderDetails = await query.getMany();

    return orderDetails.map((detail, index) => {
      const invoice = detail.order.invoice;
      const customer = invoice.customer;
      const formattedDate = moment(invoice.date)
        .tz(clientZoneName || 'Asia/Ho_Chi_Minh')
        .format('DD/MM/YYYY hh:mm A');

      return {
        lineNumber: index + 1,
        coupon: detail.product.name,
        serialNumber: detail.couponCode,
        soldDate: formattedDate,
        soldCustomer: customer
          ? `${customer.code} ${customer.firstName} ${customer.lastName}`
          : '',
        soldReferenceCode: invoice.code ? `IN${invoice.code}` : '',
        usedDate: '',
        usedCustomer: '',
        usedReferenceCode: '',
      };
    });
  }

  private async getRedeemedCoupons(
    startDate: string,
    endDate: string,
    branchIds: string[],
    searchText?: string,
    clientZoneName?: string,
  ) {
    let query = this.orderDetailRepo
      .createQueryBuilder('orderDetail')
      .leftJoinAndSelect('orderDetail.order', 'order')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .leftJoinAndSelect('order.branch', 'branch')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .where('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere('invoice.status = :status', {
        status: InvoiceStatus.PAID,
      })
      .andWhere('orderDetail.couponCode IS NOT NULL')
      .andWhere('product.type != :productType', {
        productType: ProductType.COUPON,
      });

    if (branchIds && branchIds.length > 0) {
      query = query.andWhere('branch.id IN (:...branchIds)', {
        branchIds,
      });
    }

    if (searchText) {
      query = query.andWhere('(orderDetail.couponCode LIKE :search)', {
        search: `%${searchText}%`,
      });
    }

    const orderDetails = await query.getMany();

    return orderDetails.map((detail) => ({
      couponCode: detail.couponCode,
      date: moment(detail.order.invoice.date)
        .tz(clientZoneName || 'Asia/Ho_Chi_Minh')
        .format('DD/MM/YYYY hh:mm A'),
      customer: detail.order.invoice.customer
        ? `${detail.order.invoice.customer.code} ${detail.order.invoice.customer.firstName} ${detail.order.invoice.customer.lastName}`
        : '',
      referenceCode: detail.order.invoice.code
        ? `IN${detail.order.invoice.code}`
        : '',
    }));
  }

  private mergeCouponData(soldCoupons: any[], redeemedCoupons: any[]) {
    const debugInfo = {
      totalSoldCoupons: soldCoupons.length,
      totalRedeemedCoupons: redeemedCoupons.length,
      emptySerialsCount: 0,
      unmatchedCoupons: [],
      dateParsingErrors: [],
      matchingStats: {
        foundMatches: 0,
        noMatches: 0,
        dateFilteredOut: 0,
      },
    };

    const result = soldCoupons.map((sold, index) => {
      // Track empty serial numbers
      if (!sold.serialNumber || sold.serialNumber.trim() === '') {
        debugInfo.emptySerialsCount++;
        this.logger.warn(`Empty serial number found at index ${index}:`, {
          lineNumber: sold.lineNumber,
          coupon: sold.coupon,
          soldDate: sold.soldDate,
          soldCustomer: sold.soldCustomer,
          soldReferenceCode: sold.soldReferenceCode,
        });
      }

      const possibleRedeemed = redeemedCoupons.filter(
        (r) => r.couponCode === sold.serialNumber,
      );

      if (possibleRedeemed.length === 0 && sold.serialNumber) {
        debugInfo.unmatchedCoupons.push({
          serialNumber: sold.serialNumber,
          coupon: sold.coupon,
          soldDate: sold.soldDate,
          lineNumber: sold.lineNumber,
        });
      }

      let soldMoment: moment.Moment;
      try {
        soldMoment = moment(sold.soldDate, 'DD/MM/YYYY hh:mm A');
        if (!soldMoment.isValid()) {
          throw new Error(`Invalid sold date: ${sold.soldDate}`);
        }
      } catch (error) {
        debugInfo.dateParsingErrors.push({
          type: 'sold',
          date: sold.soldDate,
          serialNumber: sold.serialNumber,
          error: error.message,
        });
        soldMoment = moment(); // fallback to current time
      }

      const redeemed = possibleRedeemed.find((r) => {
        try {
          const usedMoment = moment(r.date, 'DD/MM/YYYY hh:mm A');
          if (!usedMoment.isValid()) {
            debugInfo.dateParsingErrors.push({
              type: 'redeemed',
              date: r.date,
              couponCode: r.couponCode,
              error: `Invalid redeemed date: ${r.date}`,
            });
            return false;
          }

          const isAfter = usedMoment.isAfter(soldMoment);
          if (!isAfter && possibleRedeemed.length > 0) {
            debugInfo.matchingStats.dateFilteredOut++;
            this.logger.debug(`Date filter excluded redemption:`, {
              couponCode: r.couponCode,
              soldDate: sold.soldDate,
              usedDate: r.date,
              soldMoment: soldMoment.format(),
              usedMoment: usedMoment.format(),
            });
          }
          return isAfter;
        } catch (error) {
          debugInfo.dateParsingErrors.push({
            type: 'redeemed',
            date: r.date,
            couponCode: r.couponCode,
            error: error.message,
          });
          return false;
        }
      });

      if (redeemed) {
        debugInfo.matchingStats.foundMatches++;
      } else {
        debugInfo.matchingStats.noMatches++;
      }

      const mergedRecord = {
        ...sold,
        usedDate: redeemed?.date || '',
        usedCustomer: redeemed?.customer || '',
        usedReferenceCode: redeemed?.referenceCode || '',
        // Add debug fields to help identify issues
        _debug: {
          hasEmptySerial: !sold.serialNumber || sold.serialNumber.trim() === '',
          hasEmptyUsage: !redeemed,
          possibleRedeemedCount: possibleRedeemed.length,
          serialNumber: sold.serialNumber || 'EMPTY',
        },
      };

      return mergedRecord;
    });

    // Log comprehensive debug information
    this.logger.log('Serial Number Tracking Debug Info:', debugInfo);

    // Log records with empty fields for easy identification
    const emptyFieldRecords = result.filter(
      (record) => record._debug.hasEmptySerial || record._debug.hasEmptyUsage,
    );

    if (emptyFieldRecords.length > 0) {
      this.logger.warn(
        `Found ${emptyFieldRecords.length} records with empty fields:`,
        emptyFieldRecords.map((record) => ({
          lineNumber: record.lineNumber,
          coupon: record.coupon,
          serialNumber: record.serialNumber || 'EMPTY',
          hasEmptySerial: record._debug.hasEmptySerial,
          hasEmptyUsage: record._debug.hasEmptyUsage,
          soldDate: record.soldDate,
          usedDate: record.usedDate || 'EMPTY',
        })),
      );
    }

    return result;
  }

  /**
   * Get detailed debug information about empty fields and data issues
   * This method can be called separately for debugging purposes
   */
  async getDebugReport(
    _req: CrudRequest,
    queryParams: SerialNumberTrackingQueryDTO,
  ) {
    const {
      purchaseStartDate,
      purchaseEndDate,
      redeemedStartDate,
      redeemedEndDate,
      branchIds,
      clientZoneName,
      searchText,
    } = queryParams;

    const soldCoupons = await this.getSoldCoupons(
      purchaseStartDate,
      purchaseEndDate,
      branchIds,
      searchText,
      clientZoneName,
    );

    const redeemedCoupons = await this.getRedeemedCoupons(
      redeemedStartDate,
      redeemedEndDate,
      branchIds,
      searchText,
      clientZoneName,
    );

    // Analyze sold coupons for empty serial numbers
    const emptySerialsInSold = soldCoupons.filter(
      (sold) => !sold.serialNumber || sold.serialNumber.trim() === '',
    );

    // Analyze redeemed coupons
    const redeemedCouponCodes = redeemedCoupons.map((r) => r.couponCode);
    const soldCouponCodes = soldCoupons
      .map((s) => s.serialNumber)
      .filter((code) => code && code.trim() !== '');

    // Find sold coupons that have no redemption records
    const unredeemedCoupons = soldCoupons.filter(
      (sold) =>
        sold.serialNumber && !redeemedCouponCodes.includes(sold.serialNumber),
    );

    // Find redeemed coupons that have no corresponding sold records
    const orphanedRedemptions = redeemedCoupons.filter(
      (redeemed) =>
        redeemed.couponCode && !soldCouponCodes.includes(redeemed.couponCode),
    );

    return {
      summary: {
        totalSoldCoupons: soldCoupons.length,
        totalRedeemedCoupons: redeemedCoupons.length,
        emptySerialsCount: emptySerialsInSold.length,
        unredeemedCouponsCount: unredeemedCoupons.length,
        orphanedRedemptionsCount: orphanedRedemptions.length,
      },
      details: {
        emptySerialsInSold: emptySerialsInSold.map((sold) => ({
          lineNumber: sold.lineNumber,
          coupon: sold.coupon,
          soldDate: sold.soldDate,
          soldCustomer: sold.soldCustomer,
          soldReferenceCode: sold.soldReferenceCode,
          serialNumber: sold.serialNumber || 'EMPTY',
        })),
        unredeemedCoupons: unredeemedCoupons.map((sold) => ({
          lineNumber: sold.lineNumber,
          coupon: sold.coupon,
          serialNumber: sold.serialNumber,
          soldDate: sold.soldDate,
          soldCustomer: sold.soldCustomer,
          soldReferenceCode: sold.soldReferenceCode,
        })),
        orphanedRedemptions: orphanedRedemptions.map((redeemed) => ({
          couponCode: redeemed.couponCode,
          usedDate: redeemed.date,
          usedCustomer: redeemed.customer,
          usedReferenceCode: redeemed.referenceCode,
        })),
      },
      queryParams: {
        purchaseStartDate,
        purchaseEndDate,
        redeemedStartDate,
        redeemedEndDate,
        branchIds,
        searchText,
        clientZoneName,
      },
    };
  }
}
