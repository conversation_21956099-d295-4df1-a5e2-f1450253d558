import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudRequest, GetManyDefaultResponse } from 'src/core/crud/crud';
import { ILike, In, Repository } from 'typeorm';

import { Category } from 'src/admin/category/category.entity';
import { IReportProductResponse } from './interfaces/report-product.response.interface';
import { ReportProductQueryDTO } from './dto/report-product.dto';
import { Product } from 'src/admin/product/product.entity';
import { ProductType } from 'src/core/enums/entity';
import { CommissionSettingService } from 'src/admin/settings/commission/commission-setting.service';

@Injectable()
export class ReporProductService {
  constructor(
    @InjectRepository(Category)
    private categoryRepo: Repository<Category>,
    @InjectRepository(Product)
    private productRepo: Repository<Product>,
    private readonly commissionSettingService: CommissionSettingService,
  ) {}

  async getReport(
    req: CrudRequest,
    {
      keySearch,
      categoryId,
      statusId,
      branchIds,
      currencyCode,
    }: ReportProductQueryDTO,
    type: ProductType,
    isExport = false,
  ) {
    const { page, offset, limit, sort } = req?.parsed;

    const conditions = {
      type,
    };
    if (categoryId) {
      conditions['category.id'] = In(categoryId.split(','));
    }

    if (statusId) {
      conditions['status.id'] = In(statusId.split(','));
    }

    if (keySearch) {
      conditions['name'] = ILike(`%${keySearch}%`);
    }

    if (branchIds && branchIds.length > 0) {
      conditions['branches'] = {
        id: In(branchIds),
      };
    }

    const products = await this.productRepo.find({
      where: conditions,
      relations: [
        'category',
        'category.parent',
        'status',
        'duration',
        'branches',
      ],
      order: { name: 'ASC' },
    });

    const listCommission =
      await this.commissionSettingService.getAllCommissionSetting(
        branchIds,
        type,
        keySearch,
      );

    // return listCommission

    if (isExport) {
      return {
        data: await Promise.all([
          {
            order: '',
            name: '',
            category: 'CATEGORY',
            isMember: 'TYPE',
            cost: `COST(${currencyCode})`,
            price: `PRICE(${currencyCode})`,
            commission: {
              employee: 'THERAPIST',
              frondesk: 'FRONTDESK',
              staff: 'F&B',
            },
          },
          ...products.map(async (p, index) => {
            let productName = p.name;
            if (p.status?.name === 'Inactive') {
              productName += '          Inactive';
            }
            return {
              order: index + 1,
              name: productName,
              category: p.category?.name,
              section: p.category?.parent?.name,
              status: {
                id: p.status?.id,
                name: p.status?.name,
              },
              duration: {
                id: p.duration?.id,
                name: p.duration?.name,
              },
              cost: p.cost.toFixed(2),
              price: p.price.toFixed(2),
              commission:
                await this.commissionSettingService.getCommissionByProductId(
                  p.id,
                  listCommission,
                  true,
                ),
              isMember: p.isMember ? 'Member' : 'Non-member',
            };
          }),
        ]),
      };
    }
    return {
      data: await Promise.all(
        products.map(async (p, index) => {
          return {
            order: index + 1,
            name: p.name,
            category: p.category?.name,
            section: p.category?.parent?.name,
            status: {
              id: p.status?.id,
              name: p.status?.name,
            },
            duration: {
              id: p.duration?.id,
              name: p.duration?.name,
            },
            cost: p.cost,
            price: p.price,
            commission:
              await this.commissionSettingService.getCommissionByProductId(
                p.id,
                listCommission,
              ),
            isMember: p.isMember,
          };
        }),
      ),
      total: products.length,
    };
  }
}
