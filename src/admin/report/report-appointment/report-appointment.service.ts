import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import { Brackets, Repository } from 'typeorm';
import * as moment from 'moment';

import {
  ReportAppointmentCheckInOutQueryDTO,
  ReportAppointmentNoShowQueryDTO,
  ReportAppointmentQueryDTO,
} from './dto/report-appointment.dto';
import { Appointment } from '../../appointment/appointment.entity';
import { AppointmentStatus, ProductType } from '../../../core/enums/entity';
import { getCustomPaginationLimit } from '../../../core/common/common.utils';
import { createPaginationReportInfo } from '../utils/paginate.utils';

@Injectable()
export class ReportAppointmentService {
  constructor(
    @InjectRepository(Appointment)
    private appointmentRepo: Repository<Appointment>,
  ) {}

  async getReportAppointment(
    req: CrudRequest,
    {
      keySearch,
      startDate,
      endDate,
      branchIds,
      limit,
      page,
      clientZoneName,
    }: ReportAppointmentQueryDTO,
    type: ProductType,
    isExport = false,
  ) {
    const queryBuilder = this.appointmentRepo
      .createQueryBuilder('appointment')
      .leftJoinAndSelect('appointment.branch', 'branch')
      .leftJoinAndSelect('appointment.customer', 'customer')
      .leftJoinAndSelect('appointment.orders', 'orders')
      .leftJoinAndSelect('orders.items', 'orderItems')
      .leftJoinAndSelect('orderItems.employees', 'employees')
      .leftJoinAndSelect('orderItems.product', 'product')
      .leftJoinAndSelect('product.duration', 'duration')
      .leftJoinAndSelect('product.category', 'category')
      .leftJoinAndSelect('category.parent', 'categoryParent')
      .where('product.type = :productType', { productType: type });
    // Only include services with duration > 0
    queryBuilder.andWhere('duration.name != :durationName', {
      durationName: 'Empty',
    });
    if (branchIds && branchIds.length) {
      queryBuilder.andWhere('branch.id IN (:...branchIds)', { branchIds });
    }
    if (keySearch) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('customer.firstName ILIKE :keySearch', {
            keySearch: `%${keySearch}%`,
          })
            .orWhere('CAST(customer.code AS TEXT) ILIKE :keySearch', {
              keySearch: `%${keySearch}%`,
            })
            .orWhere('customer.lastName ILIKE :keySearch', {
              keySearch: `%${keySearch}%`,
            })
            .orWhere('customer.phone ILIKE :keySearch', {
              keySearch: `%${keySearch}%`,
            })
            .orWhere(
              "concat(TRIM(customer.firstName), ' ', TRIM(customer.lastName)) ILIKE :keySearch",
              {
                keySearch: `%${keySearch}%`,
              },
            )
            .orWhere('employees.fullName ILIKE :keySearch', {
              keySearch: `%${keySearch}%`,
            });
        }),
      );
    }
    if (
      startDate &&
      endDate &&
      moment(startDate).isValid() &&
      moment(endDate).isValid()
    ) {
      queryBuilder.andWhere(
        'appointment.startTime BETWEEN :startDate AND :endDate',
        {
          startDate,
          endDate,
        },
      );
    }
    queryBuilder.orderBy('appointment.startTime', 'ASC');
    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;
    if (!isExport) {
      queryBuilder.take(qLimit).skip(offset);
    }
    const [appointments, total] = await queryBuilder.getManyAndCount();

    const transformedData = appointments.map((apt, index) => {
      let formatStatus;
      switch (apt.status) {
        case AppointmentStatus.BOOKING:
          formatStatus = 'Booking';
          break;
        case AppointmentStatus.ARRIVED:
          formatStatus = 'Arrived';
          break;
        case AppointmentStatus.REQUEST:
          formatStatus = 'Request';
          break;
        case AppointmentStatus.REQUEST_ARRIVED:
          formatStatus = 'Request arrived';
          break;
        case AppointmentStatus.FACIAL_IPL:
          formatStatus = 'Facial/IPL';
          break;
        default:
          formatStatus = 'Cancel';
          break;
      }
      return {
        order: index + 1,
        date: apt.startTime,
        formatDate: moment
          .tz(apt.startTime, clientZoneName)
          .format('DD/MM/YYYY h:mm A'),
        status: apt.status,
        formatStatus,
        customer: apt.customer
          ? {
              id: apt.customer?.id,
              code: apt.customer?.code,
              name: apt.customer
                ? `${apt.customer?.firstName} ${apt.customer?.lastName}`
                : '',
              formatName: `${apt.customer?.code} ${apt.customer?.firstName} ${apt.customer?.lastName}`,
              phone: apt.customer?.phone,
            }
          : null,
        product: {
          id: apt.orders[0]?.items[0]?.product?.id,
          name: apt.orders[0]?.items[0]?.product?.name,
          price: apt.orders[0]?.items[0]?.product?.price,
          duration: {
            id: apt.orders[0]?.items[0]?.product?.duration?.id,
            name: apt.orders[0]?.items[0]?.product?.duration?.name,
          },
        },
        employee: {
          service: apt.orders[0]?.items[0]?.employees?.map((obj) => ({
            name: obj?.fullName || obj?.displayName,
            id: obj.id,
          })),
          formatService: apt.orders[0]?.items[0]?.employees
            ?.map((obj) => obj?.fullName)
            .filter((name) => name)
            .join(', '),
        },
      };
    });
    if (isExport) {
      return [...transformedData];
    }
    return {
      ...createPaginationReportInfo(
        transformedData,
        total,
        qLimit || total,
        offset || 0,
      ),
    };
  }

  async getReportAppointmentNoShow(
    req: CrudRequest,
    { keySearch, startDate, endDate }: ReportAppointmentNoShowQueryDTO,
    type: ProductType,
  ) {
    // const { page, offset, limit, sort } = req?.parsed;
    //
    // const queryAppointments = this.appointmentRepo
    //   .createQueryBuilder('appointment')
    //   .leftJoinAndSelect('appointment.customer', 'customer')
    //   .leftJoinAndSelect('appointment.orders', 'orders')
    //   .leftJoinAndSelect('orders.items', 'orderItems')
    //   .leftJoinAndSelect('orderItems.employees', 'employees')
    //   .leftJoinAndSelect('orderItems.product', 'product')
    //   .leftJoinAndSelect('product.duration', 'duration')
    //   .leftJoinAndSelect('product.category', 'category')
    //   .leftJoinAndSelect('category.parent', 'categoryParent')
    //   .where('product.type = :productType', { productType: type })
    //   .andWhere('appointment.status IN (:...appointmentStatus)', {
    //     appointmentStatus: [
    //       AppointmentStatus.NOSHOW,
    //     ],
    //   });
    //
    // if (keySearch) {
    //   queryAppointments.andWhere(
    //     '(customer.firstName ILIKE :keySearch OR customer.lastName ILIKE :keySearch OR employees.fullName ILIKE :keySearch)',
    //     { keySearch: `%${keySearch}%` },
    //   );
    // }
    //
    // if (startDate && endDate) {
    //   queryAppointments.andWhere(
    //     'appointment.startTime BETWEEN :startDate AND :endDate',
    //     {
    //       startDate: moment(startDate).startOf('day').toDate(),
    //       endDate: moment(endDate).endOf('day').toDate(),
    //     },
    //   );
    // }
    //
    // queryAppointments.orderBy('appointment.startTime', 'DESC');
    // const appointments = await queryAppointments.getMany();
    //
    // const outAppointments = appointments.map((apt) => {
    //   return {
    //     date: apt.startTime,
    //     status: apt.status,
    //     customer: apt.customer?.id
    //       ? {
    //         id: apt.customer?.id,
    //         code: apt.customer?.code,
    //         name: apt.customer
    //           ? `${apt.customer?.firstName} ${apt.customer?.lastName}`
    //           : '',
    //         phone: apt.customer?.phone,
    //       }
    //       : null,
    //     remark: apt.customer?.id ? apt.customer?.remark : null,
    //     product: {
    //       id: apt.orders[0]?.items[0]?.product?.id,
    //       name: apt.orders[0]?.items[0]?.product?.name,
    //       price: apt.orders[0]?.items[0]?.product?.price,
    //       duration: {
    //         id: apt.orders[0]?.items[0]?.product?.duration?.id,
    //         name: apt.orders[0]?.items[0]?.product?.duration?.name,
    //       },
    //     },
    //     employee: {
    //       service: apt.orders[0]?.items[0]?.employees.map((obj) => ({
    //         name: obj?.fullName,
    //         id: obj.id,
    //       })),
    //     },
    //   };
    // });

    return {
      data: true,
    };
  }

  async getReportAppointmentCheckInOut(
    req: CrudRequest,
    {
      keySearch,
      startDate,
      endDate,
      branchIds,
      limit,
      page,
      clientZoneName,
    }: ReportAppointmentCheckInOutQueryDTO,
    type: ProductType,
    isExport = false,
  ) {
    const queryBuilder = this.appointmentRepo
      .createQueryBuilder('appointment')
      .leftJoinAndSelect('appointment.branch', 'branch')
      .leftJoinAndSelect('appointment.customer', 'customer')
      .leftJoinAndSelect('customer.nationality', 'nationality')
      .leftJoinAndSelect('appointment.orders', 'orders')
      .leftJoinAndSelect('orders.items', 'orderItems')
      .leftJoinAndSelect('orderItems.employees', 'employees')
      .leftJoinAndSelect('orderItems.product', 'product')
      .leftJoinAndSelect('product.duration', 'duration')
      .leftJoinAndSelect('product.category', 'category')
      .leftJoinAndSelect('category.parent', 'categoryParent')
      .where('product.type = :productType', { productType: type });
    // Only include services with duration > 0
    queryBuilder.andWhere('duration.value > 0');
    if (branchIds && branchIds.length) {
      queryBuilder.andWhere('branch.id IN (:...branchIds)', { branchIds });
    }
    if (keySearch) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('customer.firstName ILIKE :keySearch', {
            keySearch: `%${keySearch}%`,
          })
            .orWhere('CAST(customer.code AS TEXT) ILIKE :keySearch', {
              keySearch: `%${keySearch}%`,
            })
            .orWhere('customer.lastName ILIKE :keySearch', {
              keySearch: `%${keySearch}%`,
            })
            .orWhere('customer.phone ILIKE :keySearch', {
              keySearch: `%${keySearch}%`,
            })
            .orWhere(
              "concat(TRIM(customer.firstName), ' ', TRIM(customer.lastName)) ILIKE :keySearch",
              {
                keySearch: `%${keySearch}%`,
              },
            )
            .orWhere('employees.fullName ILIKE :keySearch', {
              keySearch: `%${keySearch}%`,
            });
        }),
      );
    }

    if (
      startDate &&
      endDate &&
      moment(startDate).isValid() &&
      moment(endDate).isValid()
    ) {
      queryBuilder.andWhere(
        'appointment.startTime BETWEEN :startDate AND :endDate',
        {
          startDate,
          endDate,
        },
      );
    }

    queryBuilder.orderBy('appointment.startTime', 'ASC');
    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;
    if (!isExport) {
      queryBuilder.take(qLimit).skip(offset);
    }
    const [appointments, total] = await queryBuilder.getManyAndCount();

    const transformedData = appointments.map((apt, index) => {
      return {
        order: index + 1,
        date: apt.startTime,
        dateFormated: moment
          .tz(apt.startTime, clientZoneName)
          .format('DD/MM/YYYY h:mm A'),
        status: apt.status,
        referenceNo: apt.orders[0]?.code,
        referenceNoFormated: apt.orders[0]?.code
          ? `IN${apt.orders[0]?.code}`
          : '',
        customer: apt.customer?.id
          ? {
              id: apt.customer?.id,
              code: apt.customer?.code,
              nric: apt.customer?.nric,
              name: apt.customer
                ? `${apt.customer?.firstName} ${apt.customer?.lastName}`
                : '',
              phone: apt.customer?.phone,
              nationality: apt.customer?.nationality
                ? {
                    id: apt.customer?.nationality?.id,
                    name: apt.customer?.nationality?.name,
                  }
                : null,
              formatName: `${apt.customer?.code} ${apt.customer?.firstName} ${apt.customer?.lastName}`,
            }
          : null,
        remark: apt.customer?.id ? apt.customer?.remark : null,
        product: {
          id: apt.orders[0]?.items[0]?.product?.id,
          name: apt.orders[0]?.items[0]?.product?.name,
          price: apt.orders[0]?.items[0]?.product?.price,
          duration: {
            id: apt.orders[0]?.items[0]?.product?.duration?.id,
            name: apt.orders[0]?.items[0]?.product?.duration?.name,
          },
        },
        employee: {
          service: apt.orders[0]?.items[0]?.employees.map((obj) => ({
            name: obj?.fullName || obj?.displayName,
            id: obj.id,
          })),
        },
        rfid: apt.rfid,
        checkIn: apt.checkIn,
        checkInFormated: apt.checkIn
          ? moment.tz(apt.checkIn, clientZoneName).format('DD/MM/YYYY h:mm A')
          : '',
        checkOut: apt.checkOut,
        checkOutFormated: apt.checkOut
          ? moment.tz(apt.checkOut, clientZoneName).format('DD/MM/YYYY h:mm A')
          : '',
      };
    });
    if (isExport) {
      return [...transformedData];
    }
    return {
      ...createPaginationReportInfo(
        transformedData,
        total,
        qLimit || total,
        offset || 0,
      ),
    };
  }
}
