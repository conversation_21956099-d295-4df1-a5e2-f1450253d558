import { PickType } from '@nestjs/swagger';
import { ReportQueryDTO } from '../../dto/report.dto';

export class ReportAppointmentQueryDTO extends PickType(ReportQueryDTO, [
  'keySearch',
  'startDate',
  'endDate',
  'branchIds',
  'limit',
  'page',
  'clientZoneName'
]) {
}

export class ReportAppointmentNoShowQueryDTO extends PickType(ReportQueryDTO, [
  'keySearch',
  'startDate',
  'endDate',
]) {
}

export class ReportAppointmentCheckInOutQueryDTO extends PickType(ReportQueryDTO, [
  'keySearch',
  'startDate',
  'endDate',
  'branchIds',
  'limit',
  'page',
  'clientZoneName'
]) {
}
