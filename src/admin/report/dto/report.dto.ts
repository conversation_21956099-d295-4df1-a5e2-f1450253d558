import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsDateString, IsInt, IsString, IsUUID } from 'class-validator';
import { UUID } from 'typeorm/driver/mongodb/bson.typings';

export class ReportQueryDTO {
  @ApiPropertyOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional()
  @IsString()
  keySearch: string;

  @ApiPropertyOptional()
  @IsUUID()
  statusId: string;

  @ApiPropertyOptional()
  @IsInt()
  page: number;

  @ApiPropertyOptional()
  @IsInt()
  limit: number;

  @ApiPropertyOptional()
  branchIds?: string[];

  @ApiPropertyOptional()
  @IsString()
  clientZoneName?: string;

  @ApiPropertyOptional()
  @IsString()
  currencyCode?: string;

  @ApiPropertyOptional()
  @IsString()
  currencyBranchName?: string;

  @ApiPropertyOptional()
  @IsString()
  showInactiveTherapists?: string;

  @ApiPropertyOptional()
  @IsString()
  categoryId?: string;

  @ApiPropertyOptional()
  @IsString()
  categoryBeverageId?: string;
}
