import {
  Controller,
  Get,
  Query,
  Req,
  Res,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  CrudRequest,
  CrudRequestInterceptor,
  ParsedRequest,
} from 'src/core/crud/crud';

import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { REPORT_ALIAS } from './report.const';
import { ReportService } from './report.service';
import { ReportInterceptor } from './report.interceptor';
import { response } from 'express';
import { Public } from '../auth/decorators/public.decerator';

@Controller(REPORT_ALIAS)
@UseGuards(JwtAuthGuard)
@UseInterceptors(ReportInterceptor)
export class ReportController {
  constructor(public reportService: ReportService) {}

  @Get('/')
  @UseInterceptors(CrudRequestInterceptor)
  async getReport(
    @ParsedRequest() crudReq: CrudRequest,
    @Req() req: Request,
    @Query() query: any,
  ) {
    return this.reportService.getReport(crudReq, req, query);
  }

  @Public()
  @Get('/export')
  @UseInterceptors(CrudRequestInterceptor)
  async exportReport(
    @ParsedRequest() crudReq: CrudRequest,
    @Req() req: Request,
    @Query() query: any,
    @Res() response,
  ) {
    const { filename, csv } = await this.reportService.getReport(
      crudReq,
      req,
      query,
      true,
    );
    // Set the response headers to trigger the file download
    response.setHeader('Content-Type', 'text/csv');
    response.attachment(filename);
    // // // Send the file as the response
    response.status(200).send(csv);
  }

  @Get('/debug')
  @UseInterceptors(CrudRequestInterceptor)
  async getDebugReport(
    @ParsedRequest() crudReq: CrudRequest,
    @Req() req: Request,
    @Query() query: any,
  ) {
    return this.reportService.getDebugReport(crudReq, req, query);
  }
}
