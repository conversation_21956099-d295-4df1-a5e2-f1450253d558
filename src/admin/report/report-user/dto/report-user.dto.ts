import { ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';
import { ReportQueryDTO } from '../../dto/report.dto';
import { UUID } from 'crypto';

export class ReportUserLogonQueryDTO extends PickType(ReportQueryDTO, [
  'keySearch',
  'startDate',
  'endDate',
  'branchIds',
  'limit',
  'page',
]) {
  @ApiPropertyOptional()
  @IsUUID()
  roleId: UUID;
}
