import { Injectable } from '@nestjs/common';
import * as moment from 'moment';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import { Repository } from 'typeorm';

import { ReportUserLogonQueryDTO } from './dto/report-user.dto';

import { UserSession } from '../../user-session/user-session.entity';
import { getCustomPaginationLimit } from '../../../core/common/common.utils';
import { createPaginationReportInfo } from '../utils/paginate.utils';

@Injectable()
export class ReportUserService {
  constructor(
    @InjectRepository(UserSession)
    private userSessionRepo: Repository<UserSession>,
  ) {
  }

  async getReportUserLogon(
    req: CrudRequest,
    { keySearch, startDate, endDate, branchIds, roleId, limit, page }: ReportUserLogonQueryDTO,
    isExport = false,
  ) {
    let pRoleId = [];
    if (roleId) {
      pRoleId = roleId.split(',');
    }

    const queryBuilder = this.userSessionRepo.createQueryBuilder('userSession')
      .leftJoinAndSelect('userSession.user', 'user')
      .leftJoinAndSelect('user.branch', 'branch')
      .leftJoinAndSelect('userSession.role', 'role');

    // if (branchIds && branchIds.length) {
    //   queryBuilder.andWhere('branch.id IN (:...branchIds)', { branchIds });
    // }

    if (keySearch) {
      queryBuilder.andWhere('(user.username ILIKE :keySearch)', { keySearch: `%${keySearch}%` });
    }

    if (pRoleId && pRoleId.length) {
      queryBuilder.andWhere('role.id IN (:...pRoleId)', { pRoleId });
    }

    if (startDate && endDate && moment(startDate).isValid() && moment(endDate).isValid()) {
      queryBuilder.andWhere(
        'userSession.login BETWEEN :startDate AND :endDate',
        {
          startDate,
          endDate,
        },
      );
    }

    queryBuilder.orderBy('userSession.login', 'DESC');
    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;
    if (!isExport) {
      queryBuilder.take(qLimit).skip(offset);
    }
    const [users, total] = await queryBuilder.getManyAndCount();

    const transformedData = users.map((ft, index) => {
      return {
        order: index + 1,
        user: {
          id: ft.user.id,
          username: ft.user.username,
          fullname: ft.user.fullname,
        },
        role: {
          id: ft.role.id,
          name: ft.role.name,
        },
        login: ft.login,
        logout: ft.logout,
      };
    });
    if (isExport) {
      return transformedData;
    }
    return createPaginationReportInfo(
      transformedData,
      total,
      qLimit || total,
      offset || 0,
    );
  }
}
