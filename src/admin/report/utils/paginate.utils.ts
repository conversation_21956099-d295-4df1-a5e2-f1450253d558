import { GetManyDefaultResponse } from 'src/core/crud/crud';

/**
 * Wrap page into page-info
 * override this method to create custom page-info response
 * or set custom `serialize.getMany` dto in the controller's CrudOption
 * @param data
 * @param total
 * @param limit
 * @param offset
 */
export const createPaginationReportInfo = <T>(
  data: T[],
  total: number,
  limit: number,
  offset: number,
): GetManyDefaultResponse<T> => {
  return {
    data,
    count: data.length,
    total,
    page: limit ? Math.floor(offset / limit) + 1 : 1,
    pageCount: limit && total ? Math.ceil(total / limit) : 1,
  };
};
