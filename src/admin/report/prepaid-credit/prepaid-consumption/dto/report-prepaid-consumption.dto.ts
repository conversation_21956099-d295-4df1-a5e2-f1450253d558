import { ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { IsDateString } from 'class-validator';
import { ReportQueryDTO } from 'src/admin/report/dto/report.dto';

export class ReportPrepaidConsumptionQueryDTO extends PickType(ReportQueryDTO, [
  'keySearch',
  'startDate',
  'endDate',
  'page',
  'limit',
  'clientZoneName',
  'currencyBranchName'
]) {

  @ApiPropertyOptional()
  @IsDateString()
  purchaseStartDate?: string;

  @ApiPropertyOptional()
  @IsDateString()
  purchaseEndDate?: string;
}
