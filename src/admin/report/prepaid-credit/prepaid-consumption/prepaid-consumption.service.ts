import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import { Brackets, Repository, SelectQueryBuilder } from 'typeorm';
import * as moment from 'moment';
import { CreditHistory } from 'src/admin/credit-history/credit-history.entity';
import { getCustomPaginationLimit } from 'src/core/common/common.utils';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { ReportPrepaidConsumptionQueryDTO } from './dto/report-prepaid-consumption.dto';
import Decimal from 'decimal.js';

interface ReportValues {
  consumed: number | string;
  value: number;
  remain: number;
  usable: number;
  unitPrice: string;
}

interface ReportRow {
  order: number;
  customer: string;
  date: string;
  referenceNo: string;
  item: string;
  category: string;
  purchaseDate: string;
  purchaseReferenceNo: string;
  unitPrice: string;
  consumed: number | string;
  value: string;
  remain: string;
  usable: string;
  purchasedAt: string;
}

interface GrandTotal {
  value: number;
  formattedValue: string;
}

@Injectable()
export class ReportPrepaidConsumptionService extends BaseCrudService<CreditHistory> {
  constructor(
    @InjectRepository(CreditHistory)
    private creditHistoryRepo: Repository<CreditHistory>,
  ) {
    super(creditHistoryRepo);
  }

  private initializeQuery(): SelectQueryBuilder<CreditHistory> {
    return this.creditHistoryRepo
      .createQueryBuilder('creditHistory')
      .leftJoinAndSelect('creditHistory.credit', 'credit')
      .leftJoinAndSelect('credit.customer', 'customer')
      .leftJoinAndSelect('credit.creditSetting', 'creditSetting')
      .leftJoinAndSelect('creditHistory.invoice', 'invoice')
      .leftJoinAndSelect('creditHistory.membership', 'membership')
      .leftJoinAndSelect('membership.invoice', 'invoiceMembership')
      .leftJoinAndSelect('membership.product', 'productMembership')
      .leftJoinAndSelect('productMembership.category', 'category')
      .leftJoinAndSelect('creditHistory.customer', 'customerInfo')
      .where('"creditHistory"."isRefund" = FALSE')
      .andWhere('creditHistory.isMembershipPkg = FALSE')
      .andWhere('creditHistory.isPurgeExpired = FALSE')
      .andWhere('membership.id IS NOT NULL');
  }

  private applyDateFilters(
    query: SelectQueryBuilder<CreditHistory>,
    startDate?: string | Date,
    endDate?: string | Date,
    purchaseStartDate?: string | Date,
    purchaseEndDate?: string | Date,
    clientZoneName?: string,
  ): void {
    if (startDate && endDate && purchaseStartDate && purchaseEndDate) {
      const selectStart = clientZoneName
        ? moment.tz(startDate, clientZoneName).startOf('day')
        : moment(startDate).startOf('day');
      const selectEnd = clientZoneName
        ? moment.tz(endDate, clientZoneName).endOf('day')
        : moment(endDate).endOf('day');
      const purchaseStart = clientZoneName
        ? moment.tz(purchaseStartDate, clientZoneName).startOf('day')
        : moment(purchaseStartDate).startOf('day');
      const purchaseEnd = clientZoneName
        ? moment.tz(purchaseEndDate, clientZoneName).endOf('day')
        : moment(purchaseEndDate).endOf('day');

      if (selectStart.isBefore(purchaseStart)) {
        query.andWhere('1=0');
        return;
      }
      if (
        selectStart.isSame(purchaseStart, 'day') &&
        selectEnd.isSame(purchaseEnd, 'day')
      ) {
        query.andWhere('invoice.date BETWEEN :selectStart AND :selectEnd', {
          selectStart: selectStart.toDate(),
          selectEnd: selectEnd.toDate(),
        });
        query.andWhere(
          'membership.created BETWEEN :purchaseStart AND :purchaseEnd',
          {
            purchaseStart: purchaseStart.toDate(),
            purchaseEnd: purchaseEnd.toDate(),
          },
        );
        return;
      }
      query.andWhere('invoice.date BETWEEN :selectStart AND :selectEnd', {
        selectStart: selectStart.toDate(),
        selectEnd: selectEnd.toDate(),
      });
      query.andWhere(
        'membership.created BETWEEN :purchaseStart AND :purchaseEnd',
        {
          purchaseStart: purchaseStart.toDate(),
          purchaseEnd: purchaseEnd.toDate(),
        },
      );
      return;
    }
    if (startDate && endDate) {
      const start = clientZoneName
        ? moment.tz(startDate, clientZoneName).startOf('day').toDate()
        : moment(startDate).startOf('day').toDate();
      const end = clientZoneName
        ? moment.tz(endDate, clientZoneName).endOf('day').toDate()
        : moment(endDate).endOf('day').toDate();
      query.andWhere('invoice.date BETWEEN :startDate AND :endDate', {
        startDate: start,
        endDate: end,
      });
    }
    if (purchaseStartDate && purchaseEndDate) {
      const start = clientZoneName
        ? moment.tz(purchaseStartDate, clientZoneName).startOf('day').toDate()
        : moment(purchaseStartDate).startOf('day').toDate();
      const end = clientZoneName
        ? moment.tz(purchaseEndDate, clientZoneName).endOf('day').toDate()
        : moment(purchaseEndDate).endOf('day').toDate();
      query.andWhere(
        'membership.created BETWEEN :purchaseStartDate AND :purchaseEndDate',
        {
          purchaseStartDate: start,
          purchaseEndDate: end,
        },
      );
    }
  }

  private applySearchFilters(
    query: SelectQueryBuilder<CreditHistory>,
    keySearch?: string,
  ): void {
    if (keySearch) {
      query.andWhere(
        new Brackets((subQuery) =>
          subQuery
            .where(
              "(CONCAT(TRIM(customer.firstName), ' ', TRIM(customer.lastName)) ILIKE :name)",
              { name: `%${keySearch}%` },
            )
            .orWhere('customer.code::text ILIKE :code', {
              code: `%${keySearch}%`,
            })
            .orWhere('invoice.code::text ILIKE :invoiceCode', {
              invoiceCode: `%${keySearch}%`,
            })
            .orWhere('"invoiceMembership".code::text ILIKE :invoiceCode', {
              invoiceCode: `%${keySearch}%`,
            })
            .orWhere('"productMembership".name ILIKE :productName', {
              productName: `%${keySearch}%`,
            })
            .orWhere('customer.id::text ILIKE :customerId', {
              customerId: `%${keySearch}%`,
            }),
        ),
      );
    }
  }

  private calculatePassportValues(item: CreditHistory): ReportValues {
    return {
      consumed: 1,
      value: new Decimal(0).toNumber(),
      remain: item.usable,
      usable: item.usable,
      unitPrice: new Decimal(0).toFixed(2),
    };
  }

  private calculateMembershipValues(item: CreditHistory): ReportValues {
    const value =
      item?.paid > 0
        ? new Decimal(
            (item.paid * item.credit.creditSetting.price) /
              item.credit.creditSetting.credit,
          ).toNumber()
        : 0;

    return {
      consumed: item.paid.toFixed(2),
      value: value,
      remain: item.closing,
      usable: item.closing,
      unitPrice: item?.membership?.product?.price.toFixed(2) || '0.00',
    };
  }

  private formatCustomerName(item: CreditHistory): string {
    const customer = item.isPassportHistory
      ? item.customer
      : item.credit?.customer;
    if (!customer) return '';

    return `${customer.code} ${customer.firstName} ${customer.lastName}`.trim();
  }

  private formatDateTime(date: Date, timezone: string): string {
    return date ? moment.tz(date, timezone).format('DD/MM/YYYY h:mm A') : '';
  }

  async getReport(
    req: CrudRequest,
    {
      keySearch,
      startDate,
      endDate,
      page,
      limit,
      clientZoneName,
      purchaseEndDate,
      purchaseStartDate,
      currencyBranchName,
    }: ReportPrepaidConsumptionQueryDTO,
    isExport = false,
  ) {
    const query = this.initializeQuery();

    this.applyDateFilters(
      query,
      startDate,
      endDate,
      purchaseStartDate,
      purchaseEndDate,
      clientZoneName,
    );
    this.applySearchFilters(query, keySearch);

    const queryKey = 'created';
    query.orderBy(`creditHistory.${queryKey}`, 'ASC');

    if (!isExport) {
      const qLimit = getCustomPaginationLimit(limit);
      const offset = (page - 1) * limit || 0;
      query.take(qLimit).skip(offset);
    }

    const [data, total] = await query.getManyAndCount();

    const grandTotal: GrandTotal = {
      value: 0,
      formattedValue: '0.00',
    };

    const resData = await Promise.all(
      data.map(async (item, index) => {
        const values = item.isPassportHistory
          ? this.calculatePassportValues(item)
          : this.calculateMembershipValues(item);

        grandTotal.value += values.value;

        return {
          order: index + 1,
          customer: this.formatCustomerName(item),
          date: this.formatDateTime(item.invoice?.date, clientZoneName),
          referenceNo: item?.invoice?.code ? `IN${item.invoice.code}` : '',
          item: item?.membership?.product?.name || '',
          category: item?.membership?.product?.category?.name || '',
          purchaseDate: this.formatDateTime(
            item?.membership?.created,
            clientZoneName,
          ),
          purchaseReferenceNo: item?.membership?.invoice?.code
            ? `IN${item.membership.invoice.code}`
            : '',
          unitPrice: values.unitPrice,
          consumed: values.consumed,
          value: values.value.toFixed(2),
          remain: values.remain.toFixed(2),
          usable: values.usable.toFixed(2),
          purchasedAt: currencyBranchName,
        } as ReportRow;
      }),
    );

    grandTotal.formattedValue = new Decimal(grandTotal.value).toFixed(2);

    if (isExport) {
      const grandData: ReportRow = {
        order: 0,
        customer: '',
        date: '',
        referenceNo: '',
        item: '',
        category: '',
        purchaseDate: '',
        purchaseReferenceNo: '',
        value: grandTotal.formattedValue,
        unitPrice: '',
        consumed: 'GRAND TOTAL:',
        remain: '0.00',
        usable: '0.00',
        purchasedAt: currencyBranchName,
      };
      resData.push(grandData);
      return resData;
    }

    return {
      ...this.createPageInfo<any>(
        resData,
        total,
        limit || total,
        (page - 1) * limit || 0,
      ),
      grandTotal: {
        value: grandTotal.formattedValue,
      },
    };
  }
}
