import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import { Brackets, Repository } from 'typeorm';
import * as moment from 'moment';
import { CreditHistory } from 'src/admin/credit-history/credit-history.entity';
import { getCustomPaginationLimit } from 'src/core/common/common.utils';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { ReportPrepaidCreditPurchaseQueryDTO } from './dto/report-credit-purchase.dto';
import { cr } from 'tencentcloud-sdk-nodejs';

@Injectable()
export class ReportCreditPurchaseService extends BaseCrudService<CreditHistory> {
  constructor(
    @InjectRepository(CreditHistory)
    private creditHistoryRepo: Repository<CreditHistory>,
  ) {
    super(creditHistoryRepo);
  }

  async getReport(
    req: CrudRequest,
    {
      keySearch,
      startDate,
      endDate,
      page,
      limit,
      clientZoneName,
    }: ReportPrepaidCreditPurchaseQueryDTO,
    isExport = false,
  ) {
    const queryCredit = this.creditHistoryRepo
      .createQueryBuilder('creditHistory')
      .leftJoinAndSelect('creditHistory.credit', 'credit')
      .leftJoinAndSelect('credit.customer', 'customer')
      .leftJoinAndSelect('credit.creditSetting', 'creditSetting')
      .leftJoinAndSelect('creditHistory.product', 'product')
      .leftJoinAndSelect('creditHistory.invoice', 'invoice')
      .where('"creditHistory"."isMembershipPkg" = TRUE')
      // .andWhere('"creditHistory"."isRefund" = FALSE')
      .andWhere('product.id IS NOT NULL')
      .andWhere('product."isPassport" = FALSE')
      .andWhere('(creditHistory.created BETWEEN :startDate AND :endDate)', {
        startDate,
        endDate,
      });

    if (keySearch) {
      queryCredit.andWhere(
        new Brackets((subQuery) =>
          subQuery
            .where(
              "(CONCAT(TRIM(customer.firstName), ' ', TRIM(customer.lastName)) ILIKE :name)",
              { name: `%${keySearch}%` },
            )
            .orWhere('customer.code::text ILIKE :code', { code: keySearch })
            .orWhere('invoice.code::text ILIKE :invoiceCode', {
              invoiceCode: keySearch,
            })
            .orWhere('product.name ILIKE :productName', {
              productName: `%${keySearch}%`,
            }),
        ),
      );
    }
    const queryKey = 'created';

    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;
    queryCredit.orderBy(`creditHistory.${queryKey}`, 'ASC');
    if (!isExport) {
      queryCredit.take(qLimit).skip(offset);
    }
    const [data, total] = await queryCredit.getManyAndCount();

    const calculatedRows = this.calculateCreditReportRows(data);

    let resData = [];
    const grandTotal: any = {
      sellingPrice: 0,
      creditQuantity: 0,
      creditBefore: 0,
      creditTotal: 0,
      totalUsage: 0,
      balance: 0,
      type: '',
    };

    // Group rows by customer to identify latest membership for each
    const customerLatestMembership = {};
    calculatedRows.forEach((item) => {
      const customerId = item?.credit?.customer?.id;
      if (!customerId) return;

      // Update or set the latest membership for this customer
      if (
        !customerLatestMembership[customerId] ||
        new Date(item.created) >
          new Date(customerLatestMembership[customerId].created)
      ) {
        customerLatestMembership[customerId] = item;
      }
    });

    if (calculatedRows.length > 0) {
      resData = calculatedRows.map((item, index) => {
        const creditBefore = item._calculated.creditBefore || 0;
        const creditTotal = item?.product?.credit || 0;
        const totalUsage = item._calculated.totalUsage || 0;
        const balance = item._calculated.balance || 0;

        // Always add sellingPrice and creditQuantity to grand total
        grandTotal.sellingPrice += item?.product?.price || 0;
        grandTotal.creditQuantity += creditTotal;

        // Only add other values to grand total if this is the latest membership for the customer
        const customerId = item?.credit?.customer?.id;
        if (customerId && customerLatestMembership[customerId] === item) {
          grandTotal.creditBefore += creditBefore;
          grandTotal.creditTotal += creditTotal;
          grandTotal.totalUsage += totalUsage;
          grandTotal.balance += balance;
        }
        grandTotal.type = 'grand_total';

        return {
          order: index + 1,
          customer: item?.credit?.customer?.firstName
            ? item.credit.customer.code +
              ' ' +
              item.credit.customer.firstName +
              ' ' +
              item.credit.customer.lastName
            : '',
          date: item.created
            ? moment
                .tz(item.created, clientZoneName)
                .format('DD/MM/YYYY h:mm A')
            : '',
          referenceNo: item?.invoice?.code ? `IN${item.invoice.code}` : '',
          membership: item?.product?.name,
          type: item?.credit?.creditSetting?.name,
          sellingPrice: item?.product?.price?.toFixed(2),
          creditQuantity: creditTotal.toFixed(2),
          creditBefore: creditBefore.toFixed(2),
          creditTotal: creditTotal.toFixed(2),
          totalUsage: totalUsage.toFixed(2),
          balance: balance.toFixed(2),
        };
      });
    }

    // Format grand total numbers
    grandTotal.sellingPrice = grandTotal.sellingPrice.toFixed(2);
    grandTotal.creditQuantity = grandTotal.creditQuantity.toFixed(2);
    grandTotal.creditBefore = grandTotal.creditBefore.toFixed(2);
    grandTotal.creditTotal = grandTotal.creditTotal.toFixed(2);
    grandTotal.totalUsage = grandTotal.totalUsage.toFixed(2);
    grandTotal.balance = grandTotal.balance.toFixed(2);

    if (isExport) {
      grandTotal.type = 'GRAND TOTAL:';
      resData.push(grandTotal);
      return resData;
    }

    return {
      ...this.createPageInfo<any>(resData, total, qLimit || total, offset || 0),
      grandTotal,
    };
  }

  private calculateCreditReportRows(data: CreditHistory[]) {
    const groupByCustomer: Record<string, CreditHistory[]> = {};
    data.forEach((item) => {
      const customerId = item?.credit?.customer?.id;
      if (!customerId) return;
      if (!groupByCustomer[customerId]) groupByCustomer[customerId] = [];
      groupByCustomer[customerId].push(item);
    });

    const result: any[] = [];
    Object.values(groupByCustomer).forEach((records) => {
      const sorted = [...records].sort(
        (a, b) => new Date(a.created).getTime() - new Date(b.created).getTime(),
      );
      const totalCredit = sorted.reduce(
        (sum, item) => sum + (item?.product?.credit || 0),
        0,
      );
      const lastClosing = sorted[sorted.length - 1]?.closing ?? 0;
      const totalUsage = totalCredit - lastClosing;

      let cumulativeCredit = 0;
      sorted.forEach((item, idx) => {
        const creditTotal = item?.product?.credit || 0;
        const closing = item?.closing ?? 0;
        const creditBefore = cumulativeCredit;
        cumulativeCredit += creditTotal;
        const balance =
          idx === 0 && item.usable !== undefined ? item.usable : closing;
        result.push({
          ...item,
          _calculated: {
            creditBefore,
            totalUsage,
            balance,
          },
        });
      });
    });
    return result;
  }
}
