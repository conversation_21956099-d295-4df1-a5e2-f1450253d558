import { ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { IsDateString } from 'class-validator';
import { ReportQueryDTO } from 'src/admin/report/dto/report.dto';

export class ReportPrepaidCreditPurchaseQueryDTO extends PickType(ReportQueryDTO, [
  'keySearch',
  'startDate',
  'endDate',
  'page',
  'limit',
  'clientZoneName',
]) {
  @ApiPropertyOptional()
  @IsDateString()
  balanceOn: Date;

  @ApiPropertyOptional()
  prepaidFilter: string;
}
