import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import { Brackets, Repository } from 'typeorm';
import * as moment from 'moment';
import { CreditHistory } from 'src/admin/credit-history/credit-history.entity';
import { getCustomPaginationLimit } from 'src/core/common/common.utils';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { ReportPrepaidCreditConsumptionQueryDTO } from './dto/report-credit-consumption.dto';
import Decimal from 'decimal.js';
import { ProductType } from 'src/core/enums/entity';

@Injectable()
export class ReportCreditConsumptionService extends BaseCrudService<CreditHistory> {
  constructor(
    @InjectRepository(CreditHistory)
    private creditHistoryRepo: Repository<CreditHistory>,
  ) {
    super(creditHistoryRepo);
  }

  async getReport(
    req: CrudRequest,
    {
      keySearch,
      startDate,
      endDate,
      page,
      limit,
      clientZoneName,
      purchaseEndDate,
      purchaseStartDate,
    }: ReportPrepaidCreditConsumptionQueryDTO,
    isExport = false,
  ) {
    const queryUsedMembership = this.creditHistoryRepo
      .createQueryBuilder('creditHistory')
      .leftJoinAndSelect('creditHistory.credit', 'credit')
      .leftJoinAndSelect('credit.customer', 'customer')
      .leftJoinAndSelect('credit.creditSetting', 'creditSetting')
      .leftJoinAndSelect('creditHistory.invoice', 'invoice')
      .leftJoinAndSelect('creditHistory.membership', 'membership')
      .leftJoinAndSelect('membership.invoice', 'invoiceMembership')
      .leftJoinAndSelect('membership.product', 'productMembership')
      .andWhere('creditHistory.isMembershipPkg = FALSE')
      .andWhere('creditHistory.isPassportHistory = FALSE')
      .andWhere('membership.id IS NOT NULL');

    if (purchaseStartDate && purchaseEndDate) {
      queryUsedMembership.andWhere(
        '(membership.created BETWEEN :purchaseStartDate AND :purchaseEndDate)',
        {
          purchaseStartDate,
          purchaseEndDate,
        },
      );
    }

    const normalizedStartDate = startDate;
    let normalizedEndDate = endDate;
    if (endDate && clientZoneName) {
      const end = moment.tz(endDate, clientZoneName);
      if (
        end.hour() === 0 &&
        end.minute() === 0 &&
        end.second() === 0 &&
        end.millisecond() === 0
      ) {
        normalizedEndDate = end.endOf('day').toISOString();
      }
    }
    if (startDate && endDate) {
      queryUsedMembership.andWhere(
        `(
          (invoice.date IS NOT NULL AND invoice.date BETWEEN :startDate AND :endDate)
          OR
          (invoice.date IS NULL AND creditHistory.created BETWEEN :startDate AND :endDate)
        )`,
        {
          startDate: normalizedStartDate,
          endDate: normalizedEndDate,
        },
      );
    }

    if (keySearch) {
      queryUsedMembership.andWhere(
        new Brackets((subQuery) =>
          subQuery
            .where(
              "(CONCAT(TRIM(customer.firstName), ' ', TRIM(customer.lastName)) ILIKE :name)",
              { name: `%${keySearch}%` },
            )
            .orWhere('customer.code::text ILIKE :code', { code: keySearch })
            .orWhere('invoice.code::text ILIKE :invoiceCode', {
              invoiceCode: keySearch,
            })
            .orWhere('"invoiceMembership".code::text ILIKE :invoiceCode', {
              invoiceCode: keySearch,
            })
            .orWhere('"productMembership".name ILIKE :productName', {
              productName: `%${keySearch}%`,
            }),
        ),
      );
    }
    const queryKey = 'date';

    const qLimit = getCustomPaginationLimit(Number(limit));
    const pageNumber = Number(page) || 1;
    const offset: number = (pageNumber - 1) * qLimit;
    queryUsedMembership.orderBy(`invoice.${queryKey}`, 'ASC');
    queryUsedMembership.orderBy(`creditHistory.created`, 'ASC');

    const data = await queryUsedMembership.getMany();

    const sorted = data.sort((a, b) => {
      const dateA = new Date(a?.invoice?.date || a?.created);
      const dateB = new Date(b?.invoice?.date || b?.created);
      if (dateA.getTime() !== dateB.getTime()) {
        return dateA.getTime() - dateB.getTime();
      }
      // @ts-ignore
      return a.id - b.id;
    });
    const total = sorted.length;
    const paged = isExport
      ? sorted
      : sorted.slice(Number(offset), Number(offset) + Number(qLimit));

    const grandTotal: any = {
      consumed: 0,
      value: 0,
    };
    let resData = [];
    if (paged.length > 0) {
      resData = await Promise.all(
        paged.map(async (item, index: number) => {
          if (item.isRefund) {
            const detail: any = item.detail || {};
            let membershipName = '';
            const refundedValue = Math.abs(item.paid || 0);
            let referenceNo = '';
            if (item?.invoice?.code && item.invoice.code.startsWith('CN')) {
              referenceNo = item.invoice.code;
            } else if (detail?.code && String(detail.code).startsWith('CN')) {
              referenceNo = detail.code;
            } else if (detail?.code) {
              referenceNo = 'CN' + detail.code;
            }
            let refundedItem = null;
            if (Array.isArray(detail.orders)) {
              for (const order of detail.orders) {
                if (order?.items && order.items.length > 0) {
                  for (const subItem of order.items) {
                    if (
                      (subItem?.product?.type === 'MEMBERSHIP' ||
                        subItem?.product?.type === ProductType.MEMBERSHIP) &&
                      subItem?.product?.name
                    ) {
                      membershipName = subItem.product.name;
                      refundedItem = subItem;
                      break;
                    }
                  }
                  if (membershipName) break;
                } else if (
                  (order?.product?.type === 'MEMBERSHIP' ||
                    order?.product?.type === ProductType.MEMBERSHIP) &&
                  order?.product?.name
                ) {
                  membershipName = order.product.name;
                  refundedItem = order;
                  break;
                }
              }
            }
            if (!membershipName) membershipName = 'Refund credit';
            membershipName = membershipName.replace(/\.$/, '');
            grandTotal.value += refundedValue;
            grandTotal.consumed += refundedValue;
            return {
              order: (offset as number) + (index as number) + 1,
              customer: item?.credit?.customer?.firstName
                ? item.credit.customer.code +
                  ' ' +
                  item.credit.customer.firstName +
                  ' ' +
                  item.credit.customer.lastName
                : '',
              date: item?.created
                ? moment
                    .tz(item?.created, clientZoneName)
                    .format('DD/MM/YYYY h:mm A')
                : '',
              referenceNo: referenceNo,
              membershipName: membershipName,
              type: item?.credit?.creditSetting?.name,
              acquiredDate: item?.membership?.created
                ? moment
                    .tz(item?.membership?.created, clientZoneName)
                    .format('DD/MM/YYYY h:mm A')
                : '',
              acquiredReferenceNo: item?.membership?.invoice?.code
                ? `IN${item?.membership?.invoice?.code}`
                : '',
              before: refundedValue.toFixed(2),
              items: [
                {
                  name: membershipName,
                  quantity: 1,
                  consumed: refundedValue,
                },
              ],
              value: refundedValue.toFixed(2),
              remain: '0.00',
            };
          }
          grandTotal.value += item?.paid;
          const orders =
            item?.detail && 'orders' in item?.detail ? item?.detail.orders : [];
          let items = await this.transformOrderToItem(orders, item);
          const totalConsumed = items.reduce((acc, i) => acc + i.consumed, 0);
          const totalDiscount = totalConsumed - item.paid;
          items = items.map((i, idx) => {
            let consumed = i.consumed;
            let discountPercent = 0;
            if (totalDiscount > 0 && idx === items.length - 1) {
              consumed = Number((i.consumed - totalDiscount).toFixed(2));
              discountPercent = Math.round((totalDiscount / i.consumed) * 100);
            }
            const result = {
              ...i,
              originalConsumed: i.consumed,
              consumed,
              discountPercent,
            };
            return result;
          });
          const consumedInItems = items.reduce(
            (acc, item) => acc + item.consumed,
            0,
          );
          grandTotal.consumed += consumedInItems;

          const reportDate = item?.invoice?.date || item?.created;
          const formattedDate = reportDate
            ? moment.tz(reportDate, clientZoneName).format('DD/MM/YYYY h:mm A')
            : '';

          return {
            order: (offset as number) + (index as number) + 1,
            customer: item?.credit?.customer?.firstName
              ? item.credit.customer.code +
                ' ' +
                item.credit.customer.firstName +
                ' ' +
                item.credit.customer.lastName
              : '',
            date: formattedDate,
            referenceNo: item?.invoice?.code ? `IN${item?.invoice?.code}` : '',
            membershipName: item?.membership?.product?.name,
            type: item?.credit?.creditSetting?.name,
            acquiredDate: item?.membership?.created
              ? moment
                  .tz(item?.membership?.created, clientZoneName)
                  .format('DD/MM/YYYY h:mm A')
              : '',
            acquiredReferenceNo: item?.membership?.invoice?.code
              ? `IN${item?.membership?.invoice?.code}`
              : '',
            before: item?.opening?.toFixed(2) ?? '',
            items: items,
            value: item?.paid?.toFixed(2),
            remain:
              item.isPurgeExpired === true
                ? new Decimal(0).toFixed(2)
                : item?.closing?.toFixed(2),
          };
        }),
      );
      resData.sort((a, b) => {
        if (!a.date) return -1;
        if (!b.date) return 1;
        const dateA = moment(a.date, 'DD/MM/YYYY h:mm A').toDate();
        const dateB = moment(b.date, 'DD/MM/YYYY h:mm A').toDate();
        return dateA.getTime() - dateB.getTime();
      });
    }
    grandTotal.consumed = Number(grandTotal.consumed).toFixed(2);
    grandTotal.value = Number(grandTotal.value).toFixed(2);

    if (isExport) {
      const grandData = {
        order: '',
        customer: '',
        date: '',
        referenceNo: '',
        membershipName: '',
        type: '',
        acquiredDate: '',
        acquiredReferenceNo: '',
        before: '',
        items: {
          quantity: 'GRAND TOTAL:',
          consumed: grandTotal.consumed,
        },
        value: grandTotal.value,
        remain: '',
      };
      resData.push(grandData);
      return resData;
    }

    return {
      ...this.createPageInfo<any>(resData, total, qLimit || total, offset || 0),
      grandTotal,
    };
  }

  async transformOrderToItem(order, data) {
    if (data.isPurgeExpired === true) {
      return [
        {
          consumed: data.paid,
          quantity: 1,
          name: 'Expired Credits - Manual Purge',
        },
      ];
    }

    // Extract invoice-level discount/coupon if available
    const invoiceDiscount = data?.invoice?.discount || 0;
    const invoiceCoupon =
      Array.isArray(data?.invoice?.invoiceCoupon) &&
      data?.invoice?.invoiceCoupon.length > 0
        ? data.invoice.invoiceCoupon[0]
        : null;
    // Gather all items for discount logic
    const allItems = order.flatMap((orderItem) => orderItem.items || []);
    const allDetailDiscountZero = allItems.every(
      (d) => !d.discount || d.discount === 0,
    );
    // Only items that can apply discount
    const eligibleItems = allItems.filter(
      (d) => d.product && !d.product.isNotApplyDiscount,
    );
    const eligibleTotalBeforeTax = eligibleItems.reduce((sum, d) => {
      const unitPrice =
        d.product?.price || Number(d.price) / Number(d.quantity) || 0;
      return sum + unitPrice * (Number(d.quantity) || 0);
    }, 0);

    return order.flatMap((orderItem) =>
      (orderItem.items || []).map((item) => {
        const quantity = Number(item.quantity) || 0;
        // Use product.price as unit price if available, else fallback
        const unitPrice =
          item.product?.price ||
          (quantity > 0
            ? Number(item.price) / quantity
            : Number(item.price) || 0);
        let unitDiscount = 0;
        if (item.product?.isNotApplyDiscount) {
          unitDiscount = 0;
        } else if (
          allDetailDiscountZero &&
          invoiceDiscount > 0 &&
          unitPrice > 0
        ) {
          if (
            invoiceCoupon &&
            invoiceCoupon.couponType === 'Percentage' &&
            invoiceCoupon.percent
          ) {
            unitDiscount = +(unitPrice * (invoiceCoupon.percent / 100)).toFixed(
              2,
            );
          } else if (eligibleTotalBeforeTax > 0) {
            unitDiscount = +(
              (invoiceDiscount / eligibleTotalBeforeTax) *
              unitPrice
            ).toFixed(2);
          }
        } else {
          unitDiscount = Number(item.discount) || 0;
        }
        const totalDiscount = +(unitDiscount * quantity).toFixed(2);
        let consumed = +(unitPrice * quantity - totalDiscount);
        if (consumed < 0) consumed = 0;
        return {
          consumed: Number(consumed.toFixed(2)),
          quantity: quantity,
          name: item.product?.name,
        };
      }),
    );
  }
}
