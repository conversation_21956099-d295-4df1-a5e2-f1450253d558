import { ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { ReportQueryDTO } from '../../dto/report.dto';

export class ReportEmployeeSaleServiceQueryDTO extends PickType(
  ReportQueryDTO,
  ['keySearch', 'startDate', 'endDate', 'branchIds', 'clientZoneName'],
) {
  @ApiPropertyOptional()
  @IsString()
  categoryIds: string;

  @ApiPropertyOptional()
  @IsString()
  treatedById: string;

  @ApiPropertyOptional()
  @IsString()
  saleEmployee: string;

  @ApiPropertyOptional()
  showConversionSummary: any;
}
