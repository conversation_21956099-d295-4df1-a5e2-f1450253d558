import { ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { IsString } from 'class-validator';
import { ReportQueryDTO } from '../../dto/report.dto';

export class ReportPrepaidServiceSaleQueryDTO extends PickType(ReportQueryDTO, [
  'startDate',
  'endDate',
  'clientZoneName'
]) {
  @ApiPropertyOptional()
  @IsString()
  type: string;

  @ApiPropertyOptional()
  @IsString()
  saleEmployee: string;
}
