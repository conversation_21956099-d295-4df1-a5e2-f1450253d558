export const REPORT_ALIAS = 'report';

export enum REPORT_NAME_ALIAS {
  //Employee Sale & Service
  REPORT_EMPLOYEE_LIST = 'THERAPIST LIST',
  REPORT_EMPLOYEE_PERFORMANCE = '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> PERFORMANCE',
  REPORT_NEW_CUSTOMER_SALE = 'NEW CUSTOMER SALE',
  REPORT_PREPAID_SERVICE_SALE = 'PREPAID SERVICE SALE',
  REPORT_EMPLOYEE_SERVICE_DETAIL = 'THERAPIST SERVICE DETAIL',
  REPORT_NEW_CUSTOMER_BY_EMPLOYEE = 'NEW CUSTOMER BY EMPLOYEE',
  REPORT_SALE_DETAIL = 'SALE DETAIL',
  REPORT_FOC_ITEM = 'FOC ITEM',
  REPORT_SERIAL_NUMBER_TRACKING = 'SERIAL NUMBER TRACKING',

  //Customer & Credit
  REPORT_CUSTOMER_LIST = 'CUSTOMER LIST',
  REPORT_CUSTOMER_MEMBERSHIP = 'CUSTOMER MEMBERSHIP EXPIRY',
  REPORT_CUSTOMER_SPENDING = 'CUSTOMER SPENDING',
  REPORT_CUSTOMER_PREPAID_DETAIL = 'CUSTOMER PREPAID DETAIL',
  REPORT_CREDIT_HISTORY = 'CREDIT HISTORY',
  REPORT_CUSTOMER_CREDIT = 'CUSTOMER CREDIT',
  REPORT_OPENING_CLOSING_CREDIT = 'OPENING CLOSING CREDIT',
  REPORT_TREATMENT_HISTORY = 'TREATMENT HISTORY',
  REPORT_NEW_CUSTOMER = 'NEW CUSTOMER',

  //Product & Service
  REPORT_PRODUCT = 'PRODUCT LIST',
  REPORT_SERVICE = 'SERVICE LIST',
  REPORT_MEMBERSHIP = 'MEMBERSHIP LIST',
  REPORT_COUNPON = 'COUPON LIST',

  //Sale
  REPORT_Z_REPORT = 'Z REPORT',
  REPORT_PAYMENT = 'PAYMENT REPORT',
  REPORT_SALE_TAX = 'SALE TAX',
  REPORT_TAX_SUMMARY = 'TAX SUMMARY',
  REPORT_DAILY_RECEIVED = 'DAILY RECEIVED',
  REPORT_DAILY_RECEIVED_SUMMARY = 'DAILY RECEIVED SUMMARY',
  REPORT_SALE_SUMMARY = 'SALE SUMMARY',
  REPORT_FOOD_AND_BEVERAGE_SALE = 'FOOD_AND_BEVERAGE_DAILY_SALE',
  REPORT_REVENUE_CONVERSION = 'REVENUE CONVERSION',

  //Sale Types
  REPORT_ALA_CARTE_DAILY_SALE = 'À LA CARTE SERVICE SALE',
  REPORT_PRODUCT_DAILY_SALE = 'PRODUCT DAILY SALE',
  REPORT_SERVICE_DAILY_SALE = 'SERVICE DAILY SALE',
  REPORT_MEMBERSHIP_DAILY_SALE = 'MEMBERSHIP DAILY SALE',
  REPORT_COUPON_DAILY_SALE = 'COUPON DAILY SALE',

  //Sale Analytics
  REPORT_VOIDED_SALE = 'VOIDED SALE',
  REPORT_SALE_RANKING = 'SALE RANKING',
  REPORT_AUDIT_TRAIL = 'SALE TICKET AUDIT TRAIL',
  REPORT_RETURNED_ITEM = 'RETURNED ITEM',

  //Appointment
  REPORT_APPOINTMENT = 'APPOINTMENT LIST',
  REPORT_APPOINTMENT_CHECK_IN_OUT = 'APPOINTMENT CHECK IN/OUT',

  //Other
  REPORT_SERVICE_PERFORM = 'SERVICE PERFORM',
  REPORT_USER_LOGON = 'USER LOGON',

  // Prepaid & Credit
  REPORT_CREDIT_PURCHASE = 'CREDIT PURCHASE',
  REPORT_CREDIT_CONSUMPTION = 'CREDIT CONSUMPTION',
  REPORT_PREPAID_CONSUMPTION = 'PREPAID CONSUMPTION',
}

export enum REPORT_ROUTE {
  //Customer & Credit
  REPORT_CUSTOMER_LIST = 'report-customer-list',
  REPORT_CUSTOMER_MEMBERSHIP = 'customer-membership-expiry-report',
  REPORT_CUSTOMER_SPENDING = 'report-customer-spending',
  REPORT_CUSTOMER_PREPAID_DETAIL = 'report-customer-prepaid-detail',
  REPORT_CREDIT_HISTORY = 'report-credit-history',
  REPORT_CUSTOMER_CREDIT = 'report-customer-credit',
  REPORT_OPENING_CLOSING_CREDIT = 'report-opening-closing-credit',
  REPORT_TREATMENT_HISTORY = 'report-treatment-history',
  REPORT_NEW_CUSTOMER = 'report-new-customer',
  REPORT_SERIAL_NUMBER_TRACKING = 'report-serial-number-tracking',

  //Employee Sale & Service
  REPORT_EMPLOYEE_LIST = 'therapist-list',
  REPORT_NEW_CUSTOMER_SALE = 'report-new-customer-sale',
  REPORT_PREPAID_SERVICE_SALE = 'report-prepaid-service-sale',
  REPORT_EMPLOYEE_PERFORMANCE = 'report-employee-performance',
  REPORT_EMPLOYEE_SERVICE_DETAIL = 'report-employee-service-detail',
  REPORT_NEW_CUSTOMER_BY_EMPLOYEE = 'report-new-customer-by-employee',

  //Product & Service
  REPORT_PRODUCT = 'report-product-list',
  REPORT_SERVICE = 'report-service-list',
  REPORT_MEMBERSHIP = 'report-membership-list',
  REPORT_COUNPON = 'report-coupon-list',

  //Sale
  REPORT_DAILY_RECEIVED = 'report-daily-received',
  REPORT_DAILY_RECEIVED_SUMMARY = 'report-daily-received-summary',
  REPORT_Z_REPORT = 'report-z-report',
  REPORT_PAYMENT = 'report-payment',
  REPORT_SALE_TAX = 'report-sale-tax',
  REPORT_TAX_SUMMARY = 'report-tax-summary',
  REPORT_SALE_SUMMARY = 'report-sale-summary',
  REPORT_FOOD_AND_BEVERAGE_SALE = 'report-food-and-beverage-daily-sale',
  REPORT_SALE_PROFIT = 'report-sale-profit',
  REPORT_REVENUE_CONVERSION = 'report-revenue-conversion',
  REPORT_SALE_DETAIL = 'report-sale-detail',
  REPORT_FOC_ITEM = 'report-foc-item',

  //Sale Types
  REPORT_PRODUCT_DAILY_SALE = 'report-product-daily-sale',
  REPORT_SERVICE_DAILY_SALE = 'report-service-daily-sale',
  REPORT_ALA_CARTE_DAILY_SALE = 'ala-carte-service-sale-report',
  REPORT_MEMBERSHIP_DAILY_SALE = 'report-membership-daily-sale',
  REPORT_COUPON_DAILY_SALE = 'report-coupon-daily-sale',
  REPORT_DAILY_SALES = 'report-daily-sales',

  //Sale Analytics
  REPORT_AUDIT_TRAIL = 'report-audit-trail',
  REPORT_RETURNED_ITEM = 'report-returned-item',
  REPORT_SALE_RANKING = 'report-sale-ranking',
  REPORT_VOIDED_SALE = 'voided-sale-report',

  //Appointment
  REPORT_APPOINTMENT = 'appointment-list-report',
  REPORT_APPOINTMENT_NO_SHOW = 'report-appointment-no-show',
  REPORT_APPOINTMENT_CHECK_IN_OUT = 'report-appointment-check-in-out',

  //Other
  REPORT_SERVICE_PERFORM = 'report-service-perform',
  REPORT_USER_LOGON = 'report-user-logon',

  // Prepaid & Credit
  REPORT_CREDIT_PURCHASE = 'report-credit-purchase',
  REPORT_CREDIT_CONSUMPTION = 'report-credit-consumption',
  REPORT_PREPAID_CONSUMPTION = 'report-prepaid-consumption',
}
