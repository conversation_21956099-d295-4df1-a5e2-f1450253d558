import { Injectable } from '@nestjs/common';
import * as moment from 'moment';
import { Decimal } from 'decimal.js';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import { Brackets, Repository } from 'typeorm';

import { AuditTrail } from 'src/admin/audit-trail/audit-trail.entity';
import { getCustomPaginationLimit } from 'src/core/common/common.utils';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import {
  ReportAuditTrailQueryDTO,
  ReportVoidedSaleQueryDTO,
} from './dto/report-analytics.dto';
import { Invoice } from '../../invoice/invoice.entity';
import { InvoiceStatus, Prefix, ProductType } from '../../../core/enums/entity';
import { createPaginationReportInfo } from '../utils/paginate.utils';
import { OrderDetail } from 'src/admin/order-detail/order-detail.entity';

@Injectable()
export class ReportAnalyticsService extends BaseCrudService<AuditTrail> {
  constructor(
    @InjectRepository(AuditTrail)
    private auditTrailRepo: Repository<AuditTrail>,
    @InjectRepository(Invoice)
    private invoiceRepo: Repository<Invoice>,
    @InjectRepository(OrderDetail)
    private orderDetailRepo: Repository<OrderDetail>,
  ) {
    super(auditTrailRepo);
  }

  async getReportAuditTrail(
    req: CrudRequest,
    {
      keySearch,
      startDate,
      endDate,
      branchIds,
      roleId,
      filterBy,
    }: ReportAuditTrailQueryDTO,
    isExport = false,
  ) {
    const { page, limit, sort } = req?.parsed;
    const queryBuilder = this.auditTrailRepo
      .createQueryBuilder('audit')
      .leftJoinAndSelect('audit.branch', 'branch')
      .leftJoinAndSelect('audit.order', 'order')
      .leftJoinAndSelect('audit.createdBy', 'createdBy')
      .leftJoinAndSelect('audit.invoice', 'invoice')
      // .where(
      //   'branch.id ' +
      //     (branchIds && branchIds.length
      //       ? 'IN (:...branchIds)'
      //       : 'IS NOT NULL'),
      //   { branchIds },
      // )
      .where('audit.startTime BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });

    if (branchIds && branchIds.length) {
      queryBuilder.andWhere('branch.id IN (:...branchIds)', {
        branchIds,
      });
    }

    if (filterBy) {
      //search like operation
      queryBuilder.where('audit.operation = :operation', {
        operation: filterBy,
      });
    }

    if (keySearch) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.orWhere('createdBy.username ILIKE :keySearch', {
            keySearch: `%${keySearch}%`,
          })
            .orWhere('createdBy.displayName ILIKE :keySearch', {
              keySearch: `%${keySearch}%`,
            })
            .orWhere('"invoice".code::text ILIKE :keySearch', {
              keySearch: `%${keySearch}%`,
            })
            .orWhere('"order".code::text ILIKE :keySearch', {
              keySearch: `%${keySearch}%`,
            });
        }),
      );
    }
    queryBuilder.orderBy('audit.created', 'ASC');
    // const result = await query.getMany();
    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;

    // Only apply pagination if not exporting
    if (!isExport) {
      queryBuilder.take(qLimit).skip(offset);
    }

    const [logs, total] = await queryBuilder.getManyAndCount();
    const result: any = logs.map((item, index) => {
      return {
        id: item?.id,
        order: isExport ? index + 1 : item?.order?.code, // Row number for export, order code for UI
        createdBy:
          item?.createdBy?.displayName ||
          item?.createdBy?.fullname ||
          item?.createdBy?.username,
        invoice: item?.invoice?.code ? `IN${item.invoice.code}` : '',
        description: item.description,
        startTime: item.startTime,
        endTime: item.created,
        operation: item.operation,
      };
    });
    // Return raw data for export, paginated data for regular requests
    if (isExport) {
      return result;
    }

    return this.createPageInfo<any>(
      result,
      total,
      qLimit || total,
      offset || 0,
    );
  }

  async getReportVoidedSale(
    req: CrudRequest,
    {
      keySearch,
      startDate,
      endDate,
      branchIds,
      limit,
      page,
      clientZoneName,
    }: ReportVoidedSaleQueryDTO,
    isExport = false,
  ) {
    const queryBuilder = this.invoiceRepo
      .createQueryBuilder('invoice')
      .leftJoinAndSelect('invoice.branch', 'branch')
      .leftJoinAndSelect('invoice.invoicePayments', 'invoicePayments')
      .leftJoinAndSelect('invoicePayments.paymentMethod', 'paymentMethod')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .leftJoinAndSelect('invoice.referral', 'referral')
      .where('invoice.status = :invoiceStatus', {
        invoiceStatus: InvoiceStatus.VOID,
      });
    if (branchIds && branchIds.length) {
      queryBuilder.andWhere('invoice.branch.id IN (:...branchIds)', {
        branchIds,
      });
    }
    if (keySearch) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('customer.firstName ILIKE :keySearch', {
            keySearch: `%${keySearch}%`,
          }).orWhere('customer.lastName ILIKE :keySearch', {
            keySearch: `%${keySearch}%`,
          });
        }),
      );
    }
    if (
      startDate &&
      endDate &&
      moment(startDate).isValid() &&
      moment(endDate).isValid()
    ) {
      queryBuilder.andWhere(
        'invoice.voidDate BETWEEN :startDate AND :endDate',
        {
          startDate,
          endDate,
        },
      );
    }
    queryBuilder.orderBy('invoice.voidDate', 'ASC');
    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;
    if (!isExport) {
      queryBuilder.take(qLimit).skip(offset);
    }
    const [invoices, total] = await queryBuilder.getManyAndCount();
    let sumTotal = new Decimal(0);
    const transformedData = invoices.map((ft, index) => {
      const payload = ft.payload as any;
      const orders: any[] = payload.orders;
      const itemsData = [];
      let total = new Decimal(0);
      for (const invoicePayment of ft.invoicePayments) {
        total = total.plus(new Decimal(invoicePayment.paid));
      }
      sumTotal = sumTotal.plus(total);
      for (const order of orders) {
        const items = order.items;
        for (const item of items) {
          itemsData.push({
            item: {
              name: item.product.name,
            },
            quantity: item.quantity,
          });
        }
      }
      return {
        order: index + 1,
        date: ft.date,
        formatDate: ft.date
          ? moment.tz(ft.date, clientZoneName).format('DD/MM/YYYY h:mm A')
          : '',
        voidDate: ft.voidDate,
        formatVoidDate: ft.voidDate
          ? moment.tz(ft.voidDate, clientZoneName).format('DD/MM/YYYY h:mm A')
          : '',
        referenceNo: ft?.code,
        formatReferenceNo: Prefix.INVOICE + ft?.code,
        customer: ft.customer
          ? {
              id: ft.customer?.id,
              code: ft.customer?.code,
              name: `${ft.customer?.firstName} ${ft.customer?.lastName}`,
              formatName: `${ft.customer?.code} ${ft.customer?.firstName} ${ft.customer?.lastName}`,
            }
          : null,
        itemsData,
        employee: {
          sale: {
            id: ft?.referral?.id,
            name:
              ft?.referral?.username ||
              ft?.referral?.displayName ||
              ft?.referral?.fullname ||
              '',
          },
        },
        reamark: ft.note,
        total: total.toFixed(2),
      };
    });
    if (isExport) {
      return [
        ...transformedData,
        {
          order: '',
          formatDate: '',
          formatReferenceNo: '',
          customer: {
            formatName: '',
          },
          employee: {
            sale: {
              name: '',
            },
          },
          itemsData: {
            item: {
              name: '',
            },
            quantity: 'GRAND TOTAL:',
          },
          total: sumTotal.toFixed(2),
          reamark: '',
          formatVoidDate: '',
        },
      ];
    }
    return {
      ...createPaginationReportInfo(
        transformedData,
        total,
        qLimit || total,
        offset || 0,
      ),
      sumTotal,
    };
  }

  async getReportSaleRanking(
    req: CrudRequest,
    {
      keySearch,
      startDate,
      endDate,
      branchIds,
      limit,
      page,
      clientZoneName,
      categoryId,
      type,
      sortBy,
      sortOrder,
      currencyCode,
    }: ReportVoidedSaleQueryDTO,
    isExport = false,
  ) {
    const queryBuilder = this.orderDetailRepo
      .createQueryBuilder('orderDetail')
      .leftJoinAndSelect('orderDetail.order', 'order')
      .leftJoinAndSelect('order.branch', 'branch')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .leftJoinAndSelect('product.category', 'category')
      .leftJoinAndSelect('category.parent', 'categoryParent')
      .where('invoice.id IS NOT NULL');

    if (branchIds && branchIds.length) {
      queryBuilder.andWhere('invoice.branch.id IN (:...branchIds)', {
        branchIds,
      });
    }

    if (categoryId) {
      queryBuilder.andWhere('category.id IN (:...categoryIds)', {
        categoryIds: categoryId.split(','),
      });
    }
    let typeArr = [];
    if (type) {
      typeArr = type.split(',');
    } else {
      typeArr = [
        ProductType.MEMBERSHIP,
        ProductType.SERVICE,
        ProductType.PRODUCT,
        ProductType.COUPON,
      ];
    }
    queryBuilder.andWhere('product.type IN (:...type)', { type: typeArr });

    if (keySearch) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('product.name ILIKE :keySearch', {
            keySearch: `%${keySearch}%`,
          });
        }),
      );
    }
    if (
      startDate &&
      endDate &&
      moment(startDate).isValid() &&
      moment(endDate).isValid()
    ) {
      queryBuilder.andWhere('invoice.date BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }
    queryBuilder.andWhere('invoice.status IN (:...status)', {
      status: [InvoiceStatus.PAID],
    });
    queryBuilder.orderBy('invoice.date', 'DESC');
    const [orders, total] = await queryBuilder.getManyAndCount();

    let sumTotal = new Decimal(0);
    // duyệt mảng invoices bằng for
    const orderList = [];
    // return orders
    if (orders.length > 0) {
      for (const order of orders) {
        const productId = order.product.id;

        // Calculate discount for this order item
        let discount = 0;
        if (order.order.invoice?.discount > 0) {
          discount = await this.calculateDiscountForItem(
            order.id,
            (order?.order?.invoice?.payload as any)?.orders[0]?.items,
            order.order?.invoice?.discount,
          );
        }

        // Calculate total after discount: (Price * Qty) - Discount
        const totalBeforeDiscount = order.quantity * order.product.price;
        const totalAfterDiscount = totalBeforeDiscount - discount;

        // Calculate cost and profit
        const cost = order.product.cost * order.quantity;
        const profit = totalAfterDiscount - cost;

        if (orderList[productId]) {
          orderList[productId].quantity += order.quantity;
          orderList[productId].total += totalAfterDiscount;
          orderList[productId].cost += cost;
          orderList[productId].profit += profit;
          if (order.product.type === ProductType.MEMBERSHIP) {
            orderList[productId].prepaidQuantity += order.quantity;
            orderList[productId].prepaidTotal = new Decimal(
              orderList[productId].prepaidTotal.plus(totalAfterDiscount),
            );
          }
        } else {
          orderList[productId] = {
            quantity: order.quantity,
            total: totalAfterDiscount,
            item: order.product.name,
            type: order.product.type,
            category: order.product.category.name,
            cost: cost,
            profit: profit,
            prepaidQuantity: 0,
            prepaidTotal: new Decimal(0),
          };
          if (order.product.type === ProductType.MEMBERSHIP) {
            orderList[productId].prepaidQuantity = order.quantity;
            orderList[productId].prepaidTotal = new Decimal(totalAfterDiscount);
          }
        }
      }
    }
    const orderListKeys = Object.keys(orderList);
    const orderListData = [];
    let sumQuantity = 0;
    let sumCost = new Decimal(0);
    let sumProfit = new Decimal(0);
    let sumPrepaidTotal = new Decimal(0);
    let sumPrepaidQuantity = 0;
    for (const key of orderListKeys) {
      const order = orderList[key];
      order.total = order.total.toFixed(2);
      order.cost = order.cost.toFixed(2);
      order.profit = order.profit.toFixed(2);
      order.prepaidTotal = order.prepaidTotal.toFixed(2);
      orderListData.push(order);

      // gran total
      sumTotal = sumTotal.plus(order.total);
      sumQuantity += order.quantity;
      sumCost = sumCost.plus(order.cost);
      sumProfit = sumProfit.plus(order.profit);
      sumPrepaidTotal = sumPrepaidTotal.plus(order.prepaidTotal);
      sumPrepaidQuantity += order.prepaidQuantity;
    }
    // sort
    if (sortBy === 'total') {
      if (sortOrder === 'asc' || sortOrder === 'ASC') {
        orderListData.sort((a, b) => a.total - b.total);
      } else {
        orderListData.sort((a, b) => b.total - a.total);
      }
    } else {
      if (sortOrder === 'asc' || sortOrder === 'ASC') {
        orderListData.sort((a, b) => a.quantity - b.quantity);
      } else {
        orderListData.sort((a, b) => b.quantity - a.quantity);
      }
    }

    if (isExport) {
      // thêm order + 1 cho orListData
      orderListData.forEach((item, index) => {
        item.order = index + 1;
      });
      return [
        {
          order: '#',
          item: 'ITEM',
          type: 'TYPE',
          category: 'CATEGORY',
          prepaidQuantity: 'QTY',
          prepaidTotal: `TOTAL (${currencyCode})`,
          quantity: 'QTY',
          total: `TOTAL (${currencyCode})`,
          cost: `COST (${currencyCode})`,
          profit: `PROFIT (${currencyCode})`,
        },
        ...orderListData,
        {
          order: '',
          type: '',
          item: '',
          category: 'GRAND TOTAL:',
          prepaidQuantity: sumPrepaidQuantity,
          prepaidTotal: sumPrepaidTotal.toFixed(2),
          quantity: sumQuantity,
          total: sumTotal.toFixed(2),
          cost: sumCost.toFixed(2),
          profit: sumProfit.toFixed(2),
        },
      ];
    }
    return {
      data: orderListData,
      sumTotal,
      sumQuantity,
      sumCost,
      sumProfit,
      sumPrepaidTotal,
      sumPrepaidQuantity,
    };
  }

  async calculateDiscountForItem(
    orderItemId: string,
    items: Array<any>,
    totalDiscount: number,
  ) {
    let totalValue = 0;
    items.forEach((item) => {
      if (!item.product.isNotApplyDiscount) {
        totalValue += item.product.price * item.quantity;
      }
    });

    let discountPerItem = 0;
    if (totalValue > 0) {
      discountPerItem = totalDiscount / totalValue;
    }

    let result = 0;
    items.forEach((item) => {
      if (item.id === orderItemId && !item.product.isNotApplyDiscount) {
        const itemTotalValue = item.product.price * item.quantity;
        result = itemTotalValue * discountPerItem;
      }
    });
    return result;
  }
}
