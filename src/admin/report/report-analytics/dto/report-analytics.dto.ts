import { ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { IsString, IsUUID } from 'class-validator';
import { ReportQueryDTO } from '../../dto/report.dto';
import { UUID } from 'crypto';

export class ReportAuditTrailQueryDTO extends PickType(ReportQueryDTO, [
  'keySearch',
  'startDate',
  'endDate',
  'branchIds',
]) {
  @ApiPropertyOptional()
  @IsUUID()
  roleId: UUID;

  // sortBy
  @ApiPropertyOptional()
  @IsString()
  filterBy: string;
}

export class ReportVoidedSaleQueryDTO extends PickType(ReportQueryDTO, [
  'keySearch',
  'startDate',
  'endDate',
  'branchIds',
  'limit',
  'page',
  'clientZoneName',
  'currencyCode',
]) {
  @ApiPropertyOptional()
  @IsString()
  type: string;

  @ApiPropertyOptional()
  @IsUUID()
  categoryId: UUID;

  @ApiPropertyOptional()
  @IsString()
  sortBy: string;

  @ApiPropertyOptional()
  @IsString()
  sortOrder: string;
}
