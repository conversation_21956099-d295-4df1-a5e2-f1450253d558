import { ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { ReportQueryDTO } from '../../dto/report.dto';
import { IsDateString, IsEnum, IsOptional } from 'class-validator';
import { PrepaidFilter } from 'src/core/enums/entity';

export class ReportCustomerPrepaidQueryDTO extends PickType(ReportQueryDTO, [
  'keySearch',
  'startDate',
  'endDate',
  'page',
  'limit',
  'clientZoneName',
]) {
  @ApiPropertyOptional()
  @IsDateString()
  balanceOn?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(PrepaidFilter)
  prepaidFilter?: PrepaidFilter;
}
