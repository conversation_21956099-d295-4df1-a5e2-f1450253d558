import { ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { ReportQueryDTO } from '../../dto/report.dto';
import { CreditType } from 'src/core/enums/entity';

export class ReportCustomerMembershipQueryDTO extends PickType(ReportQueryDTO, [
  'keySearch',
  'startDate',
  'endDate',
  'page',
  'limit',
  'clientZoneName',
]) {
  @ApiPropertyOptional()
  creditType: string;

  @ApiPropertyOptional()
  showZero: any;
}
