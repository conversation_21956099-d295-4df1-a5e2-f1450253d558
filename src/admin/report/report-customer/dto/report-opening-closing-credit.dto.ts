import { ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { ReportQueryDTO } from '../../dto/report.dto';
import { OpeningClosingCreditType } from 'src/core/enums/entity';

export class ReportOpeningClosingCreditQueryDTO extends PickType(
  ReportQueryDTO,
  ['startDate', 'endDate', 'page', 'limit', 'clientZoneName'],
) {
  @ApiPropertyOptional()
  type: OpeningClosingCreditType;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  keySearch?: string;
}
