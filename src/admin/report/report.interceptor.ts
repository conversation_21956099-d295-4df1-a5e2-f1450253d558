import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import * as moment from 'moment-timezone';

import { Observable } from 'rxjs';
import { DataSource } from 'typeorm';
import { Branch } from '../branch/branch.entity';
import {
  getTimeZoneNameFromOffset,
  UTCtimeZone,
} from 'src/core/common/common.utils';

@Injectable()
export class ReportInterceptor implements NestInterceptor {
  constructor(private readonly dataSource: DataSource) {}

  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const req = context.switchToHttp().getRequest();
    const headers = req.headers;
    const query = req.query;

    //handle filter time
    const clientZone =
      query.startDate?.slice(-6) ||
      query.endDate?.slice(-6) ||
      moment().format('Z z');
    const clientZoneName = getTimeZoneNameFromOffset(clientZone);
    query.clientZoneName = clientZoneName;
    console.log('clientZoneName', clientZoneName);
    console.log('first', query.startDate, query.endDate);

    // Get the time zone name
    // Check if this is a customer spending report to use appropriate default date range
    const isCustomerSpendingReport = req.url.includes(
      'report-customer-spending',
    );

    if (isCustomerSpendingReport && !query.startDate) {
      // For customer spending with no start date, use a distant past date to get all records
      query.startDate = moment('2000-01-01').startOf('day').toISOString();
    } else {
      query.startDate = query.startDate
        ? moment
            .tz(query.startDate, clientZoneName)
            .tz(UTCtimeZone)
            .toISOString()
        : null;
    }

    if (isCustomerSpendingReport && !query.endDate) {
      // For customer spending with no end date, use future date to get all records
      query.endDate = moment().add(1, 'year').endOf('day').toISOString();
    } else {
      query.endDate = query.endDate
        ? moment.tz(query.endDate, clientZoneName).tz(UTCtimeZone).toISOString()
        : null;
    }

    console.log('parse', query.startDate, query.endDate);

    // query.balanceOn = query?.balanceOn
    //   ? moment
    //       .tz(query?.balanceOn, clientZoneName)
    //       .tz(UTCtimeZone)
    //       .toISOString()
    //   : moment().endOf('day').toISOString();

    query.balanceOn = query?.balanceOn
      ? moment
          .tz(query?.balanceOn, clientZoneName)
          .tz(UTCtimeZone)
          .toISOString()
      : null;

    query.purchaseStartDate = query?.purchaseStartDate
      ? moment
          .tz(query?.purchaseStartDate, clientZoneName)
          .tz(UTCtimeZone)
          .toISOString()
      : moment().startOf('day').toISOString();

    query.purchaseEndDate = query?.purchaseEndDate
      ? moment
          .tz(query?.purchaseEndDate, clientZoneName)
          .tz(UTCtimeZone)
          .toISOString()
      : moment().endOf('day').toISOString();

    //set filter branchIds
    query.branchIds = headers?.['branchid']?.split(',') || [];
    query.currencyCode =
      (
        await this.dataSource.getRepository(Branch).findOne({
          where: {
            id: query.branchIds[0],
          },
          select: ['id', 'code', 'name', 'currency'],
          relations: ['currency'],
        })
      )?.currency?.code || 'SGD';

    query.currencyBranchName =
      (
        await this.dataSource.getRepository(Branch).findOne({
          where: {
            id: query.branchIds[0],
          },
          select: ['id', 'code', 'name', 'currency'],
        })
      )?.name || 'Singapore';

    return next.handle().pipe();
  }
}
