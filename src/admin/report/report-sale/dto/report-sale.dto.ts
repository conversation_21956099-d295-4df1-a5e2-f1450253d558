import { ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';
import { ReportQueryDTO } from '../../dto/report.dto';
import { UUID } from 'crypto';

export class ReportProductDailySaleQueryDTO extends PickType(ReportQueryDTO, [
  'keySearch',
  'startDate',
  'endDate',
  'clientZoneName',
]) {
  @ApiPropertyOptional()
  @IsUUID()
  categoryId: UUID;
}

export class ReportServiceDailySaleQueryDTO extends PickType(ReportQueryDTO, [
  'keySearch',
  'startDate',
  'endDate',
  'clientZoneName',
]) {
  @ApiPropertyOptional()
  @IsUUID()
  categoryId: UUID;
}

export class ReportAlaCarteServiceDailySaleQueryDTO extends PickType(
  ReportQueryDTO,
  ['keySearch', 'startDate', 'endDate', 'clientZoneName'],
) {
  @ApiPropertyOptional()
  @IsUUID()
  categoryId: UUID;
}

export class ReportMembershipDailySaleQueryDTO extends PickType(
  ReportQueryDTO,
  ['keySearch', 'startDate', 'endDate', 'branchIds', 'clientZoneName'],
) {
  @ApiPropertyOptional()
  @IsUUID()
  categoryId: UUID;
}

export class ReportCouponDailySaleQueryDTO extends PickType(ReportQueryDTO, [
  'keySearch',
  'startDate',
  'endDate',
  'branchIds',
  'clientZoneName',
]) {
  @ApiPropertyOptional()
  @IsUUID()
  categoryId: UUID;
}

export class ReportZQueryDTO extends PickType(ReportQueryDTO, [
  'keySearch',
  'startDate',
  'endDate',
  'branchIds',
]) {}

export class ReportPaymentQueryDTO extends PickType(ReportQueryDTO, [
  'keySearch',
  'startDate',
  'endDate',
  'branchIds',
  'clientZoneName',
]) {
  @ApiPropertyOptional()
  @IsUUID()
  methodId: UUID;
}

export class ReportSaleTaxQueryDTO extends PickType(ReportQueryDTO, [
  'keySearch',
  'startDate',
  'endDate',
  'branchIds',
  'clientZoneName',
]) {}

export class ReportTaxSummaryQueryDTO extends PickType(ReportQueryDTO, [
  'keySearch',
  'startDate',
  'endDate',
  'branchIds',
  'clientZoneName',
]) {}

export class ReportFoodAndBeverageDailySaleQueryDTO extends PickType(
  ReportQueryDTO,
  [
    'keySearch',
    'startDate',
    'endDate',
    'branchIds',
    'clientZoneName',
    'categoryId',
    'categoryBeverageId',
    'limit',
    'page',
  ],
) {}

export class ReportSaleProfitQueryDTO extends PickType(ReportQueryDTO, [
  'startDate',
  'endDate',
  'branchIds',
  'limit',
  'page',
]) {}
