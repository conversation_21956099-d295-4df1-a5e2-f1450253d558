import { ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID } from 'class-validator';
import { ReportQueryDTO } from '../../dto/report.dto';
import { UUID } from 'crypto';
import { ProductType } from 'src/core/enums/entity';

export class ReportProductQueryDTO extends PickType(ReportQueryDTO, [
  'keySearch',
  'statusId',
  'branchIds',
  'currencyCode',
  'currencyCode',
]) {
  @ApiPropertyOptional()
  @IsUUID()
  categoryId: UUID;
}

export class ReportServicePerformQueryDTO extends PickType(ReportQueryDTO, [
  'keySearch',
  'startDate',
  'endDate',
  'branchIds',
  'limit',
  'page',
  'clientZoneName',
]) {
  @ApiPropertyOptional()
  @IsUUID()
  treatedById: UUID;

  @ApiPropertyOptional()
  @IsUUID()
  categoryId: UUID;
}
