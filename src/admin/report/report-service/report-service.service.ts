import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import { Brackets, ILike, In, Repository } from 'typeorm';

import { Category } from 'src/admin/category/category.entity';
import {
  ReportProductQueryDTO,
  ReportServicePerformQueryDTO,
} from './dto/report-service.dto';
import { Product } from 'src/admin/product/product.entity';
import { Prefix, ProductType } from 'src/core/enums/entity';
import * as moment from 'moment';
import { Decimal } from 'decimal.js';
import { OrderDetail } from '../../order-detail/order-detail.entity';
import { getCustomPaginationLimit } from '../../../core/common/common.utils';
import { createPaginationReportInfo } from '../utils/paginate.utils';
import { CommissionSettingService } from 'src/admin/settings/commission/commission-setting.service';

@Injectable()
export class ReportServiceService {
  constructor(
    @InjectRepository(Category)
    private categoryRepo: Repository<Category>,
    @InjectRepository(Product)
    private productRepo: Repository<Product>,
    @InjectRepository(OrderDetail)
    private orderDetailRepo: Repository<OrderDetail>,
    private readonly commissionSettingService: CommissionSettingService,
  ) {}

  async getReportService(
    req: CrudRequest,
    {
      keySearch,
      categoryId,
      statusId,
      branchIds,
      currencyCode,
    }: ReportProductQueryDTO, // : Promise< //   GetManyDefaultResponse<IReportProductResponse> | IReportProductResponse[] // >
    type: ProductType,
    isExport = false,
  ) {
    const { page, offset, limit, sort } = req?.parsed;

    const conditions = {
      type,
    };
    if (categoryId) {
      conditions['category.id'] = In(categoryId.split(','));
    }

    if (statusId) {
      conditions['status.id'] = In(statusId.split(','));
    }

    if (keySearch) {
      conditions['name'] = ILike(`%${keySearch}%`);
    }

    if (branchIds && branchIds.length > 0) {
      conditions['branches'] = {
        id: In(branchIds),
      };
    }

    const products = await this.productRepo.find({
      where: conditions,
      relations: ['category', 'category.parent', 'status', 'duration'],
      order: { name: 'ASC' },
    });

    const listCommission =
      await this.commissionSettingService.getAllCommissionSetting(
        branchIds,
        type,
        keySearch,
      );

    if (isExport) {
      const exportData = await Promise.all(
        products.map(async (p, index) => {
          let productName = p.name;
          if (p?.status?.name === 'Inactive') {
            productName += '          Inactive';
          }
          return {
            order: index + 1,
            name: productName,
            category: p.category?.name,
            section: p.category?.parent?.name,
            status: {
              id: p.status?.id,
              name: p.status?.name,
            },
            duration: {
              id: p.duration?.id,
              name: p.duration?.name,
            },
            cost: p.cost.toFixed(2),
            price: p.price.toFixed(2),
            commission: await this.getCommissionByProductId(
              p.id,
              listCommission,
              true,
            ),
            isMember: p.isMember ? 'Member' : 'Non-member',
            time: p.duration?.name,
          };
        }),
      );

      return {
        data: [
          {
            order: '',
            name: '',
            section: 'MAIN CATEGORY',
            category: 'CATEGORY',
            isMember: 'TYPE',
            time: 'TIME',
            cost: `COST (${currencyCode})`,
            price: `PRICE (${currencyCode})`,
            commission: {
              employee: 'THERAPIST',
              frondesk: 'FRONTDESK',
              staff: 'F&B',
            },
          },
          ...exportData,
        ],
      };
    }

    const data = await Promise.all(
      products.map(async (p, index) => {
        return {
          order: index + 1,
          name: p.name,
          category: p.category?.name,
          mainCategory: p.category?.parent?.name,
          section: p.category?.parent?.name,
          status: {
            id: p.status?.id,
            name: p.status?.name,
          },
          duration: {
            id: p.duration?.id,
            name: p.duration?.name,
          },
          cost: p.cost,
          price: p.price,
          commission:
            await this.commissionSettingService.getCommissionByProductId(
              p.id,
              listCommission,
            ),
          isMember: p.isMember,
        };
      }),
    );

    return {
      data,
      total: products.length,
    };
  }

  async getReportMembership(
    req: CrudRequest,
    {
      keySearch,
      categoryId,
      statusId,
      branchIds,
      currencyCode,
    }: ReportProductQueryDTO, // : Promise< //   GetManyDefaultResponse<IReportProductResponse> | IReportProductResponse[] // >
    type: ProductType,
    isExport = false,
  ) {
    const { page, offset, limit, sort } = req?.parsed;

    const conditions = {
      type,
    };
    if (categoryId) {
      conditions['category.id'] = In(categoryId.split(','));
    }

    if (statusId) {
      conditions['status.id'] = In(statusId.split(','));
    }

    if (keySearch) {
      conditions['name'] = ILike(`%${keySearch}%`);
    }

    if (branchIds && branchIds.length > 0) {
      conditions['branches'] = {
        id: In(branchIds),
      };
    }

    const products = await this.productRepo.find({
      where: conditions,
      relations: [
        'category',
        'category.parent',
        'status',
        'duration',
        'branches',
      ],
      order: { name: 'ASC' },
    });

    const listCommission =
      await this.commissionSettingService.getAllCommissionSetting(
        branchIds,
        type,
        keySearch,
      );

    if (isExport) {
      const exportData = await Promise.all(
        products.map(async (p, index) => {
          let productName = p.name;
          if (p.status?.name === 'Inactive') {
            productName += '          Inactive';
          }
          return {
            order: index + 1,
            name: productName,
            category: p.category?.name,
            section: p.category?.parent?.name,
            status: {
              id: p.status?.id,
              name: p.status?.name,
            },
            credit: p.credit.toFixed(2),
            cost: p.cost,
            price: p.price.toFixed(2),
            period: p.period,
            periodUnit: p.periodUnit,
            time: p.period ? p.period + ' ' + p.periodUnit : null,
            commission:
              await this.commissionSettingService.getCommissionByProductId(
                p.id,
                listCommission,
                true,
              ),
            branches: p.branches,
          };
        }),
      );

      return {
        data: [
          {
            order: '',
            name: '',
            category: 'CATEGORY',
            isMember: 'TYPE',
            cost: `COST (${currencyCode})`,
            price: `PRICE (${currencyCode})`,
            credit: 'CREDIT',
            time: 'PERIOD',
            commission: {
              employee: 'THERAPIST',
              frondesk: 'FRONTDESK',
              staff: 'F&B',
            },
          },
          ...exportData,
        ],
      };
    }

    const data = await Promise.all(
      products.map(async (p, index) => {
        return {
          order: index + 1,
          name: p.name,
          category: p.category?.name,
          section: p.category?.parent?.name,
          status: {
            id: p.status?.id,
            name: p.status?.name,
          },
          credit: p.credit,
          cost: p.cost,
          price: p.price,
          period: p.period,
          periodUnit: p.periodUnit,
          time: p.period + ' ' + p.periodUnit,
          commission:
            await this.commissionSettingService.getCommissionByProductId(
              p.id,
              listCommission,
            ),
          branches: p.branches,
        };
      }),
    );

    return {
      data,
      total: products.length,
    };
  }

  private normalizeDateRangeForSingleDay(
    start: string,
    end: string,
    timezone: string,
  ) {
    const startMoment = moment.tz(start, timezone);
    const endMoment = moment.tz(end, timezone);
    if (startMoment.isSame(endMoment, 'day')) {
      return {
        startTime: startMoment.startOf('day').toISOString(),
        endTime: startMoment.endOf('day').toISOString(),
      };
    }
    return {
      startTime: startMoment.toISOString(),
      endTime: endMoment.toISOString(),
    };
  }

  async getReportServicePerform(
    req: CrudRequest,
    {
      keySearch,
      startDate,
      endDate,
      branchIds,
      treatedById,
      categoryId,
      limit,
      page,
      clientZoneName,
    }: ReportServicePerformQueryDTO,
    type: ProductType,
    isExport = false,
  ) {
    let pTreatedById = [];
    if (treatedById) {
      pTreatedById = treatedById.split(',');
    }
    const queryBuilder = this.orderDetailRepo
      .createQueryBuilder('orderDetail')
      .leftJoinAndSelect('orderDetail.order', 'order')
      .leftJoinAndSelect('order.branch', 'branch')
      .leftJoinAndSelect('order.invoice', 'invoice')
      .leftJoinAndSelect('invoice.customer', 'customer')
      .leftJoinAndSelect('invoice.referral', 'referral')
      .leftJoinAndSelect('orderDetail.product', 'product')
      .leftJoinAndSelect('product.duration', 'duration')
      .leftJoinAndSelect('product.category', 'category')
      .leftJoinAndSelect('category.parent', 'categoryParent')
      .leftJoinAndSelect('orderDetail.employees', 'employees')
      .where('invoice.id IS NOT NULL')
      .andWhere('product.type = :type', { type });
    if (branchIds && branchIds.length) {
      queryBuilder.andWhere('branch.id IN (:...branchIds)', { branchIds });
    }
    if (keySearch) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('customer.firstName ILIKE :keySearch', {
            keySearch: `%${keySearch}%`,
          })
            .orWhere('CAST(customer.code AS TEXT) ILIKE :keySearch', {
              keySearch: `%${keySearch}%`,
            })
            .orWhere('customer.lastName ILIKE :keySearch', {
              keySearch: `%${keySearch}%`,
            })
            .orWhere(
              "concat(TRIM(customer.firstName), ' ', TRIM(customer.lastName)) ILIKE :keySearch",
              {
                keySearch: `%${keySearch}%`,
              },
            )
            .orWhere(
              "concat('IN', CAST(invoice.code AS TEXT)) ILIKE :keySearch",
              {
                keySearch: `%${keySearch}%`,
              },
            );
        }),
      );
    }
    if (pTreatedById && pTreatedById.length) {
      queryBuilder.andWhere('employees.id IN (:...pTreatedById)', {
        pTreatedById,
      });
    }
    if (categoryId) {
      queryBuilder.andWhere('category.id IN (:...categoryIds)', {
        categoryIds: categoryId.split(','),
      });
    }
    if (
      startDate &&
      endDate &&
      moment(startDate).isValid() &&
      moment(endDate).isValid()
    ) {
      const { startTime, endTime } = this.normalizeDateRangeForSingleDay(
        startDate,
        endDate,
        clientZoneName || 'Asia/Ho_Chi_Minh',
      );
      queryBuilder.andWhere('invoice.date BETWEEN :startDate AND :endDate', {
        startDate: startTime,
        endDate: endTime,
      });
    }
    queryBuilder.orderBy('invoice.date', 'ASC');
    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;
    if (!isExport) {
      queryBuilder.take(qLimit).skip(offset);
    }
    const [orders, total] = await queryBuilder.getManyAndCount();
    let sumQuantity = new Decimal(0);
    let sumActualValue = new Decimal(0);
    let sumValue = new Decimal(0);
    const transformedData = orders.map((ft, index) => {
      const total = new Decimal(ft.order?.total);
      const quantity = new Decimal(ft.quantity);
      const value = new Decimal(ft.product?.price).times(ft.quantity);
      const actualValue = new Decimal(value);
      sumQuantity = sumQuantity.plus(quantity);
      sumActualValue = sumActualValue.plus(actualValue);
      sumValue = sumValue.plus(value);
      return {
        order: index + 1,
        date: ft.order.invoice.date,
        formatDate: moment
          .tz(ft.order.invoice.date, clientZoneName)
          .format('DD/MM/YYYY h:mm A'),
        referenceNo: ft.order?.invoice.code,
        formatReferenceNo: Prefix.INVOICE + ft.order?.invoice.code,
        customer: ft.order?.invoice?.customer
          ? {
              id: ft.order.invoice.customer?.id,
              code: ft.order.invoice.customer?.code,
              name: `${ft.order.invoice.customer?.firstName} ${ft.order.invoice.customer?.lastName}`,
              formatName: `${ft.order.invoice.customer?.code} ${ft.order.invoice.customer?.firstName} ${ft.order.invoice.customer?.lastName}`,
            }
          : null,
        customerCode: ft.order?.invoice?.customer?.code,
        product: {
          id: ft.product?.id,
          name: ft.product?.name,
          duration: {
            id: ft.product?.duration?.id,
            name: ft.product?.duration?.name,
          },
        },
        category: {
          id: ft.product?.category?.id,
          name: ft.product?.category?.name,
        },
        employee: {
          sale: {
            id: ft.order.invoice?.referral?.id,
            name:
              ft.order.invoice?.referral?.username ||
              ft.order.invoice?.referral?.displayName ||
              ft.order.invoice?.referral?.fullname ||
              '',
          },
          service: ft.employees.map((obj) => ({
            name: obj?.fullName || obj?.displayName,
            id: obj.id,
          })),
        },
        quantity,
        value: value.toFixed(2),
        actualValue: actualValue.toFixed(2),
        treatedBy: ft.employees[0]?.displayName
          ? ft.employees.map((obj) => obj.displayName).join(',')
          : '',
      };
    });

    const groupedData = transformedData.reduce((result, current) => {
      const existing = result.find(
        (item) =>
          item.referenceNo === current.referenceNo &&
          item.product.id === current.product.id,
      );

      if (existing) {
        existing.quantity = (
          parseFloat(existing.quantity.toString()) +
          parseFloat(current.quantity.toString())
        ).toString();
        existing.value = (
          parseFloat(existing.value) + parseFloat(current.value)
        ).toFixed(2);
        existing.actualValue = (
          parseFloat(existing.actualValue) + parseFloat(current.actualValue)
        ).toFixed(2);
      } else {
        result.push({ ...current });
      }

      return result;
    }, []);
    if (isExport) {
      return [
        ...groupedData,
        {
          order: '',
          formatDate: '',
          formatReferenceNo: '',
          customer: {
            formatName: '',
          },
          employee: {
            sale: {
              name: '',
            },
          },
          product: {
            name: 'GRAND TOTAL:',
          },
          treatedBy: '',
          quantity: sumQuantity,
          value: sumValue.toFixed(2),
          actualValue: sumActualValue.toFixed(2),
        },
      ];
    }
    return {
      ...createPaginationReportInfo(
        groupedData,
        total,
        qLimit || total,
        offset || 0,
      ),
      sumQuantity,
      sumActualValue,
      sumValue,
    };
  }

  async getCommissionByProductId(
    productId: string,
    commissionListData: any[],
    isExport = false,
  ) {
    const commission = commissionListData.find(
      (c) => c.product.id === productId,
    );
    if (commission) {
      if (isExport) {
        return {
          employee: commission.employee + '%',
          frondesk: commission.frontdesk + '%',
          staff: commission.fBStaff + '%',
        };
      }
      return {
        employee: commission.employee,
        frondesk: commission.frontdesk,
        staff: commission.fBStaff,
      };
    }
    if (isExport) {
      return {
        employee: '2%',
        frondesk: '10%',
        staff: '5%',
      };
    }
    return {
      employee: 2,
      frondesk: 10,
      fBStaff: 5,
    };
  }
}
