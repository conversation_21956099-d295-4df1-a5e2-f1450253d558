import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CrudRequest } from 'src/core/crud/crud';
import { Brackets, Repository } from 'typeorm';

import { ReportReturnedItemQueryDTO } from './dto/report-return.dto';
import { CreditHistory } from '../../credit-history/credit-history.entity';
import * as moment from 'moment';
import { getCustomPaginationLimit } from '../../../core/common/common.utils';
import { Decimal } from 'decimal.js';
import { Prefix } from '../../../core/enums/entity';
import { createPaginationReportInfo } from '../utils/paginate.utils';

@Injectable()
export class ReportReturnService {
  constructor(
    @InjectRepository(CreditHistory)
    private creditHistoryRepo: Repository<CreditHistory>,
  ) {}

  async getReportReturnedItem(
    req: CrudRequest,
    {
      keySearch,
      startDate,
      endDate,
      branchIds,
      limit,
      page,
      clientZoneName,
    }: ReportReturnedItemQueryDTO,
    isExport = false,
  ) {
    const queryBuilder = this.creditHistoryRepo
      .createQueryBuilder('creditHistory')
      .leftJoinAndSelect('creditHistory.credit', 'credit')
      .leftJoinAndSelect('credit.creditSetting', 'creditSetting')
      .leftJoinAndSelect('credit.customer', 'customer')
      .leftJoinAndSelect('credit.branch', 'branch')
      .where('creditHistory.isRefund = :isRefund', { isRefund: true });
    if (branchIds && branchIds.length) {
      queryBuilder.andWhere('credit.branch.id IN (:...branchIds)', {
        branchIds,
      });
    }
    if (keySearch) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('customer.firstName ILIKE :keySearch', {
            keySearch: `%${keySearch}%`,
          }).orWhere('customer.lastName ILIKE :keySearch', {
            keySearch: `%${keySearch}%`,
          });
        }),
      );
    }
    if (
      startDate &&
      endDate &&
      moment(startDate).isValid() &&
      moment(endDate).isValid()
    ) {
      queryBuilder.andWhere(
        'creditHistory.refundDate BETWEEN :startDate AND :endDate',
        {
          startDate,
          endDate,
        },
      );
    }
    queryBuilder.orderBy('creditHistory.refundDate', 'ASC');
    const qLimit = getCustomPaginationLimit(limit);
    const offset = (page - 1) * limit || 0;
    if (!isExport) {
      queryBuilder.take(qLimit).skip(offset);
    }
    const [creditHistories, total] = await queryBuilder.getManyAndCount();
    let sumValue = new Decimal(0);
    const transformedData = creditHistories.map((ft, index) => {
      const detail = ft.detail as any;
      const value = ft.refundValue ?? ft.value ?? 0;
      sumValue = sumValue.plus(value);
      return {
        order: index + 1,
        date: ft.refundDate,
        formatDate: ft.refundDate
          ? moment.tz(ft.refundDate, clientZoneName).format('DD/MM/YYYY h:mm A')
          : '',
        referenceNo: 'CN' + detail?.code,
        formatReferenceNo: Prefix.INVOICE + detail?.code,
        purchaseDate: detail?.date,
        formatPurchaseDate: detail?.date
          ? moment.tz(detail?.date, clientZoneName).format('DD/MM/YYYY h:mm A')
          : '',
        customer: ft.credit?.customer
          ? {
              id: ft.credit?.customer?.id,
              code: ft.credit?.customer?.code,
              name: `${ft.credit?.customer?.firstName} ${ft.credit?.customer?.lastName}`,
              formatName: `${ft.credit?.customer?.code} ${ft.credit?.customer?.firstName} ${ft.credit?.customer?.lastName}`,
            }
          : null,
        product: {
          id: detail?.orders?.[0]?.product?.id,
          name: detail?.orders?.[0]?.product?.name,
        },
        employee: {
          sale: {
            id: detail?.referral?.id,
            name:
              detail?.referral?.username ||
              detail?.referral?.displayName ||
              detail?.referral?.fullname ||
              '',
          },
        },
        returned: ft.paid?.toFixed(2),
        value: value?.toFixed(2),
      };
    });
    if (isExport) {
      return [
        ...transformedData,
        {
          order: '',
          formatDate: '',
          formatReferenceNo: '',
          formatPurchaseDate: '',
          customer: {
            formatName: '',
          },
          employee: {
            sale: {
              name: '',
            },
          },
          product: {
            name: '',
          },
          returned: 'GRAND TOTAL:',
          value: sumValue.toFixed(2),
        },
      ];
    }
    return {
      ...createPaginationReportInfo(
        transformedData,
        total,
        qLimit || total,
        offset || 0,
      ),
      sumValue,
    };
  }
}
