import { BaseModule } from 'src/core/base/base.module';
import { UserModule } from './user/user.module';
import { AuthModule } from './auth/auth.module';
import { BranchModule } from './branch/branch.module';
import { CustomerModule } from './customer/customer.module';
import { EmployeeModule } from './employee/employee.module';
import { ProductModule } from './product/product.module';
import { DayOffModule } from './day-off/day-off.module';
import { WorkingHourModule } from './working-hour/working-hour.module';
import { SettingModule } from './setting/setting.module';
import { CategoryModule } from './category/category.module';
import { EmployeeSettingModule } from './employe-setting/employee-setting.module';
import { RfidModule } from './rfid/rfid.module';
import { PaymentMethodModule } from './payment-method/payment-method.module';
import { InvoiceModule } from './invoice/invoice.module';
import { CreditModule } from './credit/credit.module';
import { CreditSettingModule } from './settings/credit/credit-setting.module';
import { AppointmentModule } from './appointment/appointment.module';
import { OrderModule } from './order/order.module';
import { OrderDetailModule } from './order-detail/order-detail.module';
import { CreditHistoryModule } from './credit-history/credit-history.module';
import { MediaModule } from './media/media.module';
import { PayslipSettingModule } from './settings/payslip/payslip-setting.module';
import { CommissionSettingModule } from './settings/commission/commission-setting.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { FilterHeaderBranchInterceptor } from 'src/admin/branch/interceptors/filterHeaderBranch.interceptor';
import { CrudRequestInterceptor } from 'src/core/crud/crud';
import { CurrencyModule } from './currency/currency.module';
import { SalesModule } from './sales/sales.module';
import { SeedModule } from './seeder/seed.module';
import { ReportModule } from './report/report.module';
import { IssueCouponModule } from './inventory/issue-coupon.module';
import { ResourceModule } from './resource/resource.module';
import { FbOrderModule } from './fb-order/fb-order.module';
import { AuditTrailModule } from './audit-trail/audit-trail.module';
import { CouponSettingModule } from './settings/coupon/coupon-setting.module';

@BaseModule({
  imports: [
    AuthModule,
    UserModule,
    BranchModule,
    CustomerModule,
    EmployeeModule,
    EmployeeSettingModule,
    ProductModule,
    DayOffModule,
    WorkingHourModule,
    SettingModule,
    // CouponModule,
    IssueCouponModule,
    CategoryModule,
    RfidModule,
    PaymentMethodModule,
    InvoiceModule,
    CreditModule,
    CreditSettingModule,
    AppointmentModule,
    OrderModule,
    OrderDetailModule,
    CreditHistoryModule,
    MediaModule,
    PayslipSettingModule,
    CommissionSettingModule,
    CurrencyModule,
    SalesModule,
    SeedModule,
    ReportModule,
    ResourceModule,
    FbOrderModule,
    AuditTrailModule,
    CouponSettingModule,
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: CrudRequestInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: FilterHeaderBranchInterceptor,
    },
  ],
})
export class AppAdminModules {}
