import { TypeOrmModule } from '@nestjs/typeorm';
import { MediaController } from './media.controller';
import { Media } from './media.entity';
import { MediaService } from './media.service';
import { Module } from '@nestjs/common';
import { CloudinaryModule } from 'src/core/cloudinary/cloudinary.module';

@Module({
  imports: [TypeOrmModule.forFeature([Media]), CloudinaryModule],
  controllers: [MediaController],
  providers: [MediaService],
  exports: [MediaService],
})
export class MediaModule {}
