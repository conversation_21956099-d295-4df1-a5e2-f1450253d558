import { ApiProperty } from '@nestjs/swagger';
import { DocEntity } from 'src/core/base/doc.entity';
import { Column, Entity, OneToOne } from 'typeorm';
import { User } from '../user/user.entity';
import { Product } from '../product/product.entity';
import { Category } from '../category/category.entity';

@Entity()
export class Media extends DocEntity {
  @Column()
  @ApiProperty()
  url: string;

  @Column()
  @ApiProperty()
  key: string;

  @Column()
  @ApiProperty({ readOnly: true })
  md5: string;

  @OneToOne(() => User, (user) => user.avatar)
  @ApiProperty({
    type: () => User,
  })
  user: User;

  @OneToOne(() => Product, (product) => product.avatar)
  @ApiProperty({
    type: () => Product,
  })
  product: Product;

  @OneToOne(() => Category, (product) => product.avatar)
  @ApiProperty({
    type: () => Category,
  })
  category: Category;
}
