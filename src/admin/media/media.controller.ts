import { Crud<PERSON>ontroller, CrudRequest, ParsedRequest } from 'src/core/crud/crud';
import { Media } from './media.entity';
import { MediaService } from './media.service';
import { Post, UseInterceptors, UploadedFile } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Express } from 'express';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';

@BaseCrud(
  {
    model: {
      type: Media,
    },
    routes: {
      only: ['getOneBase'],
    },
  },
  {},
)
export class MediaController implements CrudController<Media> {
  constructor(public service: MediaService) {}
  get base(): CrudController<Media> {
    return this;
  }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  upload(
    @ParsedRequest() req: CrudRequest,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.service.uploadWithCloudinary(req, file);
  }
}
