import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Media } from './media.entity';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { v4 as uuid } from 'uuid';
// import { S3 } from 'aws-sdk';
import { md5 } from 'src/core/crypto/crypto.provider';
import { CrudRequest } from 'src/core/crud/crud';
import { pick } from 'lodash';
import { CloudinaryService } from 'src/core/cloudinary/cloudinary.service';
import { waMessage } from 'src/core/exception/exception.messages.contants';

@Injectable()
export class MediaService extends BaseCrudService<Media> {
  constructor(
    @InjectRepository(Media) repo: Repository<Media>,
    private readonly cloudinaryService: CloudinaryService,
  ) {
    super(repo);
  }

  // async uploadWithS3(req: CrudRequest, file: Express.Multer.File) {
  //   // const s3 = new S3();
  //   const s3 = new S3({
  //     credentials: {
  //       accessKeyId: 'AKIAIOSFODNN7EXAMPLE',
  //       secretAccessKey: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
  //     },
  //     // accessKeyId: 'AKIAIOSFODNN7EXAMPLE',
  //     // secretAccessKey: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
  //     endpoint: 'http://localhost:9444',
  //     s3ForcePathStyle: true,
  //     region: 'http://localhost:9444/s3',
  //     logger: console,
  //   });

  //   const uploadResult = await s3
  //     .upload({
  //       Bucket: 'test-one',
  //       Body: file.buffer,
  //       Key: `${uuid()}-${file.originalname}`,
  //     })
  //     .promise();

  //   const newFile = this.repo.create({
  //     key: uploadResult.Key,
  //     url: uploadResult.Location,
  //     md5: md5(file.buffer.toString('utf8')),
  //   });
  //   const upload = await this.repo.save(newFile);
  //   return pick(upload, ['id', 'url', 'key']);
  // }

  async uploadWithCloudinary(req: CrudRequest, file: Express.Multer.File) {
    if (!this.checkIsImage(file.originalname)) {
      throw new BadRequestException(waMessage.exception.invalidImage.message);
    }
    const uploadResult = await this.cloudinaryService.uploadImage(file);

    const newFile = this.repo.create({
      id: uploadResult.asset_id,
      key: uploadResult.public_id,
      url: uploadResult.url,
      md5: md5(file.buffer.toString('utf8')),
    });

    const upload = await this.repo.save(newFile);

    return pick(upload, ['id', 'url', 'key']);
  }
  private checkIsImage(url) {
    return url.match(/\.(jpeg|jpg|gif|png|bmp)$/) != null;
  }
}
