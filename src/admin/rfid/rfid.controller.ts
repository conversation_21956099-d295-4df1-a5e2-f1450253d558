import { CrudController, Override } from 'src/core/crud/crud';
import { BaseCrudController } from 'src/core/base/base-crud.controller';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import { Rfid } from './rfid.entity';
import { RfidService } from './rfid.service';
import { RfidDto } from './dto/createRfid.dto';
import { Get, Query, Req } from '@nestjs/common';

@BaseCrud(
  {
    model: {
      type: Rfid,
    },
    routes: {
      exclude: ['createManyBase', 'replaceOneBase'],
    },
    dto: {
      create: RfidDto,
    },
    query: {
      join: {
        branch: { eager: true, allow: ['id', 'name'] },
        group: { eager: true, allow: ['id', 'name'] },
        status: { eager: true, allow: ['id', 'name'] },
      },
    },
  },
  {
    // grantPerm: userPerm,
    // group: ResourceGroup.SYSTEM,
  },
)
export class RfidController extends BaseCrudController<Rfid> {
  constructor(public service: RfidService) {
    super(service);
  }
  get base(): CrudController<Rfid> {
    return this;
  }

  @Get('/by-code')
  getRfidByCode(@Query('code') code: string, @Req() req: Request) {
    const branchIds = req.headers?.['branchid']?.split(',') || [];
    return this.service.getByCode(code, branchIds);
  }

  @Override('getManyBase')
  async getMany(
    @Req() req: Request,
    @Query() { keySearch }: { keySearch: string },
  ) {
    const branchIds = req.headers?.['branchid']?.split(',') || [];
    return this.service.getRfidList(branchIds, keySearch);
  }
}
