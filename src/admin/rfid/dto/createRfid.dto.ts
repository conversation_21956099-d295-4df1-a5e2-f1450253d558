import { ApiProperty, PickType } from '@nestjs/swagger';
import { Rfid } from '../rfid.entity';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';
import { BranchRelationDto } from 'src/admin/branch/dto/Branch.dto';
import { Branch } from 'src/admin/branch/branch.entity';
import { Setting } from 'src/admin/setting/setting.entity';

export class RfidDto extends PickType(Rfid, ['id']) {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  serialCode: string;

  @ApiProperty()
  @IsNotEmpty()
  @ApiProperty({ type: () => Setting })
  group: Setting;

  @ApiProperty({
    type: () => BranchRelationDto,
  })
  @IsNotEmpty()
  branch: Branch;

  // @ApiProperty()
  // @IsNotEmpty()
  // @IsNumber()
  // lockerNumber: number;
}
