import {
  Entity,
  Column,
  ManyToOne,
  Index,
  OneToOne,
  Join<PERSON><PERSON>umn,
} from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Setting } from '../setting/setting.entity';
import { Branch } from '../branch/branch.entity';
import { Appointment } from '../appointment/appointment.entity';

@Entity('rfid')
@Index(
  'unique_locker_index',
  ['branch', 'group', 'lockerNumber'],
  {
    unique: true,
    where: 'deleted IS NULL',
  },
)
export class Rfid extends DocEntity {
  @Column({
    nullable: false,
  })
  @Index({ unique: true, where: 'deleted IS NULL' })
  @ApiProperty()
  serialCode: string;

  @Column({ type: 'bigint', nullable: false })
  tagId: number;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Setting })
  group: Setting;

  @ManyToOne(() => Branch)
  @ApiProperty({ type: () => Branch })
  branch: Branch;

  @ManyToOne(() => Appointment)
  @ApiProperty({ type: () => Appointment })
  appointment: Appointment;

  @Column({ nullable: true })
  @ApiPropertyOptional()
  token?: string;

  @Column({
    nullable: false,
  })
  @ApiProperty()
  lockerNumber: number;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Setting })
  status?: Setting;
}
