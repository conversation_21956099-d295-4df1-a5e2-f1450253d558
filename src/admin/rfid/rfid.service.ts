import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, ILike, In, Raw, Repository } from 'typeorm';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { Rfid } from './rfid.entity';
import { Order } from '../order/order.entity';
import { CouponType, CreditType, InvoiceStatus } from 'src/core/enums/entity';
import { Decimal } from 'decimal.js';
import { CouponItem } from '../inventory/coupon-item.entity';
import { Invoice } from '../invoice/invoice.entity';
import { Credit } from '../credit/credit.entity';

@Injectable()
export class RfidService extends BaseCrudService<Rfid> {
  constructor(@InjectRepository(Rfid) repo: Repository<Rfid>) {
    super(repo);
  }

  async getRfidList(branchIds: string[], keySearch?: string): Promise<any> {
    // const whereClause: Record<string, any> = {};
    // if (branchIds.length > 0) {
    //   whereClause.branch = {};
    //   whereClause.branch.id = In(branchIds);
    // }
    //
    // const rfid = await this.repo.find({
    //   where: !keySearch
    //     ? whereClause
    //     : [
    //         {
    //           ...whereClause,
    //           appointment: {
    //             customer: {
    //               firstName: ILike(`%${keySearch}%`),
    //             },
    //           },
    //         },
    //         {
    //           ...whereClause,
    //           appointment: {
    //             customer: {
    //               lastName: ILike(`%${keySearch}%`),
    //             },
    //           },
    //         },
    //         {
    //           ...whereClause,
    //           serialCode: ILike(`%${keySearch}%`),
    //         },
    //         {
    //           ...whereClause,
    //           lockerNumber: Raw((i) => `CAST(${i} AS TEXT) Ilike :keySearch`, {
    //             keySearch: `%${keySearch}%`,
    //           }),
    //         },
    //       ],
    //   relations: [
    //     'branch',
    //     'group',
    //     'status',
    //     'appointment',
    //     'appointment.customer',
    //     'appointment.customer.avatar',
    //   ],
    // });
    const queryBuilder = this.repo.createQueryBuilder('rfid');
    queryBuilder
      .leftJoinAndSelect('rfid.branch', 'branch')
      .leftJoinAndSelect('rfid.group', 'group')
      .leftJoinAndSelect('rfid.status', 'status')
      .leftJoinAndSelect('rfid.appointment', 'appointment')
      .leftJoinAndSelect('appointment.customer', 'customer')
      .leftJoinAndSelect('customer.avatar', 'avatar');
    if (branchIds.length > 0) {
      queryBuilder.andWhere('rfid.branchId IN (:...branchIds)', { branchIds });
    }
    if (keySearch) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('customer.firstName ILIKE :keySearch')
            .orWhere('customer.lastName ILIKE :keySearch')
            .orWhere(
              "concat(TRIM(customer.firstName), ' ', TRIM(customer.lastName)) ILIKE :keySearch",
            )
            .orWhere('rfid.serialCode ILIKE :keySearch')
            .orWhere('CAST(rfid.lockerNumber AS TEXT) ILIKE :keySearch');
        }),
        { keySearch: `%${keySearch}%` },
      );
    }
    const rfid = await queryBuilder.getMany();
    const result = rfid.reduce((acc, item) => {
      const gender =
        item?.group && item?.group?.type === 'group'
          ? item?.group.name.toLowerCase()
          : null;

      if (gender) {
        if (!acc[gender]) {
          acc[gender] = {
            total: 0,
            used: 0,
            empty: 0,
            data: [],
          };
        }
        acc[gender].used +=
          item?.token && item?.appointment?.customer?.id ? 1 : 0;
        acc[gender].empty +=
          item?.status?.name.toLocaleLowerCase() == 'active' && !item?.token
            ? 1
            : 0;
        acc[gender].total++;
        acc[gender].data.push({
          id: item?.id,
          lockerNumber: item?.lockerNumber,
          token: item?.token,
          tagId: item?.tagId,
          serialCode: item?.serialCode,
          group: { id: item?.group?.id, name: item?.group?.name },
          status: { id: item?.status?.id, name: item?.status?.name },
          branch: { id: item?.branch?.id, name: item?.branch?.name },
          customer:
            item?.token && item?.appointment?.customer?.id
              ? {
                  firstName: item?.appointment?.customer?.firstName,
                  lastName: item?.appointment?.customer?.lastName,
                  remark: item?.appointment?.customer?.remark,
                  nric: item?.appointment?.customer?.nric,
                  avatar: item?.appointment?.customer?.avatar,
                  gender: item?.group?.name,
                  checkIn: item?.appointment?.checkIn,
                  rfid: item?.serialCode,
                }
              : {},
        });
      }
      return acc;
    }, {});

    // sort ASC by lockerNumber
    Object.keys(result).forEach((gender) => {
      result[gender].data.sort((a, b) => a.lockerNumber - b.lockerNumber);
    });
    return result;
  }

  async getByCode(code: string, branchIds: string[]): Promise<Rfid> {
    const whereClause: Record<string, any> = {};
    if (branchIds.length > 0) {
      whereClause.branch = {};
      whereClause.branch.id = In(branchIds);
    }
    whereClause.serialCode = code;
    whereClause.status = { name: 'Active' };
    const rfid = await this.repo.findOne({
      where: whereClause,
      relations: [
        'appointment',
        'appointment.customer',
        'appointment.customer.gender',
        'appointment.customer.avatar',
        'appointment.customer.credits',
        'appointment.customer.credits.creditSetting',
        'appointment.customer.preferreds',
        'appointment.customer.nationality',
        'appointment.orders',
        'appointment.orders.items',
        'appointment.orders.items.product',
        'appointment.orders.items.employees',
        'appointment.orders.invoice',
        'group',
      ],
    });

    if (!rfid) {
      throw new NotFoundException(`not found RFID by code ${code}`);
    }
    if (!rfid?.appointment) return rfid;
    // Get list orders and filter
    if (rfid?.appointment?.orders.length > 0) {
      rfid.appointment.orders = rfid.appointment.orders
        .filter(function (order) {
          return order.invoice === null && order.status !== InvoiceStatus.VOID;
        })
        .sort(function (a: Order, b: Order) {
          return new Date(a.created).getTime() - new Date(b.created).getTime();
        });
    }
    // Get list invoices by appointment
    const invoices = await this.repo.manager.find(Invoice, {
      where: {
        appointment: {
          id: rfid?.appointment?.id,
        },
        status: InvoiceStatus.PART_PAID,
      },
      relations: [
        'invoiceCoupon',
        'invoicePayments',
        'invoicePayments.paymentMethod',
        'orders',
        'orders.items',
        'orders.items.employees',
        'orders.items.product',
        'customer',
      ],
    });
    if (invoices.length) {
      for (const invoice of invoices) {
        // if (invoice.orders.length > 0) {
        //   invoice.orders.forEach((order) => {
        //     if (order.payload) {
        //       const orderPayload: any = order.payload;
        //       const items: any[] = orderPayload.items;
        //       order.items = items;
        //     }
        //   });
        // }
        let sumDiscount = new Decimal(0);
        let sumPriceForDiscount = new Decimal(0);
        let sumSubTotal = new Decimal(0);
        let isEdited = false;
        for (const order of invoice.orders) {
          // const orderPayload: any = order.payload;
          for (const productItem of order.items) {
            const priceItem = !productItem?.couponCode
              ? new Decimal(productItem.quantity).times(
                  new Decimal(productItem?.product?.price),
                )
              : new Decimal(0);
            if (!productItem.product.isNotApplyDiscount) {
              sumPriceForDiscount = sumPriceForDiscount.plus(priceItem);
            }
          }
          sumSubTotal = sumSubTotal.plus(new Decimal(order.subTotal));
          if (order.isEdited) {
            isEdited = order.isEdited;
          }
        }
        if (invoice.invoiceCoupon) {
          const discountMoney = await this.processValueDiscount(
            invoice,
            sumPriceForDiscount,
          );
          sumDiscount = sumDiscount.plus(new Decimal(discountMoney));
        }
        let totalBeforeTax = sumSubTotal.minus(sumDiscount);
        let totalCreditPaid = new Decimal(0);
        for (const payment of invoice.invoicePayments) {
          if (
            payment.paymentMethod?.code.toLowerCase() === CreditType.NEW ||
            payment.paymentMethod?.code.toLowerCase() === CreditType.OLD
          ) {
            totalBeforeTax = totalBeforeTax.minus(new Decimal(payment.paid));
            totalCreditPaid = totalCreditPaid.plus(new Decimal(payment.paid));
          }
        }
        const newTotalPaid = totalBeforeTax.isPositive()
          ? totalBeforeTax
              .plus(totalBeforeTax.times(0.09))
              .plus(totalCreditPaid)
          : new Decimal(0);
        invoice['newPaid'] = {
          totalPaid: parseFloat(newTotalPaid.toFixed(2)),
          subTotal: parseFloat(sumSubTotal.toFixed(2)),
          totalBeforeTax: parseFloat(totalBeforeTax.toFixed(2)),
          invoiceCoupon: invoice.invoiceCoupon,
          isEdited,
        };
      }
      rfid.appointment.invoices = invoices;
    }
    return rfid;
  }

  async findOneByToken(token: string): Promise<any> {
    if (!token) {
      throw new NotFoundException();
    }
    const rfid = await this.repo.findOne({
      where: { token },
      relations: [
        'appointment',
        'appointment.branch',
        'appointment.customer',
        'appointment.customer.avatar',
        'appointment.customer.credits',
        'appointment.customer.credits.creditSetting',
        'appointment.orders',
        'appointment.orders.items',
        'appointment.orders.items.product',
        'appointment.orders.items.employees',
        'group',
      ],
    });

    if (!rfid) {
      throw new NotFoundException();
    }
    return rfid.appointment;
  }

  async processValueDiscount(invoice, sumPriceForDiscount) {
    let creditBalance = new Decimal(0);
    const invoicePayments = invoice?.invoicePayments;
    if (invoicePayments) {
      for (const invoicePayment of invoicePayments) {
        if (
          invoicePayment.paymentMethod.code === CreditType.NEW ||
          invoicePayment.paymentMethod.code === CreditType.OLD
        ) {
          creditBalance = creditBalance.plus(invoicePayment.paid);
        }
      }
    }
    const customer = invoice?.customer;
    if (customer) {
      const credits = await this.repo.manager.find(Credit, {
        where: {
          customer: { id: customer.id },
        },
        relations: ['creditSetting'],
      });
      if (credits?.length) {
        for (const credit of credits) {
          creditBalance = creditBalance.plus(credit.creditBalance);
        }
      }
      if (
        creditBalance.greaterThan(0) &&
        creditBalance.lessThan(sumPriceForDiscount)
      ) {
        sumPriceForDiscount = creditBalance;
      }
    }
    let sumDiscountMoney = new Decimal(0);
    for (const coupon of invoice.invoiceCoupon) {
      let discountMoney = new Decimal(0);
      let value = new Decimal(0);
      let type;
      // Handle old data of coupon in invoice
      if (coupon.couponType === CouponType.CODE) {
        const whereClause: Record<string, any> = {
          code: coupon.couponCode,
        };
        const existCouponCode = await this.repo.manager.findOne(CouponItem, {
          where: whereClause,
          relations: [
            'issueCoupon',
            'branches',
            'issueCoupon.coupon',
            'issueCoupon.coupon.status',
            'issueCoupon.coupon.discountType',
            'issueCoupon.status',
          ],
        });
        value = new Decimal(
          existCouponCode?.issueCoupon?.coupon?.discountValue,
        );
        type = existCouponCode?.issueCoupon?.coupon?.discountType?.name;
      }
      if (coupon.couponType === CouponType.MONEY) {
        value = new Decimal(coupon.discountValue);
        type = coupon.couponType;
      }
      if (coupon.couponType === CouponType.PERCENT) {
        value = new Decimal(coupon.percent);
        type = coupon.couponType;
      }
      // End: Handle old data of coupon in invoice
      // Handle new data of coupon when order update new price paid
      if (type && type === CouponType.MONEY) {
        discountMoney = discountMoney.plus(value);
      }
      if (type && type === CouponType.PERCENT) {
        discountMoney = discountMoney.plus(
          sumPriceForDiscount.times(new Decimal(value)).dividedBy(100),
        );
      }
      sumDiscountMoney = sumDiscountMoney.plus(discountMoney);
      coupon.discountValue = parseFloat(discountMoney.toFixed(2));
      // End: Handle new data of coupon when order update new price paid
    }
    return sumDiscountMoney;
  }
}
