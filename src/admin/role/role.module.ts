import { TypeOrmModule } from '@nestjs/typeorm';
import { RoleController } from './role.controller';
import { Role } from './role.entity';
import { RoleService } from './role.service';
import { Module } from '@nestjs/common';
import { Permission } from '../permission/permission.entity';
import { ResourceModule } from '../resource/resource.module';
import { User } from '../user/user.entity';
import { Setting } from '../setting/setting.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Role, Permission, User, Setting]), ResourceModule],
  controllers: [RoleController],
  providers: [RoleService],
  exports: [RoleService],
})
export class RoleModule {}
