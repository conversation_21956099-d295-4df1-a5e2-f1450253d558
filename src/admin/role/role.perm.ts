import { RoleBuiltIn } from 'src/core/database/type/index';
import { IGrantPerm } from 'src/core/base/decorator/grantPerm.decorator';

export const rolePerm: IGrantPerm[] = [
  {
    roleId: RoleBuiltIn.ADMIN,
    create: true,
    read: true,
    update: true,
    delete: false,
    list: true,
  },
  {
    roleId: RoleBuiltIn.STAFF,
    create: true,
    read: true,
    update: true,
    delete: false,
    list: true,
  },
];
