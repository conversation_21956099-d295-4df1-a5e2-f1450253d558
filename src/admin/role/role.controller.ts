import {
  <PERSON>rud<PERSON><PERSON><PERSON>er,
  CrudRequest,
  Override,
  ParsedBody,
  ParsedRequest,
} from 'src/core/crud/crud';
import { Role } from './role.entity';
import { RoleService } from './role.service';
import { BaseCrud } from '../auth/decorators/base-crud.decorator';
import {Get, Param, Query, Req} from '@nestjs/common';
import { ResourceService } from '../resource/resource.service';
import { v4 as uuidv4 } from 'uuid';

@BaseCrud({
  model: {
    type: Role,
  },
  routes: {
    exclude: ['createManyBase'],
  },
  query: {
    join: {
      status: { eager: true, allow: ['id', 'name'] },
      permission: { eager: true },
      'permission.resource': { eager: true },
    },
  },
})
export class RoleController implements CrudController<Role> {
  constructor(
    public service: RoleService,
    private readonly resourceService: ResourceService,
  ) {}
  get base(): CrudController<Role> {
    return this;
  }

  @Get('filter')
  async filterRole(@Query() filters: any) {
    return this.service.filterRole(filters);
  }

  @Override('createOneBase')
  createOne(
    @ParsedBody() dto: any,
    @ParsedRequest() crudRequest: CrudRequest,
  ): Promise<Role> {
    return this.service.createOneRole(crudRequest, dto);
  }

  @Override('updateOneBase')
  updateOne(
    @ParsedBody() dto: any,
    @ParsedRequest() crudRequest: CrudRequest,
  ): Promise<Role> {
    return this.service.updateOneRole(crudRequest, dto);
  }

  @Override('getOneBase')
  async getOne(
    @ParsedRequest() crudRequest: CrudRequest,
    @Req() req,
  ): Promise<any> {
    const role = await this.service.getOne(crudRequest);
    if (role?.['name'].toUpperCase() === 'SUPERADMIN') {
      const resources = await this.resourceService.getManyResource(crudRequest);
      const permission = [];
      resources.forEach((item) => {
        if (item.children.length > 0) {
          item.children.forEach((child) => {
            permission.push({
              id: uuidv4(),
              delete: child.delete,
              read: child.read,
              update: child.update,
              create: child.create,
              list: child.list,
              print: child.print,
              download: child.download,
              active: child.active,
              changePass: child.changePass,
              listCalendar: child.listCalendar,
              addAppointment: child.addAppointment,
              export: child.export,
              sendMail: child.sendMail,
              checkOut: child.checkOut,
              changePaymentSingleDay: child.changePaymentSingleDay,
              changePaymentAllDays: child.changePaymentAllDays,
              editSingleDay: child.editSingleDay,
              editAllDays: child.editAllDays,
              refund: child.refund,
              resource: child,
            });
          });
        } else {
          permission.push({
            id: uuidv4(),
            delete: item.delete,
            read: item.read,
            update: item.update,
            create: item.create,
            list: item.list,
            print: item.print,
            download: item.download,
            active: item.active,
            changePass: item.changePass,
            listCalendar: item.listCalendar,
            addAppointment: item.addAppointment,
            export: item.export,
            sendMail: item.sendMail,
            checkOut: item.checkOut,
            changePaymentSingleDay: item.changePaymentSingleDay,
            changePaymentAllDays: item.changePaymentAllDays,
            editSingleDay: item.editSingleDay,
            editAllDays: item.editAllDays,
            refund: item.refund,
            resource: item,
          });
        }
      });
      return { ...role, permission };
    }
    return role;
  }

  @Override('getManyBase')
  getMany(@ParsedRequest() crudRequest: CrudRequest, @Req() req: Request) {
    return this.service.getManyRole(crudRequest);
  }



  @Override('deleteOneBase')
  deleteOne(@Param('id') id: string,) {
    return this.service.deleteOneRole(id);
  }
}
