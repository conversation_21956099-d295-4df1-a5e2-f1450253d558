import { <PERSON><PERSON><PERSON>, Column, OneToMany, ManyToOne } from 'typeorm';
import { DocEntity } from 'src/core/base/doc.entity';
import { User } from '../user/user.entity';
import { Permission } from '../permission/permission.entity';
import { Setting } from '../setting/setting.entity';
import { ApiProperty } from '@nestjs/swagger';

export const DEFAULT_ROLES = ['Admin', 'Staff'];

export enum RoleLevel {
  SUPER_ADMIN = 30,
  ADMIN = 20,
  STAFF = 10,
}
@Entity('role')
export class Role extends DocEntity {
  @Column()
  name: string;

  @Column({ nullable: true })
  code: string;

  @Column({ nullable: true, default: 1 })
  level: number;

  @ManyToOne(() => Setting, { onDelete: 'SET NULL' })
  @ApiProperty({ type: () => Setting })
  status: Setting;

  @Column({
    nullable: true,
    type: 'text',
  })
  description?: string;

  @Column()
  statusId: string;

  @OneToMany(() => User, (user) => user.role)
  user: User[];

  @OneToMany(() => Permission, (permission) => permission.role)
  permission: Permission[];
}
