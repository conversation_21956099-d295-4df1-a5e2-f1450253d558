import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Role } from './role.entity';
import { BaseCrudService } from 'src/core/base/base-crud.service';
import { CrudRequest } from 'src/core/crud/crud';
import { snakeCase } from 'lodash';
import { Permission } from '../permission/permission.entity';
import { User } from '../user/user.entity';
import { Setting } from '../setting/setting.entity';

@Injectable()
export class RoleService extends BaseCrudService<Role> {
  constructor(
    @InjectRepository(Role) repo: Repository<Role>,
    @InjectRepository(Permission)
    private permissionRepo: Repository<Permission>,
    @InjectRepository(User)
    private userRepo: Repository<User>,
    @InjectRepository(Setting)
    private settingRepo: Repository<Setting>,
  ) {
    super(repo);
  }

  async createOneRole(crudReq: CrudRequest, dto: any): Promise<Role> {
    const role = await this.createOne(crudReq, {
      name: dto.name,
      level: 1,
      code: snakeCase(dto.name),
      description: dto.description,
      status: dto.status,
    });

    //create perm
    for (const resource of dto.permission) {
      const children = resource.children || [];
      const exists = await this.permissionRepo.findOne({
        where: { resource: { id: resource.id }, role: { id: role.id } },
      });
      delete resource.children;
      const perm = {
        resource: { id: resource.id },
        role: { id: role.id },
        create: resource.create,
        list: resource.list,
        read: resource.read,
        update: resource.update,
        delete: resource.delete,
        print: resource.print,
        download: resource.download,
        active: resource.active,
        changePass: resource.changePass,
        listCalendar: resource.listCalendar,
        addAppointment: resource.addAppointment,
        export: resource.export,
        sendMail: resource.sendMail,
        checkOut: resource.checkOut,
        changePaymentSingleDay: resource.changePaymentSingleDay,
        changePaymentAllDays: resource.changePaymentAllDays,
        editSingleDay: resource.editSingleDay,
        editAllDays: resource.editAllDays,
        refund: resource.refund,
      };
      if (!exists) {
        await this.permissionRepo.insert(perm);
      } else {
        await this.permissionRepo.update({ id: exists.id }, perm);
      }

      //children
      if (children.length > 0) {
        for (const child of children) {
          const existsChild = await this.permissionRepo.findOne({
            where: { resource: { id: resource.id }, role: { id: role.id } },
          });
          const perm = {
            resource: { id: child.id },
            role: { id: role.id },
            create: child.create,
            list: child.list,
            read: child.read,
            update: child.update,
            delete: child.delete,
            print: child.print,
            download: child.download,
            active: child.active,
            changePass: child.changePass,
            listCalendar: child.listCalendar,
            addAppointment: child.addAppointment,
            export: child.export,
            sendMail: child.sendMail,
            checkOut: child.checkOut,
            changePaymentSingleDay: child.changePaymentSingleDay,
            changePaymentAllDays: child.changePaymentAllDays,
            editSingleDay: child.editSingleDay,
            editAllDays: child.editAllDays,
            refund: child.refund,
          };
          if (!existsChild) {
            await this.permissionRepo.insert(perm);
          } else {
            await this.permissionRepo.update({ id: existsChild.id }, perm);
          }
        }
      }
    }

    return role;
  }

  async updateOneRole(crudReq: CrudRequest, dto: any): Promise<Role> {
    
    const role = await this.updateOne(crudReq, {
      name: dto.name,
      level: 1,
      code: snakeCase(dto.name),
      description: dto.description,
      status: dto.status,
    });

    //update perm
    for (const resource of dto?.permission || []) {
      const children = resource.children || [];
      const exists = await this.permissionRepo.findOne({
        where: { resource: { id: resource.id }, role: { id: role.id } },
      });

      delete resource.children;
      const perm = {
        resource: { id: resource.id },
        role: { id: role.id },
        create: resource.create,
        list: resource.list,
        read: resource.read,
        update: resource.update,
        delete: resource.delete,
        print: resource.print,
        download: resource.download,
        active: resource.active,
        changePass: resource.changePass,
        listCalendar: resource.listCalendar,
        addAppointment: resource.addAppointment,
        export: resource.export,
        sendMail: resource.sendMail,
        checkOut: resource.checkOut,
        changePaymentSingleDay: resource.changePaymentSingleDay,
        changePaymentAllDays: resource.changePaymentAllDays,
        editSingleDay: resource.editSingleDay,
        editAllDays: resource.editAllDays,
        refund: resource.refund,
      };
      if (!exists) {
        await this.permissionRepo.insert(perm);
      } else {
        await this.permissionRepo.update({ id: exists.id }, perm);
      }

      //children
      if (children.length > 0) {
        for (const child of children) {
          const existsChild = await this.permissionRepo.findOne({
            where: { resource: { id: child.id }, role: { id: role.id } },
          });
          const perm = {
            resource: { id: child.id },
            role: { id: role.id },
            create: child.create,
            list: child.list,
            read: child.read,
            update: child.update,
            delete: child.delete,
            print: child.print,
            download: child.download,
            active: child.active,
            changePass: child.changePass,
            listCalendar: child.listCalendar,
            addAppointment: child.addAppointment,
            export: child.export,
            sendMail: child.sendMail,
            checkOut: child.checkOut,
            changePaymentSingleDay: child.changePaymentSingleDay,
            changePaymentAllDays: child.changePaymentAllDays,
            editSingleDay: child.editSingleDay,
            editAllDays: child.editAllDays,
            refund: child.refund,
          };
          if (!existsChild) {
            await this.permissionRepo.insert(perm);
          } else {
            await this.permissionRepo.update({ id: existsChild.id }, perm);
          }
        }
      }
    }

    return role;
  }

  async getManyRole(crudReq: CrudRequest): Promise<any> {
    const roles = await super.getMany(crudReq);
    const activeSatus = await this.settingRepo.findOne({
      where: { name: 'Active' },
    });
    const dataRes = [];
    // join vs permission
    // const roleArray = Array.isArray(roles) ? roles : roles.data;
    // for (const role of roleArray) {
    //   role.permission = await this.permissionRepo.find({
    //     where: { role: { id: role.id } },
    //     relations: ['resource'],
    //   });
    // }
    const roleArray = Array.isArray(roles) ? roles : roles.data;
    for (const role of roleArray) {
      const quantity = await this.userRepo.count({
        where: { role: { id: role.id }, status: { id: activeSatus.id } },
      });
      dataRes.push({ ...role, quantity });
    }
    return dataRes;
  }

  async deleteOneRole(roleId: string): Promise<any> {
    await this.repo.manager.transaction(async (trans) => {
      const permissionRole = await trans.findOne(Role, {
        where: {
          id: roleId,
          user: { role: { id: roleId } },
        },
        relations: ['user'],
      });
      if (permissionRole) {
        throw new BadRequestException('Can not delete role has user using!');
      }
      await trans.softDelete(Role, { id: roleId });
    });
  }

  async filterRole(filters: any):Promise<any>{
    const query = this.repo.createQueryBuilder('role');
    const status = this.settingRepo.createQueryBuilder('setting');
    if (filters.name) {
      query.andWhere('role.name ILIKE :keySearch', { name: `%${filters.keySearch}%` });
    }
    status.andWhere('setting.name IN (:...statuses)', { statuses: ['Active','Inactive'] })
    const data = await query.getMany()
    const dataStatus = await status.getMany()
    const dataRes = [];
    const roleArray = Array.isArray(data) ? data : [];
    for (const role of roleArray) {
      const findStatus = dataStatus.find(itemStatus => itemStatus.id == role.statusId)
      const quantity = await this.userRepo.count({
        where: { role: { id: role.id }, status: { id: findStatus.id } },
      });
      delete role.statusId;
      dataRes.push({ ...role,
        status:{
          id:findStatus.id,
          name: findStatus.name
        },
        quantity
      });
    }
    return dataRes;
  }
}
