# package directories
/dist
node_modules
jspm_packages
.vscode
.idea
DS_Store
# Serverless directories
.serverless

# Webpack directories
.webpack

.eslintcache

.aws

functions.yml

.env

ormconfig.json

build
entities-loader.ts
true-entities
routes.ts
swagger-routes.ts
*.example.**

*_

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
.DS_Store

# Task files
# tasks.json
# tasks/

.clinerules
.cursor
.github
.roo
.taskmaster
.trae
.windsurf
.roomodes
AGENTS.md
CLAUDE.md